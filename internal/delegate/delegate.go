// Package delegate provides the ability to make function calls between different
// domain packages when a direct import is not possible.
//
// This pattern is particularly useful in hexagonal architecture where we want to
// maintain strict boundaries between domain layers while still allowing
// controlled communication.
package delegate

import (
	"fmt"
	"reflect"
	"sync"
)

// Delegate provides a registry for inter-domain function calls
type Delegate struct {
	mu        sync.RWMutex
	functions map[string]interface{}
}

// global instance for package-level convenience functions
var globalDelegate = New()

// New creates a new Delegate instance
func New() *Delegate {
	return &Delegate{
		functions: make(map[string]interface{}),
	}
}

// Register registers a function with a given name for later invocation
func (d *Delegate) Register(name string, fn interface{}) error {
	if fn == nil {
		return fmt.Errorf("function cannot be nil")
	}

	fnType := reflect.TypeOf(fn)
	if fnType.Kind() != reflect.Func {
		return fmt.Errorf("registered value must be a function, got %v", fnType.Kind())
	}

	d.mu.Lock()
	defer d.mu.Unlock()

	d.functions[name] = fn
	return nil
}

// Call invokes a registered function by name with the provided arguments
func (d *Delegate) Call(name string, args ...interface{}) ([]interface{}, error) {
	d.mu.RLock()
	fn, exists := d.functions[name]
	d.mu.RUnlock()

	if !exists {
		return nil, fmt.Errorf("function %q not registered", name)
	}

	fnValue := reflect.ValueOf(fn)
	fnType := fnValue.Type()

	// Validate argument count
	if len(args) != fnType.NumIn() {
		return nil, fmt.Errorf("function %q expects %d arguments, got %d", 
			name, fnType.NumIn(), len(args))
	}

	// Prepare arguments
	in := make([]reflect.Value, len(args))
	for i, arg := range args {
		if arg == nil {
			// Handle nil arguments by creating zero value of expected type
			in[i] = reflect.Zero(fnType.In(i))
		} else {
			argValue := reflect.ValueOf(arg)
			expectedType := fnType.In(i)
			
			// Check if types are compatible
			if !argValue.Type().AssignableTo(expectedType) {
				return nil, fmt.Errorf("argument %d: cannot assign %v to %v", 
					i, argValue.Type(), expectedType)
			}
			
			in[i] = argValue
		}
	}

	// Call function
	out := fnValue.Call(in)

	// Convert results to interface slice
	results := make([]interface{}, len(out))
	for i, v := range out {
		results[i] = v.Interface()
	}

	return results, nil
}

// CallWithResult is a convenience method that calls a function and expects exactly one result
func (d *Delegate) CallWithResult(name string, args ...interface{}) (interface{}, error) {
	results, err := d.Call(name, args...)
	if err != nil {
		return nil, err
	}

	if len(results) != 1 {
		return nil, fmt.Errorf("expected exactly 1 result, got %d", len(results))
	}

	return results[0], nil
}

// CallWithError is a convenience method for functions that return (result, error)
func (d *Delegate) CallWithError(name string, args ...interface{}) (interface{}, error) {
	results, err := d.Call(name, args...)
	if err != nil {
		return nil, err
	}

	if len(results) != 2 {
		return nil, fmt.Errorf("expected exactly 2 results (value, error), got %d", len(results))
	}

	// Check if last result is an error
	if results[1] != nil {
		if err, ok := results[1].(error); ok {
			return results[0], err
		}
	}

	return results[0], nil
}

// Unregister removes a function from the registry
func (d *Delegate) Unregister(name string) {
	d.mu.Lock()
	defer d.mu.Unlock()
	delete(d.functions, name)
}

// IsRegistered checks if a function is registered
func (d *Delegate) IsRegistered(name string) bool {
	d.mu.RLock()
	defer d.mu.RUnlock()
	_, exists := d.functions[name]
	return exists
}

// ListRegistered returns a list of all registered function names
func (d *Delegate) ListRegistered() []string {
	d.mu.RLock()
	defer d.mu.RUnlock()

	names := make([]string, 0, len(d.functions))
	for name := range d.functions {
		names = append(names, name)
	}
	return names
}

// Package-level convenience functions using the global delegate

// Register registers a function with the global delegate
func Register(name string, fn interface{}) error {
	return globalDelegate.Register(name, fn)
}

// Call invokes a registered function using the global delegate
func Call(name string, args ...interface{}) ([]interface{}, error) {
	return globalDelegate.Call(name, args...)
}

// CallWithResult calls a function and expects exactly one result using the global delegate
func CallWithResult(name string, args ...interface{}) (interface{}, error) {
	return globalDelegate.CallWithResult(name, args...)
}

// CallWithError calls a function expecting (result, error) using the global delegate
func CallWithError(name string, args ...interface{}) (interface{}, error) {
	return globalDelegate.CallWithError(name, args...)
}

// Unregister removes a function from the global delegate
func Unregister(name string) {
	globalDelegate.Unregister(name)
}

// IsRegistered checks if a function is registered with the global delegate
func IsRegistered(name string) bool {
	return globalDelegate.IsRegistered(name)
}

// ListRegistered returns a list of all registered function names from the global delegate
func ListRegistered() []string {
	return globalDelegate.ListRegistered()
}