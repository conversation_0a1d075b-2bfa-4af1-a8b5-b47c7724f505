// Package delegate provides the ability to make function calls between
// different domain packages when an import is not possible.
package delegate

import (
	"context"

	"github.com/rs/zerolog"
)

// These types are just for documentation so we know what keys go
// where in the map.
type (
	domain string
	action string
)

// Delegate manages the set of functions to be called by domain
// packages when an import is not possible.
type Delegate struct {
	log   zerolog.Logger
	funcs map[domain]map[action][]Func
}

// New constructs a delegate for indirect api access.
func New(log zerolog.Logger) *Delegate {
	return &Delegate{
		log:   log,
		funcs: make(map[domain]map[action][]Func),
	}
}

// Register adds a function to be called for a specified domain and action.
func (d *Delegate) Register(domainType string, actionType string, fn Func) {
	aMap, ok := d.funcs[domain(domainType)]
	if !ok {
		aMap = make(map[action][]Func)
		d.funcs[domain(domainType)] = aMap
	}

	funcs := aMap[action(actionType)]
	funcs = append(funcs, fn)
	aMap[action(actionType)] = funcs

	d.log.Info().
		Str("domain", domainType).
		Str("action", actionType).
		Msg("delegate function registered")
}

// Call executes all functions registered for the specified domain and
// action. These functions are executed synchronously on the G making the call.
func (d *Delegate) Call(ctx context.Context, data Data) error {
	d.log.Info().
		Str("domain", data.Domain).
		Str("action", data.Action).
		Bytes("params", data.RawParams).
		Msg("delegate call started")

	defer d.log.Info().
		Str("domain", data.Domain).
		Str("action", data.Action).
		Msg("delegate call completed")

	if dMap, ok := d.funcs[domain(data.Domain)]; ok {
		if funcs, ok := dMap[action(data.Action)]; ok {
			for _, fn := range funcs {
				d.log.Debug().
					Str("domain", data.Domain).
					Str("action", data.Action).
					Msg("executing delegate function")

				if err := fn(ctx, data); err != nil {
					d.log.Error().
						Err(err).
						Str("domain", data.Domain).
						Str("action", data.Action).
						Msg("delegate function failed")
					// Continue executing other functions even if one fails
				}
			}
		}
	}

	return nil
}

// CallSync executes all functions and returns the first error encountered
func (d *Delegate) CallSync(ctx context.Context, data Data) error {
	d.log.Info().
		Str("domain", data.Domain).
		Str("action", data.Action).
		Bytes("params", data.RawParams).
		Msg("delegate sync call started")

	if dMap, ok := d.funcs[domain(data.Domain)]; ok {
		if funcs, ok := dMap[action(data.Action)]; ok {
			for _, fn := range funcs {
				if err := fn(ctx, data); err != nil {
					d.log.Error().
						Err(err).
						Str("domain", data.Domain).
						Str("action", data.Action).
						Msg("delegate sync function failed")
					return err
				}
			}
		}
	}

	d.log.Info().
		Str("domain", data.Domain).
		Str("action", data.Action).
		Msg("delegate sync call completed")

	return nil
}

// Unregister removes all functions for a specific domain and action
func (d *Delegate) Unregister(domainType string, actionType string) {
	if dMap, ok := d.funcs[domain(domainType)]; ok {
		delete(dMap, action(actionType))
		d.log.Info().
			Str("domain", domainType).
			Str("action", actionType).
			Msg("delegate functions unregistered")
	}
}

// IsRegistered checks if any functions are registered for domain and action
func (d *Delegate) IsRegistered(domainType string, actionType string) bool {
	if dMap, ok := d.funcs[domain(domainType)]; ok {
		if funcs, ok := dMap[action(actionType)]; ok {
			return len(funcs) > 0
		}
	}
	return false
}

// ListRegistered returns all registered domain/action combinations
func (d *Delegate) ListRegistered() map[string][]string {
	result := make(map[string][]string)
	
	for domain, actions := range d.funcs {
		domainStr := string(domain)
		for action := range actions {
			result[domainStr] = append(result[domainStr], string(action))
		}
	}
	
	return result
}