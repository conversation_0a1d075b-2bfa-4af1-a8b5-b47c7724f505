package delegate

import (
	"context"
	"os"
	"testing"

	"github.com/rs/zerolog"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// This test demonstrates how the delegate pattern prevents import cycles
// by allowing packages to communicate without direct imports

// Simulate package A (e.g., HTTP handlers)
type packageA struct {
	delegate *Delegate
	name     string
}

func newPackageA(delegate *Delegate) *packageA {
	return &packageA{
		delegate: delegate,
		name:     "HTTP Handlers Package",
	}
}

// packageA needs to call packageB and packageC without importing them
func (a *packageA) processRequest(ctx context.Context, licenseKey string) error {
	// Step 1: Call license validation (packageB) via delegate
	data, err := NewData(LicenseDomain, LicenseValidateAction, map[string]interface{}{
		"license_key":         licenseKey,
		"machine_fingerprint": "test-fp",
	})
	if err != nil {
		return err
	}

	err = a.delegate.CallSync(ctx, data)
	if err != nil {
		return err
	}

	// Step 2: Call audit logging (packageC) via delegate
	auditData, err := NewData(AuditDomain, AuditLogAction, map[string]interface{}{
		"event_type":  "license_validation_request",
		"entity_type": "license",
		"entity_id":   licenseKey,
		"source":      a.name,
	})
	if err != nil {
		return err
	}

	return a.delegate.Call(ctx, auditData)
}

// Simulate package B (e.g., License domain service)
type packageB struct {
	delegate *Delegate
	name     string
	results  map[string]bool
}

func newPackageB(delegate *Delegate) *packageB {
	return &packageB{
		delegate: delegate,
		name:     "License Domain Service",
		results:  make(map[string]bool),
	}
}

// packageB needs to call packageC (audit) and packageD (cache) without importing them
func (b *packageB) validateLicense(ctx context.Context, data Data) error {
	var params struct {
		LicenseKey         string `json:"license_key"`
		MachineFingerprint string `json:"machine_fingerprint"`
	}

	if err := data.UnmarshalParams(&params); err != nil {
		return err
	}

	// Simulate license validation logic
	isValid := len(params.LicenseKey) > 10 // Simple validation rule
	b.results[params.LicenseKey] = isValid

	// Step 1: Call cache (packageD) to store result via delegate
	cacheData, err := NewData(CacheDomain, CacheSetAction, map[string]interface{}{
		"key":   "license:" + params.LicenseKey,
		"value": isValid,
		"ttl":   3600,
	})
	if err != nil {
		return err
	}

	err = b.delegate.Call(ctx, cacheData)
	if err != nil {
		return err
	}

	// Step 2: Call audit (packageC) to log validation via delegate
	auditData, err := NewData(AuditDomain, AuditLogAction, map[string]interface{}{
		"event_type":  "license_validated",
		"entity_type": "license",
		"entity_id":   params.LicenseKey,
		"details": map[string]interface{}{
			"valid":  isValid,
			"source": b.name,
		},
	})
	if err != nil {
		return err
	}

	return b.delegate.Call(ctx, auditData)
}

// Simulate package C (e.g., Audit service)
type packageC struct {
	delegate *Delegate
	name     string
	events   []map[string]interface{}
}

func newPackageC(delegate *Delegate) *packageC {
	return &packageC{
		delegate: delegate,
		name:     "Audit Service",
		events:   make([]map[string]interface{}, 0),
	}
}

// packageC needs to call packageD (metrics) without importing it
func (c *packageC) logEvent(ctx context.Context, data Data) error {
	var params map[string]interface{}
	if err := data.UnmarshalParams(&params); err != nil {
		return err
	}

	// Store audit event
	c.events = append(c.events, params)

	// Call metrics (packageD) to increment audit counter via delegate
	metricsData, err := NewData(MetricsDomain, MetricsRecordAction, map[string]interface{}{
		"metric_name": "audit_events_total",
		"value":       1,
		"tags": map[string]string{
			"event_type": params["event_type"].(string),
			"source":     c.name,
		},
	})
	if err != nil {
		return err
	}

	return c.delegate.Call(ctx, metricsData)
}

// Simulate package D (e.g., Cache/Metrics infrastructure)
type packageD struct {
	name    string
	cache   map[string]interface{}
	metrics map[string]float64
}

func newPackageD() *packageD {
	return &packageD{
		name:    "Infrastructure Service",
		cache:   make(map[string]interface{}),
		metrics: make(map[string]float64),
	}
}

func (d *packageD) handleCache(ctx context.Context, data Data) error {
	var params struct {
		Key   string      `json:"key"`
		Value interface{} `json:"value"`
		TTL   int         `json:"ttl,omitempty"`
	}

	if err := data.UnmarshalParams(&params); err != nil {
		return err
	}

	// Store in cache
	d.cache[params.Key] = params.Value
	return nil
}

func (d *packageD) handleMetrics(ctx context.Context, data Data) error {
	var params struct {
		MetricName string            `json:"metric_name"`
		Value      float64           `json:"value"`
		Tags       map[string]string `json:"tags,omitempty"`
	}

	if err := data.UnmarshalParams(&params); err != nil {
		return err
	}

	// Record metric
	if existing, ok := d.metrics[params.MetricName]; ok {
		d.metrics[params.MetricName] = existing + params.Value
	} else {
		d.metrics[params.MetricName] = params.Value
	}
	return nil
}

func TestCyclePrevention(t *testing.T) {
	// This test demonstrates the communication flow:
	// packageA (HTTP) -> packageB (License) -> packageC (Audit) -> packageD (Infra)
	//                 -> packageD (Cache)    -> packageD (Metrics)
	// 
	// Without delegate pattern, this would create import cycles:
	// packageA imports packageB
	// packageB imports packageC and packageD
	// packageC imports packageD
	// If packageD needed to import packageA, it would create a cycle
	//
	// With delegate pattern, no package imports any other domain package

	logger := zerolog.New(os.Stdout).With().Timestamp().Logger()
	delegate := New(logger)

	// Create mock packages
	pkgA := newPackageA(delegate)
	pkgB := newPackageB(delegate)
	pkgC := newPackageC(delegate)
	pkgD := newPackageD()

	// Register delegate functions (this would typically happen in main.go or service coordinator)
	delegate.Register(LicenseDomain, LicenseValidateAction, pkgB.validateLicense)
	delegate.Register(AuditDomain, AuditLogAction, pkgC.logEvent)
	delegate.Register(CacheDomain, CacheSetAction, pkgD.handleCache)
	delegate.Register(MetricsDomain, MetricsRecordAction, pkgD.handleMetrics)

	t.Run("packages communicate without import cycles", func(t *testing.T) {
		ctx := context.Background()
		licenseKey := "CYCLE-TEST-12345-ABCDE"

		// Package A processes a request, triggering the whole flow
		err := pkgA.processRequest(ctx, licenseKey)
		require.NoError(t, err)

		// Verify the flow worked through all packages:

		// 1. Package B should have validated the license
		assert.True(t, pkgB.results[licenseKey], "License should be validated by package B")

		// 2. Package D should have cached the result
		cacheKey := "license:" + licenseKey
		assert.Contains(t, pkgD.cache, cacheKey, "Result should be cached by package D")
		assert.True(t, pkgD.cache[cacheKey].(bool), "Cached result should be true")

		// 3. Package C should have logged audit events
		assert.Len(t, pkgC.events, 2, "Should have 2 audit events")
		
		// Events can come in any order due to async nature, so check both exist
		eventTypes := make(map[string]bool)
		for _, event := range pkgC.events {
			eventTypes[event["event_type"].(string)] = true
			assert.Equal(t, licenseKey, event["entity_id"], "Entity ID should match")
		}
		
		assert.True(t, eventTypes["license_validation_request"], "Should have license_validation_request event")
		assert.True(t, eventTypes["license_validated"], "Should have license_validated event")

		// 4. Package D should have recorded metrics
		assert.Equal(t, float64(2), pkgD.metrics["audit_events_total"], "Should have recorded 2 audit events in metrics")
	})

	t.Run("verify no direct package dependencies", func(t *testing.T) {
		// This test documents that packages only depend on the delegate interface
		// and not on each other directly

		// Package A only knows about delegate interface
		assert.IsType(t, &Delegate{}, pkgA.delegate)

		// Package B only knows about delegate interface  
		assert.IsType(t, &Delegate{}, pkgB.delegate)

		// Package C only knows about delegate interface
		assert.IsType(t, &Delegate{}, pkgC.delegate)

		// Package D doesn't even need to know about delegate
		// (it just implements handler functions)
		assert.NotNil(t, pkgD.cache)
		assert.NotNil(t, pkgD.metrics)

		// All communication happens through the delegate
		assert.True(t, delegate.IsRegistered(LicenseDomain, LicenseValidateAction))
		assert.True(t, delegate.IsRegistered(AuditDomain, AuditLogAction))
		assert.True(t, delegate.IsRegistered(CacheDomain, CacheSetAction))
		assert.True(t, delegate.IsRegistered(MetricsDomain, MetricsRecordAction))
	})
}

func TestArchitecturalBoundaries(t *testing.T) {
	logger := zerolog.New(os.Stdout).With().Timestamp().Logger()
	delegate := New(logger)

	t.Run("demonstrate hexagonal architecture boundaries", func(t *testing.T) {
		// In hexagonal architecture:
		// - Core domain (center) should not depend on infrastructure
		// - Infrastructure (adapters) can depend on domain interfaces
		// - Delegate pattern allows loose coupling between all layers

		// Domain layer function (should not import infrastructure)
		domainFunction := func(ctx context.Context, data Data) error {
			// Domain logic can call infrastructure via delegate
			infraData, _ := NewData("infrastructure", "persist", map[string]interface{}{
				"entity": "domain_entity",
				"action": "save",
			})
			return delegate.Call(ctx, infraData)
		}

		// Infrastructure layer function
		infraFunction := func(ctx context.Context, data Data) error {
			// Infrastructure can implement domain requirements
			var params map[string]interface{}
			data.UnmarshalParams(&params)
			// Simulate persistence
			return nil
		}

		// Register functions
		delegate.Register("domain", "business_logic", domainFunction)
		delegate.Register("infrastructure", "persist", infraFunction)

		// Execute domain logic
		data, _ := NewData("domain", "business_logic", nil)
		err := delegate.Call(context.Background(), data)
		require.NoError(t, err)

		// Verify architectural boundaries are maintained
		assert.True(t, delegate.IsRegistered("domain", "business_logic"))
		assert.True(t, delegate.IsRegistered("infrastructure", "persist"))
	})
}

func TestDelegateAsAntiCorruptionLayer(t *testing.T) {
	logger := zerolog.New(os.Stdout).With().Timestamp().Logger()
	delegate := New(logger)

	t.Run("delegate acts as anti-corruption layer", func(t *testing.T) {
		// Simulate legacy system with different data format
		legacySystemData := map[string]interface{}{
			"licenseId":  "legacy-format-123",
			"isActive":   "true", // string instead of bool
			"expiryDate": "2025-12-31", // string date
		}

		// Adapter function that translates between formats
		adaptLegacyData := func(ctx context.Context, data Data) error {
			var params map[string]interface{}
			if err := data.UnmarshalParams(&params); err != nil {
				return err
			}

			// Transform legacy format to modern format
			modernData := map[string]interface{}{
				"license_key": params["licenseId"],
				"active":      params["isActive"] == "true",
				"expires_at":  params["expiryDate"].(string) + "T23:59:59Z",
			}

			// Call modern system via delegate
			modernSystemData, _ := NewData("modern", "process", modernData)
			return delegate.Call(ctx, modernSystemData)
		}

		// Modern system function
		processModernData := func(ctx context.Context, data Data) error {
			var params struct {
				LicenseKey string `json:"license_key"`
				Active     bool   `json:"active"`
				ExpiresAt  string `json:"expires_at"`
			}
			return data.UnmarshalParams(&params)
		}

		// Register functions
		delegate.Register("legacy", "adapt", adaptLegacyData)
		delegate.Register("modern", "process", processModernData)

		// Process legacy data
		data, _ := NewData("legacy", "adapt", legacySystemData)
		err := delegate.Call(context.Background(), data)
		require.NoError(t, err)

		// Verify anti-corruption layer works
		assert.True(t, delegate.IsRegistered("legacy", "adapt"))
		assert.True(t, delegate.IsRegistered("modern", "process"))
	})
}