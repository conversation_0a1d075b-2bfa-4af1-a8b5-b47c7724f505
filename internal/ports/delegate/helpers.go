package delegate

import (
	"context"
	"time"

	"github.com/google/uuid"
)

// Helper functions for common delegate operations

// CallLicenseValidation calls license validation delegate
func CallLicenseValidation(ctx context.Context, delegate *Delegate, licenseKey string, machineFingerprint string) error {
	params := map[string]interface{}{
		"license_key":         licenseKey,
		"machine_fingerprint": machineFingerprint,
		"timestamp":          time.Now(),
	}

	data, err := NewData(LicenseDomain, LicenseValidateAction, params)
	if err != nil {
		return err
	}

	return delegate.Call(ctx, data)
}

// CallMachineRegistration calls machine registration delegate
func CallMachineRegistration(ctx context.Context, delegate *Delegate, machineID uuid.UUID, fingerprint string) error {
	params := map[string]interface{}{
		"machine_id":   machineID.String(),
		"fingerprint":  fingerprint,
		"timestamp":    time.Now(),
	}

	data, err := NewData(MachineDomain, MachineRegisterAction, params)
	if err != nil {
		return err
	}

	return delegate.Call(ctx, data)
}

// CallCacheOperation calls cache operation delegate
func CallCacheOperation(ctx context.Context, delegate *Delegate, action, key string, value interface{}) error {
	params := map[string]interface{}{
		"key":       key,
		"value":     value,
		"timestamp": time.Now(),
	}

	data, err := NewData(CacheDomain, action, params)
	if err != nil {
		return err
	}

	return delegate.Call(ctx, data)
}

// CallAuditLog calls audit logging delegate
func CallAuditLog(ctx context.Context, delegate *Delegate, eventType, entityType, entityID string, userID *uuid.UUID, details map[string]interface{}) error {
	params := map[string]interface{}{
		"event_type":  eventType,
		"entity_type": entityType,
		"entity_id":   entityID,
		"details":     details,
		"timestamp":   time.Now(),
	}

	if userID != nil {
		params["user_id"] = userID.String()
	}

	data, err := NewData(AuditDomain, AuditLogAction, params)
	if err != nil {
		return err
	}

	return delegate.Call(ctx, data)
}

// CallMetricsRecord calls metrics recording delegate
func CallMetricsRecord(ctx context.Context, delegate *Delegate, metricName string, value float64, tags map[string]string) error {
	params := map[string]interface{}{
		"metric_name": metricName,
		"value":       value,
		"tags":        tags,
		"timestamp":   time.Now(),
	}

	data, err := NewData(MetricsDomain, MetricsRecordAction, params)
	if err != nil {
		return err
	}

	return delegate.Call(ctx, data)
}

// CallEventPublish calls event publishing delegate
func CallEventPublish(ctx context.Context, delegate *Delegate, eventType string, payload interface{}) error {
	params := map[string]interface{}{
		"event_type": eventType,
		"payload":    payload,
		"timestamp":  time.Now(),
	}

	data, err := NewData(EventDomain, EventPublishAction, params)
	if err != nil {
		return err
	}

	return delegate.Call(ctx, data)
}

// CallUserAuthentication calls user authentication delegate
func CallUserAuthentication(ctx context.Context, delegate *Delegate, userID uuid.UUID, token string) error {
	params := map[string]interface{}{
		"user_id":   userID.String(),
		"token":     token,
		"timestamp": time.Now(),
	}

	data, err := NewData(UserDomain, UserAuthenticateAction, params)
	if err != nil {
		return err
	}

	return delegate.CallSync(ctx, data) // Use sync for auth
}

// CallPolicyValidation calls policy validation delegate
func CallPolicyValidation(ctx context.Context, delegate *Delegate, policyID uuid.UUID, contextData map[string]interface{}) error {
	params := map[string]interface{}{
		"policy_id": policyID.String(),
		"context":   contextData,
		"timestamp": time.Now(),
	}

	data, err := NewData(PolicyDomain, PolicyValidateAction, params)
	if err != nil {
		return err
	}

	return delegate.CallSync(ctx, data) // Use sync for policy validation
}