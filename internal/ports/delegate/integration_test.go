package delegate

import (
	"context"
	"os"
	"sync"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/rs/zerolog"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// Mock services to simulate real domain services without import cycles
type mockLicenseService struct {
	validations map[string]bool
	mu          sync.RWMutex
}

func (m *mockLicenseService) validate(ctx context.Context, data Data) error {
	var params struct {
		LicenseKey         string `json:"license_key"`
		MachineFingerprint string `json:"machine_fingerprint"`
	}
	
	if err := data.UnmarshalParams(&params); err != nil {
		return err
	}

	m.mu.Lock()
	defer m.mu.Unlock()
	
	// Simulate validation logic
	m.validations[params.LicenseKey] = true
	return nil
}

type mockCacheService struct {
	cache map[string]interface{}
	mu    sync.RWMutex
}

func (m *mockCacheService) get(ctx context.Context, data Data) error {
	var params struct {
		Key string `json:"key"`
	}
	
	if err := data.UnmarshalParams(&params); err != nil {
		return err
	}

	m.mu.RLock()
	defer m.mu.RUnlock()
	
	// Simulate cache get
	_ = m.cache[params.Key]
	return nil
}

func (m *mockCacheService) set(ctx context.Context, data Data) error {
	var params struct {
		Key   string      `json:"key"`
		Value interface{} `json:"value"`
		TTL   int         `json:"ttl,omitempty"`
	}
	
	if err := data.UnmarshalParams(&params); err != nil {
		return err
	}

	m.mu.Lock()
	defer m.mu.Unlock()
	
	// Simulate cache set
	m.cache[params.Key] = params.Value
	return nil
}

func (m *mockCacheService) delete(ctx context.Context, data Data) error {
	var params struct {
		Key string `json:"key"`
	}
	
	if err := data.UnmarshalParams(&params); err != nil {
		return err
	}

	m.mu.Lock()
	defer m.mu.Unlock()
	
	// Simulate cache delete
	delete(m.cache, params.Key)
	return nil
}

type mockAuditService struct {
	events []map[string]interface{}
	mu     sync.RWMutex
}

func (m *mockAuditService) log(ctx context.Context, data Data) error {
	var params map[string]interface{}
	
	if err := data.UnmarshalParams(&params); err != nil {
		return err
	}

	m.mu.Lock()
	defer m.mu.Unlock()
	
	// Simulate audit logging
	m.events = append(m.events, params)
	return nil
}

type mockMetricsService struct {
	metrics map[string]float64
	mu      sync.RWMutex
}

func (m *mockMetricsService) record(ctx context.Context, data Data) error {
	var params struct {
		MetricName string            `json:"metric_name"`
		Value      float64           `json:"value"`
		Tags       map[string]string `json:"tags,omitempty"`
	}
	
	if err := data.UnmarshalParams(&params); err != nil {
		return err
	}

	m.mu.Lock()
	defer m.mu.Unlock()
	
	// Simulate metrics recording
	if existing, ok := m.metrics[params.MetricName]; ok {
		m.metrics[params.MetricName] = existing + params.Value
	} else {
		m.metrics[params.MetricName] = params.Value
	}
	return nil
}

func setupIntegrationTest(t *testing.T) (*Delegate, *mockLicenseService, *mockCacheService, *mockAuditService, *mockMetricsService) {
	logger := zerolog.New(os.Stdout).With().Timestamp().Logger()
	delegate := New(logger)

	// Create mock services
	licenseService := &mockLicenseService{
		validations: make(map[string]bool),
	}
	cacheService := &mockCacheService{
		cache: make(map[string]interface{}),
	}
	auditService := &mockAuditService{
		events: make([]map[string]interface{}, 0),
	}
	metricsService := &mockMetricsService{
		metrics: make(map[string]float64),
	}

	// Register delegate functions
	delegate.Register(LicenseDomain, LicenseValidateAction, licenseService.validate)
	delegate.Register(CacheDomain, CacheGetAction, cacheService.get)
	delegate.Register(CacheDomain, CacheSetAction, cacheService.set)
	delegate.Register(CacheDomain, CacheDeleteAction, cacheService.delete)
	delegate.Register(AuditDomain, AuditLogAction, auditService.log)
	delegate.Register(MetricsDomain, MetricsRecordAction, metricsService.record)

	return delegate, licenseService, cacheService, auditService, metricsService
}

func TestLicenseValidationFlow(t *testing.T) {
	delegate, licenseService, cacheService, auditService, metricsService := setupIntegrationTest(t)

	ctx := context.Background()
	licenseKey := "LIC-12345-ABCDE-67890-FGHIJ"
	machineFingerprint := "fp-mac-12345678"

	t.Run("complete license validation flow", func(t *testing.T) {
		// Step 1: Check cache first
		err := CallCacheOperation(ctx, delegate, CacheGetAction, "license:"+licenseKey, nil)
		require.NoError(t, err)

		// Step 2: Validate license (cache miss simulation)
		err = CallLicenseValidation(ctx, delegate, licenseKey, machineFingerprint)
		require.NoError(t, err)

		// Verify license was validated
		licenseService.mu.RLock()
		assert.True(t, licenseService.validations[licenseKey])
		licenseService.mu.RUnlock()

		// Step 3: Cache the result
		cacheData := map[string]interface{}{
			"valid":      true,
			"expires_at": time.Now().Add(24 * time.Hour),
		}
		err = CallCacheOperation(ctx, delegate, CacheSetAction, "license:"+licenseKey, cacheData)
		require.NoError(t, err)

		// Verify cache was set
		cacheService.mu.RLock()
		assert.Contains(t, cacheService.cache, "license:"+licenseKey)
		cacheService.mu.RUnlock()

		// Step 4: Log audit event
		err = CallAuditLog(ctx, delegate, "license_validation", "license", licenseKey, nil, map[string]interface{}{
			"machine_fingerprint": machineFingerprint,
			"result":             "valid",
		})
		require.NoError(t, err)

		// Verify audit was logged
		auditService.mu.RLock()
		assert.Len(t, auditService.events, 1)
		assert.Equal(t, "license_validation", auditService.events[0]["event_type"])
		auditService.mu.RUnlock()

		// Step 5: Record metrics
		err = CallMetricsRecord(ctx, delegate, "license_validations_total", 1, map[string]string{
			"result": "valid",
		})
		require.NoError(t, err)

		// Verify metrics were recorded
		metricsService.mu.RLock()
		assert.Equal(t, float64(1), metricsService.metrics["license_validations_total"])
		metricsService.mu.RUnlock()
	})
}

func TestMachineRegistrationFlow(t *testing.T) {
	delegate, _, cacheService, auditService, metricsService := setupIntegrationTest(t)

	ctx := context.Background()
	machineID := uuid.New()
	fingerprint := "fp-new-machine-123"
	licenseKey := "LIC-98765-FGHIJ-12345-ABCDE"

	t.Run("complete machine registration flow", func(t *testing.T) {
		// Step 1: Register machine
		err := CallMachineRegistration(ctx, delegate, machineID, fingerprint)
		require.NoError(t, err)

		// Step 2: Invalidate related license cache
		err = CallCacheOperation(ctx, delegate, CacheDeleteAction, "license:"+licenseKey, nil)
		require.NoError(t, err)

		// Verify cache was cleared
		cacheService.mu.RLock()
		assert.NotContains(t, cacheService.cache, "license:"+licenseKey)
		cacheService.mu.RUnlock()

		// Step 3: Log machine registration audit
		err = CallAuditLog(ctx, delegate, "machine_registered", "machine", machineID.String(), nil, map[string]interface{}{
			"fingerprint":  fingerprint,
			"license_key":  licenseKey,
		})
		require.NoError(t, err)

		// Verify audit was logged
		auditService.mu.RLock()
		assert.Len(t, auditService.events, 1)
		assert.Equal(t, "machine_registered", auditService.events[0]["event_type"])
		auditService.mu.RUnlock()

		// Step 4: Record machine count metrics
		err = CallMetricsRecord(ctx, delegate, "machines_registered_total", 1, map[string]string{
			"license_key": licenseKey,
		})
		require.NoError(t, err)

		// Verify metrics were recorded
		metricsService.mu.RLock()
		assert.Equal(t, float64(1), metricsService.metrics["machines_registered_total"])
		metricsService.mu.RUnlock()
	})
}

func TestConcurrentDelegateOperations(t *testing.T) {
	delegate, _, _, auditService, metricsService := setupIntegrationTest(t)

	ctx := context.Background()
	numOperations := 10

	t.Run("concurrent delegate calls", func(t *testing.T) {
		var wg sync.WaitGroup
		wg.Add(numOperations * 2) // audit + metrics operations

		// Concurrent audit logging
		for i := 0; i < numOperations; i++ {
			go func(index int) {
				defer wg.Done()
				err := CallAuditLog(ctx, delegate, "concurrent_test", "test", 
					uuid.New().String(), nil, map[string]interface{}{
						"index": index,
					})
				assert.NoError(t, err)
			}(i)
		}

		// Concurrent metrics recording
		for i := 0; i < numOperations; i++ {
			go func(index int) {
				defer wg.Done()
				err := CallMetricsRecord(ctx, delegate, "concurrent_operations", 1, 
					map[string]string{"operation": "test"})
				assert.NoError(t, err)
			}(i)
		}

		wg.Wait()

		// Verify all operations completed
		auditService.mu.RLock()
		assert.Len(t, auditService.events, numOperations)
		auditService.mu.RUnlock()

		metricsService.mu.RLock()
		assert.Equal(t, float64(numOperations), metricsService.metrics["concurrent_operations"])
		metricsService.mu.RUnlock()
	})
}

func TestErrorPropagation(t *testing.T) {
	logger := zerolog.New(os.Stdout).With().Timestamp().Logger()
	delegate := New(logger)

	t.Run("error handling in delegate functions", func(t *testing.T) {
		errorFunc := func(ctx context.Context, data Data) error {
			return assert.AnError
		}

		delegate.Register("error_test", "fail", errorFunc)

		data, err := NewData("error_test", "fail", nil)
		require.NoError(t, err)

		// Call should not propagate error (fire and forget)
		err = delegate.Call(context.Background(), data)
		require.NoError(t, err)

		// CallSync should propagate error
		err = delegate.CallSync(context.Background(), data)
		require.Error(t, err)
		assert.Equal(t, assert.AnError, err)
	})
}

func TestHelperFunctions(t *testing.T) {
	delegate, licenseService, cacheService, auditService, metricsService := setupIntegrationTest(t)

	ctx := context.Background()

	t.Run("helper functions work correctly", func(t *testing.T) {
		// Test license validation helper
		err := CallLicenseValidation(ctx, delegate, "TEST-LICENSE", "TEST-FINGERPRINT")
		require.NoError(t, err)

		licenseService.mu.RLock()
		assert.True(t, licenseService.validations["TEST-LICENSE"])
		licenseService.mu.RUnlock()

		// Test cache operation helper
		err = CallCacheOperation(ctx, delegate, CacheSetAction, "test-key", "test-value")
		require.NoError(t, err)

		cacheService.mu.RLock()
		assert.Equal(t, "test-value", cacheService.cache["test-key"])
		cacheService.mu.RUnlock()

		// Test audit log helper
		userID := uuid.New()
		err = CallAuditLog(ctx, delegate, "test_event", "test_entity", "test_id", &userID, map[string]interface{}{"key": "value"})
		require.NoError(t, err)

		auditService.mu.RLock()
		assert.Len(t, auditService.events, 1)
		assert.Equal(t, "test_event", auditService.events[0]["event_type"])
		auditService.mu.RUnlock()

		// Test metrics helper
		err = CallMetricsRecord(ctx, delegate, "test_metric", 42.5, map[string]string{"tag": "test"})
		require.NoError(t, err)

		metricsService.mu.RLock()
		assert.Equal(t, 42.5, metricsService.metrics["test_metric"])
		metricsService.mu.RUnlock()
	})
}

func TestDelegatePerformance(t *testing.T) {
	delegate, _, _, _, _ := setupIntegrationTest(t)

	t.Run("performance under load", func(t *testing.T) {
		numCalls := 1000
		start := time.Now()

		for i := 0; i < numCalls; i++ {
			data, _ := NewData(LicenseDomain, LicenseValidateAction, map[string]interface{}{
				"license_key":         "PERF-TEST",
				"machine_fingerprint": "perf-test-fp",
			})
			err := delegate.Call(context.Background(), data)
			require.NoError(t, err)
		}

		duration := time.Since(start)
		t.Logf("Processed %d delegate calls in %v (%.2f calls/sec)", 
			numCalls, duration, float64(numCalls)/duration.Seconds())

		// Performance should be reasonable (> 1000 calls/sec)
		assert.Less(t, duration.Seconds(), 1.0, "Should process 1000 calls in less than 1 second")
	})
}