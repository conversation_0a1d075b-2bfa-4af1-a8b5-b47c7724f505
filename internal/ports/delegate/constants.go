package delegate

// Domain constants for GoKeys system
const (
	// Core business domains
	LicenseDomain      = "license"
	MachineDomain      = "machine"
	OrganizationDomain = "organization"
	UserDomain         = "user"
	PolicyDomain       = "policy"
	ProductDomain      = "product"

	// Infrastructure domains
	CacheDomain   = "cache"
	AuditDomain   = "audit"
	EventDomain   = "event"
	MetricsDomain = "metrics"
	CryptoDomain  = "crypto"
)

// Action constants for License domain
const (
	LicenseValidateAction     = "validate"
	LicenseCheckExpiryAction  = "check_expiry"
	LicenseGetInfoAction      = "get_info"
	LicenseInvalidateAction   = "invalidate"
	LicenseUpdateStatusAction = "update_status"
	LicenseCountUsageAction   = "count_usage"
)

// Action constants for Machine domain
const (
	MachineRegisterAction         = "register"
	MachineUpdateAction           = "update"
	MachineGetByFingerprintAction = "get_by_fingerprint"
	MachineCheckLimitAction       = "check_limit"
	MachineUpdateLastSeenAction   = "update_last_seen"
	MachineDeactivateAction       = "deactivate"
)

// Action constants for Organization domain
const (
	OrganizationGetAction         = "get"
	OrganizationValidateAction    = "validate"
	OrganizationGetSettingsAction = "get_settings"
	OrganizationUpdateAction      = "update"
	OrganizationSuspendAction     = "suspend"
)

// Action constants for User domain
const (
	UserGetAction            = "get"
	UserValidateTokenAction  = "validate_token"
	UserGetPermissionsAction = "get_permissions"
	UserAuthenticateAction   = "authenticate"
	UserAuthorizeAction      = "authorize"
)

// Action constants for Policy domain
const (
	PolicyValidateAction    = "validate"
	PolicyGetRulesAction    = "get_rules"
	PolicyCheckLimitsAction = "check_limits"
	PolicyEnforceAction     = "enforce"
	PolicyUpdateAction      = "update"
)

// Action constants for Cache domain
const (
	CacheGetAction    = "get"
	CacheSetAction    = "set"
	CacheDeleteAction = "delete"
	CacheClearAction  = "clear"
	CacheExistsAction = "exists"
)

// Action constants for Audit domain
const (
	AuditLogAction     = "log"
	AuditGetLogsAction = "get_logs"
	AuditPurgeAction   = "purge"
	AuditExportAction  = "export"
)

// Action constants for Event domain
const (
	EventPublishAction   = "publish"
	EventSubscribeAction = "subscribe"
	EventProcessAction   = "process"
)

// Action constants for Metrics domain
const (
	MetricsRecordAction = "record"
	MetricsGetAction    = "get"
	MetricsExportAction = "export"
)

// Action constants for Crypto domain
const (
	CryptoSignAction    = "sign"
	CryptoVerifyAction  = "verify"
	CryptoEncryptAction = "encrypt"
	CryptoDecryptAction = "decrypt"
	CryptoHashAction    = "hash"
)
