package delegate

import (
	"context"
	"os"
	"testing"

	"github.com/rs/zerolog"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestDelegate_BasicOperations(t *testing.T) {
	logger := zerolog.New(os.Stdout).With().Timestamp().Logger()
	delegate := New(logger)

	t.Run("should register and execute functions", func(t *testing.T) {
		var called bool
		testFunc := func(ctx context.Context, data Data) error {
			called = true
			return nil
		}

		delegate.Register("test", "action", testFunc)
		assert.True(t, delegate.IsRegistered("test", "action"))

		data, err := NewData("test", "action", nil)
		require.NoError(t, err)

		err = delegate.Call(context.Background(), data)
		require.NoError(t, err)
		assert.True(t, called)
	})

	t.Run("should handle multiple functions for same domain/action", func(t *testing.T) {
		callCount := 0
		
		func1 := func(ctx context.Context, data Data) error {
			callCount++
			return nil
		}
		
		func2 := func(ctx context.Context, data Data) error {
			callCount++
			return nil
		}

		delegate.Register("multi", "test", func1)
		delegate.Register("multi", "test", func2)

		data, err := NewData("multi", "test", nil)
		require.NoError(t, err)

		err = delegate.Call(context.Background(), data)
		require.NoError(t, err)
		assert.Equal(t, 2, callCount)
	})

	t.Run("should handle unregistered functions gracefully", func(t *testing.T) {
		data, err := NewData("nonexistent", "action", nil)
		require.NoError(t, err)

		err = delegate.Call(context.Background(), data)
		require.NoError(t, err) // Should not error for unregistered functions
	})

	t.Run("should unregister functions", func(t *testing.T) {
		testFunc := func(ctx context.Context, data Data) error { return nil }
		
		delegate.Register("remove", "test", testFunc)
		assert.True(t, delegate.IsRegistered("remove", "test"))

		delegate.Unregister("remove", "test")
		assert.False(t, delegate.IsRegistered("remove", "test"))
	})

	t.Run("should list registered functions", func(t *testing.T) {
		delegate.Register("list", "action1", func(ctx context.Context, data Data) error { return nil })
		delegate.Register("list", "action2", func(ctx context.Context, data Data) error { return nil })

		registered := delegate.ListRegistered()
		assert.Contains(t, registered["list"], "action1")
		assert.Contains(t, registered["list"], "action2")
	})
}

func TestData_Operations(t *testing.T) {
	t.Run("should create data with params", func(t *testing.T) {
		params := map[string]interface{}{
			"key":   "value",
			"count": 42,
		}

		data, err := NewData("test", "action", params)
		require.NoError(t, err)
		
		assert.Equal(t, "test", data.Domain)
		assert.Equal(t, "action", data.Action)
		assert.True(t, data.HasParams())
	})

	t.Run("should unmarshal params correctly", func(t *testing.T) {
		originalParams := struct {
			Name  string `json:"name"`
			Value int    `json:"value"`
		}{
			Name:  "test",
			Value: 123,
		}

		data, err := NewData("test", "action", originalParams)
		require.NoError(t, err)

		var unmarshaledParams struct {
			Name  string `json:"name"`
			Value int    `json:"value"`
		}

		err = data.UnmarshalParams(&unmarshaledParams)
		require.NoError(t, err)
		
		assert.Equal(t, originalParams.Name, unmarshaledParams.Name)
		assert.Equal(t, originalParams.Value, unmarshaledParams.Value)
	})

	t.Run("should handle nil params", func(t *testing.T) {
		data, err := NewData("test", "action", nil)
		require.NoError(t, err)
		
		assert.False(t, data.HasParams())
		assert.Len(t, data.RawParams, 0)
	})

	t.Run("should create proper string representation", func(t *testing.T) {
		data, err := NewData("test", "action", map[string]string{"key": "value"})
		require.NoError(t, err)
		
		str := data.String()
		assert.Contains(t, str, "test")
		assert.Contains(t, str, "action")
	})
}

func TestDelegate_ErrorHandling(t *testing.T) {
	logger := zerolog.New(os.Stdout).With().Timestamp().Logger()
	delegate := New(logger)

	t.Run("should handle function errors gracefully", func(t *testing.T) {
		errorFunc := func(ctx context.Context, data Data) error {
			return assert.AnError
		}

		delegate.Register("error", "test", errorFunc)

		data, err := NewData("error", "test", nil)
		require.NoError(t, err)

		// Call should not return error even if function fails
		err = delegate.Call(context.Background(), data)
		require.NoError(t, err)
	})

	t.Run("should return error for CallSync on function failure", func(t *testing.T) {
		errorFunc := func(ctx context.Context, data Data) error {
			return assert.AnError
		}

		delegate.Register("sync_error", "test", errorFunc)

		data, err := NewData("sync_error", "test", nil)
		require.NoError(t, err)

		// CallSync should return error if function fails
		err = delegate.CallSync(context.Background(), data)
		require.Error(t, err)
		assert.Equal(t, assert.AnError, err)
	})
}

func TestDelegate_ConcurrentAccess(t *testing.T) {
	logger := zerolog.New(os.Stdout).With().Timestamp().Logger()
	delegate := New(logger)

	t.Run("should handle concurrent registration", func(t *testing.T) {
		const numGoroutines = 10
		done := make(chan bool, numGoroutines)

		for i := 0; i < numGoroutines; i++ {
			go func(index int) {
				testFunc := func(ctx context.Context, data Data) error { return nil }
				delegate.Register("concurrent", "test", testFunc)
				done <- true
			}(i)
		}

		// Wait for all goroutines to complete
		for i := 0; i < numGoroutines; i++ {
			<-done
		}

		assert.True(t, delegate.IsRegistered("concurrent", "test"))
	})

	t.Run("should handle concurrent calls", func(t *testing.T) {
		callCount := 0
		testFunc := func(ctx context.Context, data Data) error {
			callCount++
			return nil
		}

		delegate.Register("concurrent_call", "test", testFunc)

		const numCalls = 5
		done := make(chan bool, numCalls)

		for i := 0; i < numCalls; i++ {
			go func() {
				data, _ := NewData("concurrent_call", "test", nil)
				delegate.Call(context.Background(), data)
				done <- true
			}()
		}

		// Wait for all calls to complete
		for i := 0; i < numCalls; i++ {
			<-done
		}

		// Note: Due to race conditions, we can't guarantee exact count
		// but we can ensure the function was called multiple times
		assert.Greater(t, callCount, 0)
	})
}