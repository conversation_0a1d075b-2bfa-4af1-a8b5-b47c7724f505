package delegate

import (
	"context"
	"encoding/json"
	"fmt"
)

// Func represents a function that is registered and called by the system.
type Func func(context.Context, Data) error

// Data represents an event between domains.
type Data struct {
	Domain    string
	Action    string
	RawParams []byte
}

// String implements the Stringer interface.
func (d Data) String() string {
	return fmt.Sprintf(
		"Event{Domain:%#v, Action:%#v, RawParams:%#v}",
		d.Domain, d.Action, string(d.RawParams),
	)
}

// NewData creates a new Data instance with JSON marshaling of params
func NewData(domain, action string, params interface{}) (Data, error) {
	var rawParams []byte
	var err error

	if params != nil {
		rawParams, err = json.Marshal(params)
		if err != nil {
			return Data{}, fmt.Errorf("failed to marshal params: %w", err)
		}
	}

	return Data{
		Domain:    domain,
		Action:    action,
		RawParams: rawParams,
	}, nil
}

// UnmarshalParams unmarshals the RawParams into the provided struct
func (d Data) UnmarshalParams(v interface{}) error {
	if len(d.RawParams) == 0 {
		return nil
	}
	return json.Unmarshal(d.RawParams, v)
}

// HasParams returns true if the Data contains parameters
func (d Data) HasParams() bool {
	return len(d.RawParams) > 0
}