package delegate

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
)

// ExampleUsage demonstrates how to use the delegate pattern to avoid import cycles
func ExampleUsage() {
	// This file demonstrates how different packages can communicate
	// through the delegate pattern without creating import cycles

	// 1. From HTTP handlers - calling license validation without importing license package
	exampleHTTPHandler()

	// 2. From domain services - calling cache operations without importing cache package
	exampleDomainService()

	// 3. From background jobs - calling audit logging without importing audit package
	exampleBackgroundJob()
}

// exampleHTTPHandler shows how HTTP handlers can call domain services
// without creating import cycles
func exampleHTTPHandler() {
	// In a real HTTP handler, you would get the delegate from the service coordinator
	// For example purposes, we'll create a mock delegate
	
	_ = context.Background() // ctx would be used in real implementation
	
	// Example: License validation call from HTTP handler
	params := map[string]interface{}{
		"license_key":         "LIC-12345-ABCDE-67890-FGHIJ",
		"machine_fingerprint": "fp-mac-12345678",
		"timestamp":          time.Now(),
	}
	
	data, err := NewData(LicenseDomain, LicenseValidateAction, params)
	if err != nil {
		fmt.Printf("Error creating delegate data: %v\n", err)
		return
	}
	
	fmt.Printf("HTTP Handler would call: %s.%s with params: %s\n", 
		data.Domain, data.Action, string(data.RawParams))
	
	// In real code, this would be:
	// err = serviceCoordinator.Delegate.CallSync(ctx, data)
}

// exampleDomainService shows how domain services can call infrastructure
// services without importing them
func exampleDomainService() {
	_ = context.Background() // ctx would be used in real implementation
	
	// Example: Cache operation call from domain service
	params := map[string]interface{}{
		"key":       "license:LIC-12345-ABCDE-67890-FGHIJ",
		"value":     map[string]interface{}{"status": "valid", "expires_at": "2025-12-31"},
		"ttl":       3600,
		"timestamp": time.Now(),
	}
	
	data, err := NewData(CacheDomain, CacheSetAction, params)
	if err != nil {
		fmt.Printf("Error creating delegate data: %v\n", err)
		return
	}
	
	fmt.Printf("Domain Service would call: %s.%s with params: %s\n", 
		data.Domain, data.Action, string(data.RawParams))
}

// exampleBackgroundJob shows how background jobs can call audit services
// without importing audit package
func exampleBackgroundJob() {
	_ = context.Background() // ctx would be used in real implementation
	userID := uuid.New()
	
	// Example: Audit logging call from background job
	params := map[string]interface{}{
		"event_type":  "license_expired",
		"entity_type": "license",
		"entity_id":   "LIC-12345-ABCDE-67890-FGHIJ",
		"user_id":     userID.String(),
		"details": map[string]interface{}{
			"expired_at": time.Now(),
			"action":     "auto_suspend",
		},
		"timestamp": time.Now(),
	}
	
	data, err := NewData(AuditDomain, AuditLogAction, params)
	if err != nil {
		fmt.Printf("Error creating delegate data: %v\n", err)
		return
	}
	
	fmt.Printf("Background Job would call: %s.%s with params: %s\n", 
		data.Domain, data.Action, string(data.RawParams))
}

// ExampleCrossPackageCommunication shows various cross-package communication scenarios
func ExampleCrossPackageCommunication() {
	fmt.Println("=== Delegate Pattern Usage Examples ===")
	
	// Scenario 1: License validation triggers multiple side effects
	demonstrateLicenseValidationFlow()
	
	// Scenario 2: Machine registration updates multiple systems
	demonstrateMachineRegistrationFlow()
	
	// Scenario 3: Policy update propagates changes
	demonstratePolicyUpdateFlow()
}

func demonstrateLicenseValidationFlow() {
	fmt.Println("\n1. License Validation Flow:")
	_ = context.Background() // ctx would be used in real implementation
	
	// Step 1: HTTP handler receives license validation request
	fmt.Println("   HTTP Handler -> License Validation")
	licenseData, _ := NewData(LicenseDomain, LicenseValidateAction, map[string]interface{}{
		"license_key": "LIC-12345",
		"machine_fingerprint": "fp-12345",
	})
	fmt.Printf("   Call: %s.%s\n", licenseData.Domain, licenseData.Action)
	
	// Step 2: License service checks cache first
	fmt.Println("   License Service -> Cache Check")
	cacheData, _ := NewData(CacheDomain, CacheGetAction, map[string]interface{}{
		"key": "license:LIC-12345",
	})
	fmt.Printf("   Call: %s.%s\n", cacheData.Domain, cacheData.Action)
	
	// Step 3: License service logs audit event
	fmt.Println("   License Service -> Audit Log")
	auditData, _ := NewData(AuditDomain, AuditLogAction, map[string]interface{}{
		"event_type": "license_validation",
		"entity_id":  "LIC-12345",
	})
	fmt.Printf("   Call: %s.%s\n", auditData.Domain, auditData.Action)
	
	// Step 4: License service records metrics
	fmt.Println("   License Service -> Metrics")
	metricsData, _ := NewData(MetricsDomain, MetricsRecordAction, map[string]interface{}{
		"metric_name": "license_validations_total",
		"value":       1,
	})
	fmt.Printf("   Call: %s.%s\n", metricsData.Domain, metricsData.Action)
}

func demonstrateMachineRegistrationFlow() {
	fmt.Println("\n2. Machine Registration Flow:")
	
	// Step 1: Machine registers
	fmt.Println("   HTTP Handler -> Machine Registration")
	machineData, _ := NewData(MachineDomain, MachineRegisterAction, map[string]interface{}{
		"machine_id":   uuid.New().String(),
		"fingerprint":  "fp-new-machine",
		"license_key":  "LIC-12345",
	})
	fmt.Printf("   Call: %s.%s\n", machineData.Domain, machineData.Action)
	
	// Step 2: Check policy limits
	fmt.Println("   Machine Service -> Policy Validation")
	policyData, _ := NewData(PolicyDomain, PolicyCheckLimitsAction, map[string]interface{}{
		"policy_id": uuid.New().String(),
		"context":   map[string]interface{}{"machine_count": 1},
	})
	fmt.Printf("   Call: %s.%s\n", policyData.Domain, policyData.Action)
	
	// Step 3: Invalidate license cache
	fmt.Println("   Machine Service -> Cache Invalidation")
	invalidateData, _ := NewData(LicenseDomain, LicenseInvalidateAction, map[string]interface{}{
		"license_key": "LIC-12345",
	})
	fmt.Printf("   Call: %s.%s\n", invalidateData.Domain, invalidateData.Action)
}

func demonstratePolicyUpdateFlow() {
	fmt.Println("\n3. Policy Update Flow:")
	
	// Step 1: Policy is updated
	fmt.Println("   Admin API -> Policy Update")
	policyData, _ := NewData(PolicyDomain, PolicyUpdateAction, map[string]interface{}{
		"policy_id": uuid.New().String(),
		"changes":   map[string]interface{}{"machine_limit": 10},
	})
	fmt.Printf("   Call: %s.%s\n", policyData.Domain, policyData.Action)
	
	// Step 2: Clear all related license caches
	fmt.Println("   Policy Service -> Cache Clear")
	cacheData, _ := NewData(CacheDomain, CacheClearAction, map[string]interface{}{
		"pattern": "license:*",
	})
	fmt.Printf("   Call: %s.%s\n", cacheData.Domain, cacheData.Action)
	
	// Step 3: Publish policy change event
	fmt.Println("   Policy Service -> Event Publication")
	eventData, _ := NewData(EventDomain, EventPublishAction, map[string]interface{}{
		"event_type": "policy_updated",
		"payload":    map[string]interface{}{"policy_id": uuid.New().String()},
	})
	fmt.Printf("   Call: %s.%s\n", eventData.Domain, eventData.Action)
}