package entities

import (
	"encoding/json"
	"fmt"
	"slices"
	"time"

	"github.com/google/uuid"
	"github.com/lib/pq"
)

// Permission represents optimized flattened permissions for high-performance lookups
type Permission struct {
	ID            uuid.UUID      `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	UserID        uuid.UUID      `json:"user_id" gorm:"column:user_id;type:uuid;not null"`
	PermissionKey string         `json:"permission_key" gorm:"column:permission_key;size:255;not null;uniqueIndex:idx_user_permission"`
	ResourceIDs   pq.StringArray `json:"resource_ids" gorm:"type:text[]"`
	GrantedBy     *uuid.UUID     `json:"granted_by,omitempty" gorm:"column:granted_by;type:uuid"`
	GrantedAt     time.Time      `json:"granted_at" gorm:"column:granted_at;not null;default:CURRENT_TIMESTAMP"`
	ExpiresAt     *time.Time     `json:"expires_at,omitempty" gorm:"column:expires_at"`
	
	// Delegation fields
	DelegationAllowed   bool            `json:"delegation_allowed" gorm:"column:delegation_allowed;default:false"`
	DelegatedFrom       *uuid.UUID      `json:"delegated_from,omitempty" gorm:"column:delegated_from;type:uuid"`
	MaxDelegationDepth  int             `json:"max_delegation_depth" gorm:"column:max_delegation_depth;default:0"`
	Conditions          json.RawMessage `json:"conditions" gorm:"column:conditions;type:jsonb;default:'{}'"`
	Reason              string          `json:"reason,omitempty" gorm:"column:reason;type:text"`
	
	CreatedAt     time.Time      `json:"created_at" gorm:"column:created_at;not null;default:CURRENT_TIMESTAMP"`
	UpdatedAt     time.Time      `json:"updated_at" gorm:"column:updated_at;not null;default:CURRENT_TIMESTAMP"`
	
	// Relations
	User          *User          `json:"user,omitempty" gorm:"foreignKey:UserID"`
	Granter       *User          `json:"granter,omitempty" gorm:"foreignKey:GrantedBy"`
	ParentPermission *Permission `json:"parent_permission,omitempty" gorm:"foreignKey:DelegatedFrom"`
}

func (Permission) TableName() string {
	return "permissions"
}

// Optimized permission scopes
const (
	ScopeSystem   = "S" // System-wide access
	ScopeOrg      = "O" // Organization access (O:uuid)
	ScopeResource = "R" // Resource access (includes owner permissions)
)

// Resource types
const (
	ResourceTypeAll         = "*"
	ResourceTypeOrganization = "organization"
	ResourceTypeProduct     = "product"
	ResourceTypePolicy      = "policy"
	ResourceTypeLicense     = "license"
	ResourceTypeMachine     = "machine"
	ResourceTypeUser        = "user"
	ResourceTypeAPIToken    = "api_token"
	ResourceTypeSession     = "session"
)

// Actions
const (
	ActionAll      = "*"
	ActionCreate   = "create"
	ActionRead     = "read"
	ActionUpdate   = "update"
	ActionDelete   = "delete"
	ActionValidate = "validate"
	ActionCheckout = "checkout"
)

// BuildPermissionKey creates a flattened permission key
func BuildPermissionKey(scope, resourceType, action string) string {
	return fmt.Sprintf("%s:%s:%s", scope, resourceType, action)
}

// BuildOrgScope creates an organization scope string
func BuildOrgScope(orgID string) string {
	return "O:" + orgID
}

// IsExpired checks if the permission has expired
func (p *Permission) IsExpired() bool {
	if p.ExpiresAt == nil {
		return false
	}
	return time.Now().After(*p.ExpiresAt)
}

// IsActive checks if the permission is currently active (not expired)
func (p *Permission) IsActive() bool {
	return !p.IsExpired()
}

// MatchesResource checks if permission applies to a specific resource
func (p *Permission) MatchesResource(resourceType, resourceID string) bool {
	// If no specific resource IDs, it's a wildcard permission
	if len(p.ResourceIDs) == 0 {
		return true
	}
	// Check if resource ID is in the allowed list
	return slices.Contains(p.ResourceIDs, resourceID)
}

// CanDelegate checks if this permission can be delegated
func (p *Permission) CanDelegate() bool {
	return p.DelegationAllowed && p.IsActive()
}

// IsDelegated checks if this permission was delegated from another
func (p *Permission) IsDelegated() bool {
	return p.DelegatedFrom != nil
}

// GetDelegationDepth returns the current delegation depth
func (p *Permission) GetDelegationDepth() int {
	if !p.IsDelegated() {
		return 0
	}
	// In a real implementation, would need to traverse the delegation chain
	// For now, assume depth is tracked
	return 1
}

// CanSubDelegate checks if this permission can be further delegated
func (p *Permission) CanSubDelegate() bool {
	if !p.CanDelegate() {
		return false
	}
	currentDepth := p.GetDelegationDepth()
	return currentDepth < p.MaxDelegationDepth
}
