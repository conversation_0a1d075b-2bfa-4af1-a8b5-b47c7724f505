package entities

import (
	"encoding/json"
	"time"

	"github.com/google/uuid"
	"github.com/lib/pq"
)

// RoleScope defines the scope of a role
type RoleScope string

const (
	RoleScopeSystem       RoleScope = "system"
	RoleScopeOrganization RoleScope = "organization"
	RoleScopeCustom       RoleScope = "custom"
)

// Role represents a reusable set of permissions
type Role struct {
	ID             uuid.UUID  `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	Name           string     `json:"name" gorm:"column:name;size:255;not null;uniqueIndex:idx_role_name_org"`
	DisplayName    string     `json:"display_name" gorm:"column:display_name;size:255;not null"`
	Description    string     `json:"description" gorm:"column:description;type:text"`
	Scope          RoleScope  `json:"scope" gorm:"column:scope;size:50;not null"`
	OrganizationID *uuid.UUID `json:"organization_id,omitempty" gorm:"column:organization_id;type:uuid;uniqueIndex:idx_role_name_org"`
	
	// Permissions as JSON array of permission keys
	Permissions pq.StringArray `json:"permissions" gorm:"type:text[]"`
	
	// Conditions for the role (time-based access, IP restrictions, etc.)
	Conditions json.RawMessage `json:"conditions" gorm:"column:conditions;type:jsonb;default:'{}'"`
	
	// Metadata
	IsSystem  bool       `json:"is_system" gorm:"column:is_system;default:false"`
	IsActive  bool       `json:"is_active" gorm:"column:is_active;default:true"`
	CreatedBy *uuid.UUID `json:"created_by,omitempty" gorm:"column:created_by;type:uuid"`
	
	// Timestamps
	CreatedAt time.Time `json:"created_at" gorm:"column:created_at;not null;default:CURRENT_TIMESTAMP"`
	UpdatedAt time.Time `json:"updated_at" gorm:"column:updated_at;not null;default:CURRENT_TIMESTAMP"`
	
	// Relations
	Organization *Organization `json:"organization,omitempty" gorm:"foreignKey:OrganizationID"`
	Creator      *User         `json:"creator,omitempty" gorm:"foreignKey:CreatedBy"`
}

func (Role) TableName() string {
	return "roles"
}

// IsSystemRole checks if this is a system-defined role
func (r *Role) IsSystemRole() bool {
	return r.IsSystem
}

// IsOrgRole checks if this is an organization-scoped role
func (r *Role) IsOrgRole() bool {
	return r.Scope == RoleScopeOrganization
}

// CanBeModified checks if the role can be modified
func (r *Role) CanBeModified() bool {
	return !r.IsSystem && r.IsActive
}

// GetEffectivePermissions returns permissions with placeholders replaced
func (r *Role) GetEffectivePermissions(orgID string) []string {
	if r.Scope != RoleScopeOrganization || orgID == "" {
		return r.Permissions
	}
	
	// Replace {{org_id}} placeholder with actual org ID
	effectivePerms := make([]string, len(r.Permissions))
	for i, perm := range r.Permissions {
		effectivePerms[i] = replaceOrgPlaceholder(perm, orgID)
	}
	return effectivePerms
}

// UserRole represents the assignment of a role to a user
type UserRole struct {
	ID             uuid.UUID  `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	UserID         uuid.UUID  `json:"user_id" gorm:"column:user_id;type:uuid;not null;uniqueIndex:idx_user_role_org"`
	RoleID         uuid.UUID  `json:"role_id" gorm:"column:role_id;type:uuid;not null;uniqueIndex:idx_user_role_org"`
	OrganizationID *uuid.UUID `json:"organization_id,omitempty" gorm:"column:organization_id;type:uuid;uniqueIndex:idx_user_role_org"`
	
	// Assignment metadata
	GrantedBy uuid.UUID       `json:"granted_by" gorm:"column:granted_by;type:uuid;not null"`
	GrantedAt time.Time       `json:"granted_at" gorm:"column:granted_at;not null;default:CURRENT_TIMESTAMP"`
	ExpiresAt *time.Time      `json:"expires_at,omitempty" gorm:"column:expires_at"`
	Conditions json.RawMessage `json:"conditions" gorm:"column:conditions;type:jsonb;default:'{}'"`
	Reason     string          `json:"reason,omitempty" gorm:"column:reason;type:text"`
	
	// Timestamps
	CreatedAt time.Time `json:"created_at" gorm:"column:created_at;not null;default:CURRENT_TIMESTAMP"`
	UpdatedAt time.Time `json:"updated_at" gorm:"column:updated_at;not null;default:CURRENT_TIMESTAMP"`
	
	// Relations
	User         *User         `json:"user,omitempty" gorm:"foreignKey:UserID"`
	Role         *Role         `json:"role,omitempty" gorm:"foreignKey:RoleID"`
	Organization *Organization `json:"organization,omitempty" gorm:"foreignKey:OrganizationID"`
	Granter      *User         `json:"granter,omitempty" gorm:"foreignKey:GrantedBy"`
}

func (UserRole) TableName() string {
	return "user_roles"
}

// IsExpired checks if the role assignment has expired
func (ur *UserRole) IsExpired() bool {
	if ur.ExpiresAt == nil {
		return false
	}
	return time.Now().After(*ur.ExpiresAt)
}

// IsActive checks if the role assignment is currently active
func (ur *UserRole) IsActive() bool {
	return !ur.IsExpired()
}

// PermissionAuditLog tracks changes to permissions
type PermissionAuditLog struct {
	ID           uuid.UUID       `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	Action       string          `json:"action" gorm:"column:action;size:50;not null"` // grant, revoke, delegate, expire, update
	ActorID      uuid.UUID       `json:"actor_id" gorm:"column:actor_id;type:uuid;not null"`
	ActorIP      string          `json:"actor_ip,omitempty" gorm:"column:actor_ip;type:inet"`
	TargetUserID uuid.UUID       `json:"target_user_id" gorm:"column:target_user_id;type:uuid;not null"`
	PermissionKey string         `json:"permission_key,omitempty" gorm:"column:permission_key;size:255"`
	RoleID       *uuid.UUID      `json:"role_id,omitempty" gorm:"column:role_id;type:uuid"`
	OldValue     json.RawMessage `json:"old_value,omitempty" gorm:"column:old_value;type:jsonb"`
	NewValue     json.RawMessage `json:"new_value,omitempty" gorm:"column:new_value;type:jsonb"`
	Reason       string          `json:"reason,omitempty" gorm:"column:reason;type:text"`
	CreatedAt    time.Time       `json:"created_at" gorm:"column:created_at;not null;default:CURRENT_TIMESTAMP"`
	
	// Relations
	Actor      *User `json:"actor,omitempty" gorm:"foreignKey:ActorID"`
	TargetUser *User `json:"target_user,omitempty" gorm:"foreignKey:TargetUserID"`
	Role       *Role `json:"role,omitempty" gorm:"foreignKey:RoleID"`
}

func (PermissionAuditLog) TableName() string {
	return "permission_audit_log"
}

// Predefined system roles
var (
	SystemAdminRole = Role{
		Name:        "system_admin",
		DisplayName: "System Administrator",
		Description: "Full system access",
		Scope:       RoleScopeSystem,
		Permissions: []string{"S:*:*"},
		IsSystem:    true,
		IsActive:    true,
	}
	
	OrgAdminRole = Role{
		Name:        "org_admin",
		DisplayName: "Organization Administrator",
		Description: "Full organization access",
		Scope:       RoleScopeOrganization,
		Permissions: []string{"O:{{org_id}}:*:*"},
		IsSystem:    true,
		IsActive:    true,
	}
	
	OrgManagerRole = Role{
		Name:        "org_manager",
		DisplayName: "Organization Manager",
		Description: "Manage organization resources",
		Scope:       RoleScopeOrganization,
		Permissions: []string{
			"O:{{org_id}}:product:*",
			"O:{{org_id}}:license:*",
			"O:{{org_id}}:user:read",
		},
		IsSystem: true,
		IsActive: true,
	}
	
	OrgMemberRole = Role{
		Name:        "org_member",
		DisplayName: "Organization Member",
		Description: "Basic organization access",
		Scope:       RoleScopeOrganization,
		Permissions: []string{
			"O:{{org_id}}:product:read",
			"O:{{org_id}}:license:read",
		},
		IsSystem: true,
		IsActive: true,
	}
)

// Helper function to replace organization placeholder
func replaceOrgPlaceholder(permission string, orgID string) string {
	// Replace {{org_id}} with actual organization ID
	const placeholder = "{{org_id}}"
	if orgID != "" && len(permission) > len(placeholder) {
		// Use simple string replacement for now
		// Could use strings.ReplaceAll in production
		result := permission
		for i := 0; i < len(result)-len(placeholder)+1; i++ {
			if result[i:i+len(placeholder)] == placeholder {
				result = result[:i] + orgID + result[i+len(placeholder):]
				i += len(orgID) - 1
			}
		}
		return result
	}
	return permission
}