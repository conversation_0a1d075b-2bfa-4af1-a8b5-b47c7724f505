package permission

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/gokeys/gokeys/internal/domain/entities"
	"github.com/gokeys/gokeys/internal/domain/repositories"
	"github.com/google/uuid"
	"github.com/rs/zerolog/log"
)

// EnhancedPermissionService provides flexible permission management with role support
type EnhancedPermissionService struct {
	permissionRepo repositories.PermissionRepository
	roleRepo       repositories.RoleRepository
	userRoleRepo   repositories.UserRoleRepository
	auditLogRepo   repositories.PermissionAuditLogRepository
	cache          PermissionCache
}

// PermissionCache interface for caching permissions
type PermissionCache interface {
	GetUserPermissions(userID string) ([]string, bool)
	SetUserPermissions(userID string, permissions []string, ttl time.Duration)
	InvalidateUser(userID string)
	InvalidateAll()
}

// NewEnhancedPermissionService creates a new enhanced permission service
func NewEnhancedPermissionService(
	permissionRepo repositories.PermissionRepository,
	roleRepo repositories.RoleRepository,
	userRoleRepo repositories.UserRoleRepository,
	auditLogRepo repositories.PermissionAuditLogRepository,
	cache PermissionCache,
) *EnhancedPermissionService {
	return &EnhancedPermissionService{
		permissionRepo: permissionRepo,
		roleRepo:       roleRepo,
		userRoleRepo:   userRoleRepo,
		auditLogRepo:   auditLogRepo,
		cache:          cache,
	}
}

// ==================== ROLE MANAGEMENT ====================

// AssignRole assigns a role to a user with context
func (s *EnhancedPermissionService) AssignRole(ctx context.Context, req AssignRoleRequest) (*entities.UserRole, error) {
	// Validate role exists and is active
	role, err := s.roleRepo.GetByID(ctx, req.RoleID)
	if err != nil {
		return nil, fmt.Errorf("role not found: %w", err)
	}
	if !role.IsActive {
		return nil, fmt.Errorf("role is not active")
	}

	// Validate organization context for org-scoped roles
	if role.IsOrgRole() && req.OrganizationID == nil {
		return nil, fmt.Errorf("organization ID required for organization-scoped role")
	}

	// Assign role
	userRole, err := s.userRoleRepo.AssignRole(
		ctx,
		req.UserID,
		req.RoleID,
		req.OrganizationID,
		req.GrantedBy,
		req.ExpiresAt,
		req.Reason,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to assign role: %w", err)
	}

	// Log action
	s.logAction(ctx, "assign_role", req.GrantedBy, req.UserID, "", &req.RoleID, nil, userRole, req.Reason)

	// Invalidate cache
	s.cache.InvalidateUser(req.UserID.String())

	return userRole, nil
}

// RevokeRole revokes a role from a user
func (s *EnhancedPermissionService) RevokeRole(ctx context.Context, req RevokeRoleRequest) error {
	// Get existing role assignment for audit
	userRole, err := s.userRoleRepo.GetByID(ctx, req.UserRoleID)
	if err != nil {
		return fmt.Errorf("role assignment not found: %w", err)
	}

	// Revoke role
	err = s.userRoleRepo.Delete(ctx, req.UserRoleID)
	if err != nil {
		return fmt.Errorf("failed to revoke role: %w", err)
	}

	// Log action
	s.logAction(ctx, "revoke_role", req.RevokedBy, userRole.UserID, "", &userRole.RoleID, userRole, nil, req.Reason)

	// Invalidate cache
	s.cache.InvalidateUser(userRole.UserID.String())

	return nil
}

// ==================== PERMISSION DELEGATION ====================

// DelegatePermission allows a user to delegate their permission to another user
func (s *EnhancedPermissionService) DelegatePermission(ctx context.Context, req DelegatePermissionRequest) (*entities.Permission, error) {
	// Get source permission
	sourcePerms, err := s.permissionRepo.GetUserPermissions(ctx, req.DelegatorID.String())
	if err != nil {
		return nil, fmt.Errorf("failed to get delegator permissions: %w", err)
	}

	// Find matching permission that can be delegated
	var sourcePermission *entities.Permission
	for _, perm := range sourcePerms {
		if perm.PermissionKey == req.PermissionKey && perm.CanDelegate() {
			sourcePermission = perm
			break
		}
	}

	if sourcePermission == nil {
		return nil, fmt.Errorf("delegator does not have delegatable permission: %s", req.PermissionKey)
	}

	// Check delegation depth
	if !sourcePermission.CanSubDelegate() {
		return nil, fmt.Errorf("permission cannot be further delegated (max depth reached)")
	}

	// Parse permission key
	scope, resourceType, action := parsePermissionKey(req.PermissionKey)

	// Create delegated permission
	delegatedPerm, err := s.permissionRepo.GrantPermissionWithResources(
		ctx,
		req.DelegateeID,
		scope,
		resourceType,
		[]string{action},
		req.ResourceIDs,
		&req.DelegatorID,
		req.ExpiresAt,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to create delegated permission: %w", err)
	}

	// Update delegation metadata
	delegatedPerm.DelegatedFrom = &sourcePermission.ID
	delegatedPerm.DelegationAllowed = req.AllowSubDelegation
	delegatedPerm.MaxDelegationDepth = sourcePermission.MaxDelegationDepth - 1
	delegatedPerm.Conditions = req.Conditions
	delegatedPerm.Reason = req.Reason

	err = s.permissionRepo.Update(ctx, delegatedPerm)
	if err != nil {
		return nil, fmt.Errorf("failed to update delegation metadata: %w", err)
	}

	// Log action
	s.logAction(ctx, "delegate", req.DelegatorID, req.DelegateeID, req.PermissionKey, nil, nil, delegatedPerm, req.Reason)

	// Invalidate cache
	s.cache.InvalidateUser(req.DelegateeID.String())

	return delegatedPerm, nil
}

// RevokeDelegatedPermission revokes a delegated permission
func (s *EnhancedPermissionService) RevokeDelegatedPermission(ctx context.Context, permissionID uuid.UUID, revokedBy uuid.UUID, reason string) error {
	// Get permission
	permission, err := s.permissionRepo.GetByID(ctx, permissionID.String())
	if err != nil {
		return fmt.Errorf("permission not found: %w", err)
	}

	if !permission.IsDelegated() {
		return fmt.Errorf("permission is not delegated")
	}

	// Revoke permission
	err = s.permissionRepo.Delete(ctx, permissionID.String())
	if err != nil {
		return fmt.Errorf("failed to revoke permission: %w", err)
	}

	// Log action
	s.logAction(ctx, "revoke", revokedBy, permission.UserID, permission.PermissionKey, nil, permission, nil, reason)

	// Invalidate cache
	s.cache.InvalidateUser(permission.UserID.String())

	return nil
}

// ==================== EFFECTIVE PERMISSIONS ====================

// GetUserEffectivePermissions gets all effective permissions for a user (direct + role-based + delegated)
func (s *EnhancedPermissionService) GetUserEffectivePermissions(ctx context.Context, userID uuid.UUID, orgID *uuid.UUID) ([]EffectivePermission, error) {
	var effectivePerms []EffectivePermission

	// Check cache first
	cacheKey := userID.String()
	if orgID != nil {
		cacheKey += ":" + orgID.String()
	}

	// Get direct permissions
	directPerms, err := s.permissionRepo.GetUserPermissions(ctx, userID.String())
	if err != nil {
		return nil, fmt.Errorf("failed to get direct permissions: %w", err)
	}

	for _, perm := range directPerms {
		if perm.IsActive() {
			effectivePerms = append(effectivePerms, EffectivePermission{
				PermissionKey: perm.PermissionKey,
				Source:        "direct",
				SourceID:      perm.ID.String(),
				ResourceIDs:   perm.ResourceIDs,
				ExpiresAt:     perm.ExpiresAt,
				Conditions:    perm.Conditions,
			})
		}
	}

	// Get role-based permissions
	rolePerms, err := s.userRoleRepo.GetUserEffectivePermissions(ctx, userID, orgID)
	if err != nil {
		return nil, fmt.Errorf("failed to get role permissions: %w", err)
	}

	for _, permKey := range rolePerms {
		effectivePerms = append(effectivePerms, EffectivePermission{
			PermissionKey: permKey,
			Source:        "role",
			ResourceIDs:   []string{}, // Role permissions are typically wildcard
		})
	}

	return effectivePerms, nil
}

// CheckPermission checks if a user has a specific permission considering all sources
func (s *EnhancedPermissionService) CheckPermission(ctx context.Context, req CheckPermissionRequest) (bool, error) {
	// Get effective permissions
	effectivePerms, err := s.GetUserEffectivePermissions(ctx, req.UserID, req.OrganizationID)
	if err != nil {
		return false, err
	}

	// Build permission key to check
	permissionKey := entities.BuildPermissionKey(req.Scope, req.ResourceType, req.Action)

	// Check each effective permission
	for _, perm := range effectivePerms {
		if s.permissionMatches(perm, permissionKey, req.ResourceID) {
			// Check additional conditions if any
			if len(perm.Conditions) > 0 {
				if !s.evaluateConditions(perm.Conditions, req.Context) {
					continue
				}
			}
			return true, nil
		}
	}

	return false, nil
}

// ==================== HELPER METHODS ====================

// logAction logs a permission-related action to audit log
func (s *EnhancedPermissionService) logAction(
	ctx context.Context,
	action string,
	actorID, targetUserID uuid.UUID,
	permissionKey string,
	roleID *uuid.UUID,
	oldValue, newValue interface{},
	reason string,
) {
	oldJSON, _ := json.Marshal(oldValue)
	newJSON, _ := json.Marshal(newValue)

	logEntry := &entities.PermissionAuditLog{
		Action:        action,
		ActorID:       actorID,
		TargetUserID:  targetUserID,
		PermissionKey: permissionKey,
		RoleID:        roleID,
		OldValue:      oldJSON,
		NewValue:      newJSON,
		Reason:        reason,
		CreatedAt:     time.Now(),
	}

	err := s.auditLogRepo.LogAction(ctx, logEntry)
	if err != nil {
		log.Error().Err(err).Msg("Failed to log permission action")
	}
}

// permissionMatches checks if an effective permission matches the requested permission
func (s *EnhancedPermissionService) permissionMatches(effectivePerm EffectivePermission, requestedKey string, resourceID *string) bool {
	// Check exact match
	if effectivePerm.PermissionKey == requestedKey {
		return s.checkResourceAccess(effectivePerm, resourceID)
	}

	// Check wildcard matches
	effectiveScope, effectiveResource, effectiveAction := parsePermissionKey(effectivePerm.PermissionKey)
	requestedScope, requestedResource, requestedAction := parsePermissionKey(requestedKey)

	// System scope matches everything
	if effectiveScope == entities.ScopeSystem {
		return true
	}

	// Check scope match
	if effectiveScope != requestedScope {
		return false
	}

	// Check resource type match (with wildcard)
	if effectiveResource != entities.ResourceTypeAll && effectiveResource != requestedResource {
		return false
	}

	// Check action match (with wildcard)
	if effectiveAction != entities.ActionAll && effectiveAction != requestedAction {
		return false
	}

	return s.checkResourceAccess(effectivePerm, resourceID)
}

// checkResourceAccess checks if permission allows access to specific resource
func (s *EnhancedPermissionService) checkResourceAccess(perm EffectivePermission, resourceID *string) bool {
	if resourceID == nil {
		return true
	}

	// Empty resource IDs means wildcard access
	if len(perm.ResourceIDs) == 0 {
		return true
	}

	// Check if resource ID is in allowed list
	for _, allowedID := range perm.ResourceIDs {
		if allowedID == *resourceID {
			return true
		}
	}

	return false
}

// evaluateConditions evaluates permission conditions
func (s *EnhancedPermissionService) evaluateConditions(conditions json.RawMessage, context map[string]interface{}) bool {
	// Simple implementation - in production would use a proper rule engine
	// or integrate with OPA for complex condition evaluation
	
	var condMap map[string]interface{}
	if err := json.Unmarshal(conditions, &condMap); err != nil {
		return true // If can't parse, allow by default
	}

	// Example: Check time-based conditions
	if validFrom, ok := condMap["valid_from"].(string); ok {
		t, err := time.Parse(time.RFC3339, validFrom)
		if err == nil && time.Now().Before(t) {
			return false
		}
	}

	if validUntil, ok := condMap["valid_until"].(string); ok {
		t, err := time.Parse(time.RFC3339, validUntil)
		if err == nil && time.Now().After(t) {
			return false
		}
	}

	// Example: Check IP-based conditions
	if allowedIPs, ok := condMap["allowed_ips"].([]interface{}); ok {
		if clientIP, ok := context["client_ip"].(string); ok {
			allowed := false
			for _, ip := range allowedIPs {
				if ipStr, ok := ip.(string); ok && ipStr == clientIP {
					allowed = true
					break
				}
			}
			if !allowed {
				return false
			}
		}
	}

	return true
}

// parsePermissionKey parses a permission key into components
func parsePermissionKey(key string) (scope, resourceType, action string) {
	// Implementation would parse the key format
	// For now, simple placeholder
	return "", "", ""
}

// ==================== REQUEST/RESPONSE TYPES ====================

// AssignRoleRequest represents a role assignment request
type AssignRoleRequest struct {
	UserID         uuid.UUID
	RoleID         uuid.UUID
	OrganizationID *uuid.UUID
	GrantedBy      uuid.UUID
	ExpiresAt      *time.Time
	Reason         string
	Conditions     json.RawMessage
}

// RevokeRoleRequest represents a role revocation request
type RevokeRoleRequest struct {
	UserRoleID uuid.UUID
	RevokedBy  uuid.UUID
	Reason     string
}

// DelegatePermissionRequest represents a permission delegation request
type DelegatePermissionRequest struct {
	DelegatorID        uuid.UUID
	DelegateeID        uuid.UUID
	PermissionKey      string
	ResourceIDs        []string
	AllowSubDelegation bool
	ExpiresAt          *time.Time
	Conditions         json.RawMessage
	Reason             string
}

// CheckPermissionRequest represents a permission check request
type CheckPermissionRequest struct {
	UserID         uuid.UUID
	Scope          string
	ResourceType   string
	Action         string
	ResourceID     *string
	OrganizationID *uuid.UUID
	Context        map[string]interface{} // Additional context for condition evaluation
}

// EffectivePermission represents a permission with its source
type EffectivePermission struct {
	PermissionKey string          `json:"permission_key"`
	Source        string          `json:"source"` // "direct", "role", "delegated"
	SourceID      string          `json:"source_id,omitempty"`
	ResourceIDs   []string        `json:"resource_ids,omitempty"`
	ExpiresAt     *time.Time      `json:"expires_at,omitempty"`
	Conditions    json.RawMessage `json:"conditions,omitempty"`
}