package opa

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"github.com/gokeys/gokeys/internal/domain/entities"
	"github.com/gokeys/gokeys/internal/domain/repositories"
	"github.com/rs/zerolog/log"
)

// OPAService provides integration with Open Policy Agent
type OPAService struct {
	opaURL         string
	httpClient     *http.Client
	permissionRepo repositories.PermissionRepository
	roleRepo       repositories.RoleRepository
	userRoleRepo   repositories.UserRoleRepository
}

// NewOPAService creates a new OPA service
func NewOPAService(
	opaURL string,
	permissionRepo repositories.PermissionRepository,
	roleRepo repositories.RoleRepository,
	userRoleRepo repositories.UserRoleRepository,
) *OPAService {
	return &OPAService{
		opaURL: opaURL,
		httpClient: &http.Client{
			Timeout: 5 * time.Second,
		},
		permissionRepo: permissionRepo,
		roleRepo:       roleRepo,
		userRoleRepo:   userRoleRepo,
	}
}

// ==================== POLICY MANAGEMENT ====================

// UpdatePolicy updates OPA policy bundle
func (s *OPAService) UpdatePolicy(ctx context.Context, policy string) error {
	url := fmt.Sprintf("%s/v1/policies/gokeys/authz", s.opaURL)
	
	req, err := http.NewRequestWithContext(ctx, "PUT", url, bytes.NewBufferString(policy))
	if err != nil {
		return fmt.Errorf("failed to create request: %w", err)
	}
	
	req.Header.Set("Content-Type", "text/plain")
	
	resp, err := s.httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("failed to update policy: %w", err)
	}
	defer resp.Body.Close()
	
	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("policy update failed: %s", string(body))
	}
	
	return nil
}

// LoadDefaultPolicies loads default authorization policies
func (s *OPAService) LoadDefaultPolicies(ctx context.Context) error {
	policy := `
package gokeys.authz

import future.keywords

# System admin có tất cả quyền
allow if {
    some role in input.user.roles
    role.name == "system_admin"
}

# Org admin có quyền trong org của họ
allow if {
    some role in input.user.roles
    role.name == "org_admin"
    role.organization_id == input.resource.organization_id
}

# Permission-based access
allow if {
    some perm in input.user.permissions
    permission_matches(perm, input.action, input.resource)
}

# Delegation rules
allow if {
    some perm in input.user.permissions
    perm.delegation_allowed == true
    delegated_permission_valid(perm, input)
}

# Helper: Check if permission matches request
permission_matches(perm, action, resource) if {
    perm.active == true
    permission_key_matches(perm.key, action, resource)
    resource_access_allowed(perm, resource)
}

# Helper: Check permission key matching with wildcards
permission_key_matches(key, action, resource) if {
    parts := split(key, ":")
    scope := parts[0]
    resource_type := parts[1]
    perm_action := parts[2]
    
    # Check scope
    scope_matches(scope, resource)
    
    # Check resource type
    resource_type_matches(resource_type, resource.type)
    
    # Check action
    action_matches(perm_action, action)
}

# Scope matching rules
scope_matches(scope, resource) if {
    scope == "S"  # System scope matches all
}

scope_matches(scope, resource) if {
    startswith(scope, "O:")
    org_id := substring(scope, 2, -1)
    org_id == resource.organization_id
}

scope_matches(scope, resource) if {
    scope == "R"
    # Resource scope - additional checks needed
}

# Resource type matching with wildcards
resource_type_matches(perm_type, actual_type) if {
    perm_type == "*"
}

resource_type_matches(perm_type, actual_type) if {
    perm_type == actual_type
}

# Action matching with wildcards
action_matches(perm_action, actual_action) if {
    perm_action == "*"
}

action_matches(perm_action, actual_action) if {
    perm_action == actual_action
}

# Resource access control
resource_access_allowed(perm, resource) if {
    count(perm.resource_ids) == 0  # Empty means wildcard
}

resource_access_allowed(perm, resource) if {
    resource.id in perm.resource_ids
}

# Delegation validation
delegated_permission_valid(perm, input) if {
    perm.delegated_from != null
    delegation_not_expired(perm)
    delegation_conditions_met(perm, input)
}

delegation_not_expired(perm) if {
    perm.expires_at == null
}

delegation_not_expired(perm) if {
    time.parse_rfc3339_ns(perm.expires_at) > time.now_ns()
}

delegation_conditions_met(perm, input) if {
    # Evaluate additional conditions
    # This can be extended based on requirements
    true
}
`
	
	return s.UpdatePolicy(ctx, policy)
}

// ==================== POLICY EVALUATION ====================

// EvaluatePermission evaluates a permission request against OPA policies
func (s *OPAService) EvaluatePermission(ctx context.Context, req EvaluateRequest) (*EvaluateResponse, error) {
	// Prepare input data
	input, err := s.prepareInput(ctx, req)
	if err != nil {
		return nil, fmt.Errorf("failed to prepare input: %w", err)
	}
	
	// Make OPA query
	url := fmt.Sprintf("%s/v1/data/gokeys/authz/allow", s.opaURL)
	
	body, err := json.Marshal(map[string]interface{}{
		"input": input,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to marshal input: %w", err)
	}
	
	httpReq, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewBuffer(body))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}
	
	httpReq.Header.Set("Content-Type", "application/json")
	
	resp, err := s.httpClient.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("failed to evaluate policy: %w", err)
	}
	defer resp.Body.Close()
	
	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("policy evaluation failed: %s", string(body))
	}
	
	// Parse response
	var opaResp OPAResponse
	if err := json.NewDecoder(resp.Body).Decode(&opaResp); err != nil {
		return nil, fmt.Errorf("failed to decode response: %w", err)
	}
	
	return &EvaluateResponse{
		Allow:       opaResp.Result,
		DecisionID:  opaResp.DecisionID,
		Explanation: s.generateExplanation(opaResp),
	}, nil
}

// prepareInput prepares input data for OPA evaluation
func (s *OPAService) prepareInput(ctx context.Context, req EvaluateRequest) (map[string]interface{}, error) {
	// Get user data
	userData, err := s.getUserData(ctx, req.UserID)
	if err != nil {
		return nil, err
	}
	
	// Build input
	input := map[string]interface{}{
		"user": userData,
		"action": req.Action,
		"resource": map[string]interface{}{
			"type":            req.ResourceType,
			"id":              req.ResourceID,
			"organization_id": req.OrganizationID,
		},
		"context": req.Context,
	}
	
	return input, nil
}

// getUserData gets user data including permissions and roles
func (s *OPAService) getUserData(ctx context.Context, userID string) (map[string]interface{}, error) {
	// Get direct permissions
	permissions, err := s.permissionRepo.GetUserPermissions(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get permissions: %w", err)
	}
	
	// Get user roles
	userUUID, err := parseUUID(userID)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID: %w", err)
	}
	
	userRoles, err := s.userRoleRepo.GetUserRoles(ctx, userUUID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user roles: %w", err)
	}
	
	// Format permissions
	permData := make([]map[string]interface{}, len(permissions))
	for i, perm := range permissions {
		permData[i] = map[string]interface{}{
			"key":                perm.PermissionKey,
			"active":             perm.IsActive(),
			"resource_ids":       perm.ResourceIDs,
			"delegation_allowed": perm.DelegationAllowed,
			"delegated_from":     perm.DelegatedFrom,
			"expires_at":         perm.ExpiresAt,
		}
	}
	
	// Format roles
	roleData := make([]map[string]interface{}, len(userRoles))
	for i, userRole := range userRoles {
		if userRole.Role != nil {
			roleData[i] = map[string]interface{}{
				"name":            userRole.Role.Name,
				"scope":           userRole.Role.Scope,
				"organization_id": userRole.OrganizationID,
				"active":          userRole.IsActive(),
			}
		}
	}
	
	return map[string]interface{}{
		"id":          userID,
		"permissions": permData,
		"roles":       roleData,
	}, nil
}

// generateExplanation generates a human-readable explanation of the decision
func (s *OPAService) generateExplanation(resp OPAResponse) string {
	// In a real implementation, would parse OPA's explanation/provenance
	// For now, simple message
	if resp.Result {
		return "Access granted based on policy evaluation"
	}
	return "Access denied - no matching permissions or roles"
}

// ==================== DATA SYNCHRONIZATION ====================

// SyncPermissionsToOPA syncs permission data to OPA for caching
func (s *OPAService) SyncPermissionsToOPA(ctx context.Context) error {
	// This would sync permission data to OPA's data store
	// for better performance in production
	log.Info().Msg("Syncing permissions to OPA")
	
	// Implementation would batch sync data to OPA
	// For now, placeholder
	
	return nil
}

// ==================== TYPES ====================

// EvaluateRequest represents a permission evaluation request
type EvaluateRequest struct {
	UserID         string                 `json:"user_id"`
	Action         string                 `json:"action"`
	ResourceType   string                 `json:"resource_type"`
	ResourceID     string                 `json:"resource_id,omitempty"`
	OrganizationID string                 `json:"organization_id,omitempty"`
	Context        map[string]interface{} `json:"context,omitempty"`
}

// EvaluateResponse represents the evaluation result
type EvaluateResponse struct {
	Allow       bool   `json:"allow"`
	DecisionID  string `json:"decision_id,omitempty"`
	Explanation string `json:"explanation,omitempty"`
}

// OPAResponse represents OPA's response format
type OPAResponse struct {
	Result     bool   `json:"result"`
	DecisionID string `json:"decision_id,omitempty"`
}

// ==================== HELPER FUNCTIONS ====================

func parseUUID(s string) (entities.UUID, error) {
	// Simple UUID parsing - would use proper UUID library
	// For now, placeholder
	var uuid entities.UUID
	return uuid, nil
}

// Define UUID type if not exists
type UUID = entities.UUID