package services

import (
	"context"
	"os"
	"testing"
	"time"

	"github.com/gokeys/gokeys/internal/config"
	"github.com/gokeys/gokeys/internal/ports/delegate"
	"github.com/google/uuid"
	"github.com/rs/zerolog"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

func setupTestServiceCoordinator(t *testing.T) *ServiceCoordinator {
	// Use in-memory SQLite for testing
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	require.NoError(t, err)

	logger := zerolog.New(os.Stdout).With().
		Timestamp().
		Str("test", t.Name()).
		Logger()

	// Create service coordinator
	cfg := &config.Config{
		OPA: &config.OPAConfig{
			Enabled: false, // Disable OPA for tests
		},
	}
	coordinator := NewServiceCoordinator(db, cfg, logger)
	require.NotNil(t, coordinator)
	require.NotNil(t, coordinator.Delegate)

	return coordinator
}

func TestServiceCoordinatorDelegateIntegration(t *testing.T) {
	coordinator := setupTestServiceCoordinator(t)
	ctx := context.Background()

	t.Run("delegate is properly initialized", func(t *testing.T) {
		assert.NotNil(t, coordinator.Delegate)
		
		// Verify core delegate functions are registered
		assert.True(t, coordinator.Delegate.IsRegistered(delegate.LicenseDomain, delegate.LicenseValidateAction))
		assert.True(t, coordinator.Delegate.IsRegistered(delegate.LicenseDomain, delegate.LicenseInvalidateAction))
		assert.True(t, coordinator.Delegate.IsRegistered(delegate.CacheDomain, delegate.CacheGetAction))
		assert.True(t, coordinator.Delegate.IsRegistered(delegate.CacheDomain, delegate.CacheSetAction))
		assert.True(t, coordinator.Delegate.IsRegistered(delegate.CacheDomain, delegate.CacheDeleteAction))
		assert.True(t, coordinator.Delegate.IsRegistered(delegate.MachineDomain, delegate.MachineRegisterAction))
		assert.True(t, coordinator.Delegate.IsRegistered(delegate.CryptoDomain, delegate.CryptoSignAction))
	})

	t.Run("license validation delegate handler", func(t *testing.T) {
		data, err := delegate.NewData(delegate.LicenseDomain, delegate.LicenseValidateAction, map[string]interface{}{
			"license_key":         "TEST-LICENSE-KEY",
			"machine_fingerprint": "test-fingerprint",
		})
		require.NoError(t, err)

		// This should not error even though we don't have real data
		err = coordinator.Delegate.Call(ctx, data)
		require.NoError(t, err)
	})

	t.Run("cache operations delegate handlers", func(t *testing.T) {
		// Test cache set
		setData, err := delegate.NewData(delegate.CacheDomain, delegate.CacheSetAction, map[string]interface{}{
			"key":   "test-key",
			"value": "test-value",
			"ttl":   300,
		})
		require.NoError(t, err)

		err = coordinator.Delegate.Call(ctx, setData)
		require.NoError(t, err)

		// Test cache get
		getData, err := delegate.NewData(delegate.CacheDomain, delegate.CacheGetAction, map[string]interface{}{
			"key": "test-key",
		})
		require.NoError(t, err)

		err = coordinator.Delegate.Call(ctx, getData)
		require.NoError(t, err)

		// Test cache delete
		deleteData, err := delegate.NewData(delegate.CacheDomain, delegate.CacheDeleteAction, map[string]interface{}{
			"key": "test-key",
		})
		require.NoError(t, err)

		err = coordinator.Delegate.Call(ctx, deleteData)
		require.NoError(t, err)
	})

	t.Run("license cache invalidation", func(t *testing.T) {
		licenseKey := "CACHE-TEST-LICENSE"

		// First, set something in cache
		setData, err := delegate.NewData(delegate.CacheDomain, delegate.CacheSetAction, map[string]interface{}{
			"key":   "license:" + licenseKey,
			"value": map[string]interface{}{"valid": true},
			"ttl":   3600,
		})
		require.NoError(t, err)

		err = coordinator.Delegate.Call(ctx, setData)
		require.NoError(t, err)

		// Now invalidate the license cache
		invalidateData, err := delegate.NewData(delegate.LicenseDomain, delegate.LicenseInvalidateAction, map[string]interface{}{
			"license_key": licenseKey,
		})
		require.NoError(t, err)

		err = coordinator.Delegate.Call(ctx, invalidateData)
		require.NoError(t, err)
	})

	t.Run("machine registration delegate handler", func(t *testing.T) {
		data, err := delegate.NewData(delegate.MachineDomain, delegate.MachineRegisterAction, map[string]interface{}{
			"machine_id":   "test-machine-123",
			"fingerprint":  "test-fp-456",
		})
		require.NoError(t, err)

		err = coordinator.Delegate.Call(ctx, data)
		require.NoError(t, err)
	})

	t.Run("crypto operations delegate handlers", func(t *testing.T) {
		// Test crypto sign
		signData, err := delegate.NewData(delegate.CryptoDomain, delegate.CryptoSignAction, map[string]interface{}{
			"data":      []byte("test data to sign"),
			"algorithm": "rsa",
		})
		require.NoError(t, err)

		err = coordinator.Delegate.Call(ctx, signData)
		require.NoError(t, err)

		// Test crypto verify
		verifyData, err := delegate.NewData(delegate.CryptoDomain, delegate.CryptoVerifyAction, map[string]interface{}{
			"data":      []byte("test data"),
			"signature": []byte("test signature"),
			"algorithm": "rsa",
		})
		require.NoError(t, err)

		err = coordinator.Delegate.Call(ctx, verifyData)
		require.NoError(t, err)

		// Test crypto encrypt
		encryptData, err := delegate.NewData(delegate.CryptoDomain, delegate.CryptoEncryptAction, map[string]interface{}{
			"data":      []byte("secret data"),
			"algorithm": "aes",
		})
		require.NoError(t, err)

		err = coordinator.Delegate.Call(ctx, encryptData)
		require.NoError(t, err)

		// Test crypto decrypt
		decryptData, err := delegate.NewData(delegate.CryptoDomain, delegate.CryptoDecryptAction, map[string]interface{}{
			"data":      []byte("encrypted data"),
			"algorithm": "aes",
		})
		require.NoError(t, err)

		err = coordinator.Delegate.Call(ctx, decryptData)
		require.NoError(t, err)
	})
}

func TestDelegateHelperFunctionsWithCoordinator(t *testing.T) {
	coordinator := setupTestServiceCoordinator(t)
	ctx := context.Background()

	t.Run("helper functions work with real coordinator", func(t *testing.T) {
		// Test license validation helper
		err := delegate.CallLicenseValidation(ctx, coordinator.Delegate, "HELPER-TEST-LICENSE", "helper-test-fp")
		require.NoError(t, err)

		// Test cache operation helper
		err = delegate.CallCacheOperation(ctx, coordinator.Delegate, delegate.CacheSetAction, "helper-test-key", "helper-test-value")
		require.NoError(t, err)

		// Test machine registration helper
		testUUID := uuid.MustParse("12345678-1234-1234-1234-123456789012")
		err = delegate.CallMachineRegistration(ctx, coordinator.Delegate, testUUID, "helper-machine-fp")
		require.NoError(t, err)

		// Test metrics helper
		err = delegate.CallMetricsRecord(ctx, coordinator.Delegate, "helper_test_metric", 1.0, map[string]string{"test": "helper"})
		require.NoError(t, err)
	})
}

func TestDelegateErrorHandling(t *testing.T) {
	coordinator := setupTestServiceCoordinator(t)
	ctx := context.Background()

	t.Run("invalid parameters are handled gracefully", func(t *testing.T) {
		// Test with invalid JSON data
		data := delegate.Data{
			Domain:    delegate.LicenseDomain,
			Action:    delegate.LicenseValidateAction,
			RawParams: []byte("invalid json {"),
		}

		// Should not panic, should handle error gracefully
		err := coordinator.Delegate.Call(ctx, data)
		require.NoError(t, err) // Call doesn't propagate errors

		// CallSync should propagate the error
		err = coordinator.Delegate.CallSync(ctx, data)
		require.Error(t, err)
	})

	t.Run("unregistered domain/action combinations", func(t *testing.T) {
		data, err := delegate.NewData("nonexistent", "action", nil)
		require.NoError(t, err)

		// Should not error for unregistered combinations
		err = coordinator.Delegate.Call(ctx, data)
		require.NoError(t, err)

		err = coordinator.Delegate.CallSync(ctx, data)
		require.NoError(t, err)
	})
}

func TestRealCacheIntegration(t *testing.T) {
	coordinator := setupTestServiceCoordinator(t)
	ctx := context.Background()

	t.Run("delegate integrates with real cache", func(t *testing.T) {
		// The coordinator should be using either memory cache or Valkey
		assert.NotNil(t, coordinator.Cache)
		
		cacheType := coordinator.GetCacheType()
		assert.Contains(t, []string{"memory", "valkey"}, cacheType)

		// Test cache operations through delegate
		testKey := "delegate-integration-test"
		testValue := map[string]interface{}{
			"test":      true,
			"timestamp": time.Now().Unix(),
		}

		// Set value via delegate
		setData, err := delegate.NewData(delegate.CacheDomain, delegate.CacheSetAction, map[string]interface{}{
			"key":   testKey,
			"value": testValue,
			"ttl":   300,
		})
		require.NoError(t, err)

		err = coordinator.Delegate.Call(ctx, setData)
		require.NoError(t, err)

		// Verify value was actually set in cache
		// Note: We can't easily verify this without accessing the cache directly
		// In a real scenario, you would verify through a cache get operation

		// Get value via delegate
		getData, err := delegate.NewData(delegate.CacheDomain, delegate.CacheGetAction, map[string]interface{}{
			"key": testKey,
		})
		require.NoError(t, err)

		err = coordinator.Delegate.Call(ctx, getData)
		require.NoError(t, err)

		// Delete value via delegate
		deleteData, err := delegate.NewData(delegate.CacheDomain, delegate.CacheDeleteAction, map[string]interface{}{
			"key": testKey,
		})
		require.NoError(t, err)

		err = coordinator.Delegate.Call(ctx, deleteData)
		require.NoError(t, err)
	})
}

func TestConcurrentDelegateAccess(t *testing.T) {
	coordinator := setupTestServiceCoordinator(t)
	ctx := context.Background()

	t.Run("concurrent delegate operations", func(t *testing.T) {
		numOperations := 20
		done := make(chan bool, numOperations)

		// Launch concurrent operations
		for i := 0; i < numOperations; i++ {
			go func(index int) {
				defer func() { done <- true }()

				// Perform various delegate operations
				operations := []func() error{
					func() error {
						data, err := delegate.NewData(delegate.LicenseDomain, delegate.LicenseValidateAction, map[string]interface{}{
							"license_key":         "CONCURRENT-LICENSE",
							"machine_fingerprint": "concurrent-fp",
						})
						if err != nil {
							return err
						}
						return coordinator.Delegate.Call(ctx, data)
					},
					func() error {
						data, err := delegate.NewData(delegate.CacheDomain, delegate.CacheSetAction, map[string]interface{}{
							"key":   "concurrent-key",
							"value": "concurrent-value",
							"ttl":   300,
						})
						if err != nil {
							return err
						}
						return coordinator.Delegate.Call(ctx, data)
					},
				}

				// Execute random operation
				op := operations[index%len(operations)]
				err := op()
				assert.NoError(t, err)
			}(i)
		}

		// Wait for all operations to complete
		for i := 0; i < numOperations; i++ {
			<-done
		}
	})
}


func TestDelegateRegistryInspection(t *testing.T) {
	coordinator := setupTestServiceCoordinator(t)

	t.Run("inspect registered delegate functions", func(t *testing.T) {
		registered := coordinator.Delegate.ListRegistered()
		
		// Should have license domain functions
		assert.Contains(t, registered[delegate.LicenseDomain], delegate.LicenseValidateAction)
		assert.Contains(t, registered[delegate.LicenseDomain], delegate.LicenseInvalidateAction)
		
		// Should have cache domain functions
		assert.Contains(t, registered[delegate.CacheDomain], delegate.CacheGetAction)
		assert.Contains(t, registered[delegate.CacheDomain], delegate.CacheSetAction)
		assert.Contains(t, registered[delegate.CacheDomain], delegate.CacheDeleteAction)
		
		// Should have machine domain functions
		assert.Contains(t, registered[delegate.MachineDomain], delegate.MachineRegisterAction)
		
		// Should have crypto domain functions
		assert.Contains(t, registered[delegate.CryptoDomain], delegate.CryptoSignAction)
		assert.Contains(t, registered[delegate.CryptoDomain], delegate.CryptoVerifyAction)
		assert.Contains(t, registered[delegate.CryptoDomain], delegate.CryptoEncryptAction)
		assert.Contains(t, registered[delegate.CryptoDomain], delegate.CryptoDecryptAction)
		
		t.Logf("Registered delegate functions: %+v", registered)
	})
}