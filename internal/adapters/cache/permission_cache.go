package cache

import (
	"sync"
	"time"
)

// SimplePermission<PERSON>ache provides in-memory caching for permissions
type SimplePermissionCache struct {
	mu    sync.RWMutex
	data  map[string]cacheEntry
	ttl   time.Duration
}

type cacheEntry struct {
	permissions []string
	expiresAt   time.Time
}

// NewSimplePermissionCache creates a new permission cache
func NewSimplePermissionCache(ttl time.Duration) *SimplePermissionCache {
	cache := &SimplePermissionCache{
		data: make(map[string]cacheEntry),
		ttl:  ttl,
	}
	
	// Start cleanup goroutine
	go cache.cleanup()
	
	return cache
}

// GetUserPermissions retrieves cached permissions for a user
func (c *SimplePermissionCache) GetUserPermissions(userID string) ([]string, bool) {
	c.mu.RLock()
	defer c.mu.RUnlock()
	
	entry, exists := c.data[userID]
	if !exists || time.Now().After(entry.expiresAt) {
		return nil, false
	}
	
	return entry.permissions, true
}

// SetUserPermissions caches permissions for a user
func (c *SimplePermissionCache) SetUserPermissions(userID string, permissions []string, ttl time.Duration) {
	c.mu.Lock()
	defer c.mu.Unlock()
	
	expiry := ttl
	if expiry == 0 {
		expiry = c.ttl
	}
	
	c.data[userID] = cacheEntry{
		permissions: permissions,
		expiresAt:   time.Now().Add(expiry),
	}
}

// InvalidateUser removes a user from cache
func (c *SimplePermissionCache) InvalidateUser(userID string) {
	c.mu.Lock()
	defer c.mu.Unlock()
	
	delete(c.data, userID)
}

// InvalidateAll clears the entire cache
func (c *SimplePermissionCache) InvalidateAll() {
	c.mu.Lock()
	defer c.mu.Unlock()
	
	c.data = make(map[string]cacheEntry)
}

// cleanup removes expired entries periodically
func (c *SimplePermissionCache) cleanup() {
	ticker := time.NewTicker(c.ttl)
	defer ticker.Stop()
	
	for range ticker.C {
		c.mu.Lock()
		now := time.Now()
		
		for key, entry := range c.data {
			if now.After(entry.expiresAt) {
				delete(c.data, key)
			}
		}
		
		c.mu.Unlock()
	}
}