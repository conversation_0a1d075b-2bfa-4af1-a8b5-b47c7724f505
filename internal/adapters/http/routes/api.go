package routes

import (
	"time"

	"github.com/gin-gonic/gin"
	"github.com/gokeys/gokeys/internal/adapters/http/handlers"
	"github.com/gokeys/gokeys/internal/adapters/http/middleware"
	"github.com/gokeys/gokeys/internal/domain/services/auth"
)

// APIRoutes manages all API route configurations with clean architecture
type APIRoutes struct {
	// Core services
	authService *auth.AuthService
	
	// Middleware
	authMW  *middleware.AuthenticationMiddleware
	authzMW *middleware.AuthorizationMiddleware
	
	// Handlers
	authHandler       *handlers.AuthHandler
	userHandler       *handlers.UserHandler
	orgHandler        *handlers.OrganizationHandler
	productHandler    *handlers.ProductHandler
	licenseHandler    *handlers.LicenseHandler
	policyHandler     *handlers.PolicyHandler
	machineHandler    *handlers.MachineHandler
	permissionHandler *handlers.PermissionHandler
}

// NewAPIRoutes creates a new API routes instance
func NewAPIRoutes(
	authService *auth.AuthService,
	authMW *middleware.AuthenticationMiddleware,
	authzMW *middleware.AuthorizationMiddleware,
	authHandler *handlers.AuthHandler,
	userHandler *handlers.UserHandler,
	orgHandler *handlers.OrganizationHandler,
	productHandler *handlers.ProductHandler,
	licenseHandler *handlers.LicenseHandler,
	policyHandler *handlers.PolicyHandler,
	machineHandler *handlers.MachineHandler,
	permissionHandler *handlers.PermissionHandler,
) *APIRoutes {
	return &APIRoutes{
		authService:       authService,
		authMW:            authMW,
		authzMW:           authzMW,
		authHandler:       authHandler,
		userHandler:       userHandler,
		orgHandler:        orgHandler,
		productHandler:    productHandler,
		licenseHandler:    licenseHandler,
		policyHandler:     policyHandler,
		machineHandler:    machineHandler,
		permissionHandler: permissionHandler,
	}
}

// SetupRoutes configures all API routes with clean architecture
func (r *APIRoutes) SetupRoutes(router *gin.Engine) {
	// Global middleware
	router.Use(gin.Logger())
	router.Use(gin.Recovery())
	router.Use(r.withCORS())
	
	// API v1 group
	v1 := router.Group("/api/v1")
	{
		// Public routes (no authentication required)
		r.setupPublicRoutes(v1)
		
		// Protected routes (authentication + authorization required)
		protected := v1.Group("")
		protected.Use(r.authMW.RequireAuthentication())
		protected.Use(r.authzMW.PreloadUserPermissions()) // Preload for performance
		{
			r.setupAuthRoutes(protected)
			r.setupUserRoutes(protected)
			r.setupOrganizationRoutes(protected)
			
			// Global resource routes (cross-organizations, for system admin)
			r.setupGlobalResourceRoutes(protected)
		}
		
		// System admin routes
		admin := v1.Group("/admin")
		admin.Use(r.authMW.RequireAuthentication())
		admin.Use(r.authzMW.RequireSystemAdmin())
		{
			r.setupAdminRoutes(admin)
		}
	}
}

// setupPublicRoutes configures routes accessible without authentication
func (r *APIRoutes) setupPublicRoutes(rg *gin.RouterGroup) {
	// Authentication endpoints
	auth := rg.Group("/auth")
	{
		auth.POST("/login", r.authHandler.Login)
	}
	
	// License validation (public for end-user software)
	licenses := rg.Group("/licenses")
	{
		// License key authentication for validation
		licenses.POST("/validate", r.licenseHandler.ValidateLicense)
		licenses.GET("/validate/:key", r.licenseHandler.ValidateLicense)
	}
	
	// Machine operations (public for registered machines)
	machines := rg.Group("/machines")
	{
		machines.POST("/heartbeat", r.machineHandler.UpdateHeartbeat)
	}
}

// setupAuthRoutes configures authentication management routes
func (r *APIRoutes) setupAuthRoutes(rg *gin.RouterGroup) {
	auth := rg.Group("/auth")
	{
		// Session management
		auth.POST("/logout", r.authHandler.Logout)
		auth.POST("/refresh", r.authHandler.RefreshToken)
		auth.GET("/profile", r.authHandler.GetProfile)
		
		// API token management (user can manage own tokens)
		tokens := auth.Group("/tokens")
		{
			tokens.GET("", r.authHandler.GetAPITokens)
			tokens.POST("", r.authHandler.GenerateAPIToken)
			tokens.DELETE("/:id", r.authHandler.RevokeAPIToken)
		}
	}
}

// setupUserRoutes configures user management routes
func (r *APIRoutes) setupUserRoutes(rg *gin.RouterGroup) {
	users := rg.Group("/users")
	{
		// List users - requires read permission
		users.GET("", 
			r.authzMW.RequirePermission("user", "read", nil),
			r.userHandler.GetUsers)
		
		// Create user - requires create permission  
		users.POST("",
			r.authzMW.RequirePermission("user", "create", nil),
			r.userHandler.CreateUser)
		
		// Get specific user - resource-level permission check
		users.GET("/:id",
			r.authzMW.RequirePermission("user", "read", middleware.GetResourceIDFromParam("id")),
			r.userHandler.GetUser)
		
		// Update user - resource-level permission check
		users.PUT("/:id",
			r.authzMW.RequirePermission("user", "update", middleware.GetResourceIDFromParam("id")),
			r.userHandler.UpdateUser)
		
		// Delete user - requires delete permission
		users.DELETE("/:id",
			r.authzMW.RequirePermission("user", "delete", middleware.GetResourceIDFromParam("id")),
			r.userHandler.DeleteUser)
	}
}

// setupOrganizationRoutes configures organization management routes with nested resources
func (r *APIRoutes) setupOrganizationRoutes(rg *gin.RouterGroup) {
	orgs := rg.Group("/organizations")
	{
		// Organization CRUD
		orgs.GET("",
			r.authzMW.RequirePermission("organization", "read", nil),
			r.orgHandler.GetOrganizations)
		
		orgs.POST("",
			r.authzMW.RequirePermission("organization", "create", nil),
			r.orgHandler.CreateOrganization)
		
		orgs.GET("/:id",
			r.authzMW.RequirePermission("organization", "read", middleware.GetResourceIDFromParam("id")),
			r.orgHandler.GetOrganization)
		
		orgs.PUT("/:id",
			r.authzMW.RequirePermission("organization", "update", middleware.GetResourceIDFromParam("id")),
			r.orgHandler.UpdateOrganization)
		
		orgs.DELETE("/:id",
			r.authzMW.RequirePermission("organization", "delete", middleware.GetResourceIDFromParam("id")),
			r.orgHandler.DeleteOrganization)
		
		// Nested resources within organization
		// RESTful pattern: /organizations/:org_id/products
		orgScope := orgs.Group("/:org_id")
		{
			r.setupOrganizationProductRoutes(orgScope)
			r.setupOrganizationPolicyRoutes(orgScope)
			r.setupOrganizationLicenseRoutes(orgScope)
			r.setupOrganizationMachineRoutes(orgScope)
		}
	}
}

// setupOrganizationProductRoutes configures nested product routes within organizations
// Pattern: /organizations/:org_id/products
func (r *APIRoutes) setupOrganizationProductRoutes(orgScope *gin.RouterGroup) {
	products := orgScope.Group("/products")
	{
		// List products in this organization
		// GET /organizations/:org_id/products
		products.GET("",
			r.RequireOrganizationScopedPermission("product", "read", nil),
			r.productHandler.GetProducts)
		
		// Create product in this organization
		// POST /organizations/:org_id/products
		products.POST("",
			r.RequireOrganizationScopedPermission("product", "create", nil),
			r.productHandler.CreateProduct)
		
		// Get specific product in this organization
		// GET /organizations/:org_id/products/:id
		products.GET("/:id",
			r.RequireOrganizationScopedPermission("product", "read", GetOrgResourceID("id")),
			r.productHandler.GetProduct)
		
		// Update specific product in this organization
		// PUT /organizations/:org_id/products/:id
		products.PUT("/:id",
			r.RequireOrganizationScopedPermission("product", "update", GetOrgResourceID("id")),
			r.productHandler.UpdateProduct)
		
		// Delete specific product in this organization
		// DELETE /organizations/:org_id/products/:id
		products.DELETE("/:id",
			r.RequireOrganizationScopedPermission("product", "delete", GetOrgResourceID("id")),
			r.productHandler.DeleteProduct)
	}
}

// setupOrganizationPolicyRoutes configures nested policy routes within organizations
// Pattern: /organizations/:org_id/policies
func (r *APIRoutes) setupOrganizationPolicyRoutes(orgScope *gin.RouterGroup) {
	policies := orgScope.Group("/policies")
	{
		// List policies in this organization
		// GET /organizations/:org_id/policies
		policies.GET("",
			r.RequireOrganizationScopedPermission("policy", "read", nil),
			r.policyHandler.GetPolicies)
		
		// Create policy in this organization
		// POST /organizations/:org_id/policies
		policies.POST("",
			r.RequireOrganizationScopedPermission("policy", "create", nil),
			r.policyHandler.CreatePolicy)
		
		// Get specific policy in this organization
		// GET /organizations/:org_id/policies/:id
		policies.GET("/:id",
			r.RequireOrganizationScopedPermission("policy", "read", GetOrgResourceID("id")),
			r.policyHandler.GetPolicy)
		
		// Update specific policy in this organization
		// PUT /organizations/:org_id/policies/:id
		policies.PUT("/:id",
			r.RequireOrganizationScopedPermission("policy", "update", GetOrgResourceID("id")),
			r.policyHandler.UpdatePolicy)
		
		// Delete specific policy in this organization
		// DELETE /organizations/:org_id/policies/:id
		policies.DELETE("/:id",
			r.RequireOrganizationScopedPermission("policy", "delete", GetOrgResourceID("id")),
			r.policyHandler.DeletePolicy)
	}
}

// setupOrganizationLicenseRoutes configures nested license routes within organizations
// Pattern: /organizations/:org_id/licenses
func (r *APIRoutes) setupOrganizationLicenseRoutes(orgScope *gin.RouterGroup) {
	licenses := orgScope.Group("/licenses")
	{
		// List licenses in this organization
		// GET /organizations/:org_id/licenses
		licenses.GET("",
			r.RequireOrganizationScopedPermission("license", "read", nil),
			r.licenseHandler.GetLicenses)
		
		// Create license in this organization
		// POST /organizations/:org_id/licenses
		licenses.POST("",
			r.RequireOrganizationScopedPermission("license", "create", nil),
			r.licenseHandler.CreateLicense)
		
		// Get specific license in this organization
		// GET /organizations/:org_id/licenses/:id
		licenses.GET("/:id",
			r.RequireOrganizationScopedPermission("license", "read", GetOrgResourceID("id")),
			r.licenseHandler.GetLicense)
		
		// Update specific license in this organization
		// PUT /organizations/:org_id/licenses/:id
		licenses.PUT("/:id",
			r.RequireOrganizationScopedPermission("license", "update", GetOrgResourceID("id")),
			r.licenseHandler.UpdateLicense)
		
		// Delete specific license in this organization
		// DELETE /organizations/:org_id/licenses/:id
		licenses.DELETE("/:id",
			r.RequireOrganizationScopedPermission("license", "delete", GetOrgResourceID("id")),
			r.licenseHandler.DeleteLicense)
		
		// License actions within organization
		// Pattern: /organizations/:org_id/licenses/:id/actions
		actions := licenses.Group("/:id/actions")
		{
			actions.POST("/validate",
				r.RequireOrganizationScopedPermission("license", "validate", GetOrgResourceID("id")),
				r.licenseHandler.ValidateLicense)
			
			actions.POST("/suspend",
				r.RequireOrganizationScopedPermission("license", "update", GetOrgResourceID("id")),
				r.licenseHandler.SuspendLicense)
			
			actions.POST("/reinstate",
				r.RequireOrganizationScopedPermission("license", "update", GetOrgResourceID("id")),
				r.licenseHandler.ReinstateLicense)
			
			actions.POST("/renew",
				r.RequireOrganizationScopedPermission("license", "update", GetOrgResourceID("id")),
				r.licenseHandler.RenewLicense)
		}
	}
}

// setupOrganizationMachineRoutes configures nested machine routes within organizations
// Pattern: /organizations/:org_id/machines
func (r *APIRoutes) setupOrganizationMachineRoutes(orgScope *gin.RouterGroup) {
	machines := orgScope.Group("/machines")
	{
		// List machines in this organization
		// GET /organizations/:org_id/machines
		machines.GET("",
			r.RequireOrganizationScopedPermission("machine", "read", nil),
			r.machineHandler.GetMachines)
		
		// Create machine in this organization
		// POST /organizations/:org_id/machines
		machines.POST("",
			r.RequireOrganizationScopedPermission("machine", "create", nil),
			r.machineHandler.CreateMachine)
		
		// Get specific machine in this organization
		// GET /organizations/:org_id/machines/:id
		machines.GET("/:id",
			r.RequireOrganizationScopedPermission("machine", "read", GetOrgResourceID("id")),
			r.machineHandler.GetMachine)
		
		// Update specific machine in this organization
		// PUT /organizations/:org_id/machines/:id
		machines.PUT("/:id",
			r.RequireOrganizationScopedPermission("machine", "update", GetOrgResourceID("id")),
			r.machineHandler.UpdateMachine)
		
		// Delete specific machine in this organization
		// DELETE /organizations/:org_id/machines/:id
		machines.DELETE("/:id",
			r.RequireOrganizationScopedPermission("machine", "delete", GetOrgResourceID("id")),
			r.machineHandler.DeleteMachine)
		
		// Machine actions within organization
		// Pattern: /organizations/:org_id/machines/:id/actions
		actions := machines.Group("/:id/actions")
		{
			actions.POST("/heartbeat",
				r.RequireOrganizationScopedPermission("machine", "update", GetOrgResourceID("id")),
				r.machineHandler.UpdateHeartbeat)
		}
	}
}

// setupGlobalResourceRoutes configures global resource routes for system admins
// These routes allow cross-organization access for system administrators
func (r *APIRoutes) setupGlobalResourceRoutes(rg *gin.RouterGroup) {
	// Global products access (system admin only)
	products := rg.Group("/products")
	{
		products.GET("",
			r.authzMW.RequirePermission("product", "read", nil),
			r.productHandler.GetProducts)
		
		products.GET("/:id",
			r.authzMW.RequirePermission("product", "read", middleware.GetResourceIDFromParam("id")),
			r.productHandler.GetProduct)
	}
	
	// Global licenses access (system admin only)
	licenses := rg.Group("/licenses")
	{
		licenses.GET("",
			r.authzMW.RequirePermission("license", "read", nil),
			r.licenseHandler.GetLicenses)
		
		licenses.GET("/:id",
			r.authzMW.RequirePermission("license", "read", middleware.GetResourceIDFromParam("id")),
			r.licenseHandler.GetLicense)
	}
	
	// Global machines access (system admin only)
	machines := rg.Group("/machines")
	{
		machines.GET("",
			r.authzMW.RequirePermission("machine", "read", nil),
			r.machineHandler.GetMachines)
		
		machines.GET("/:id",
			r.authzMW.RequirePermission("machine", "read", middleware.GetResourceIDFromParam("id")),
			r.machineHandler.GetMachine)
	}
}

// setupAdminRoutes configures system administration routes
func (r *APIRoutes) setupAdminRoutes(rg *gin.RouterGroup) {
	// System health and monitoring
	rg.GET("/health", r.healthHandler)
	rg.GET("/metrics", r.metricsHandler)
	
	// Permission management (comprehensive)
	r.setupPermissionManagementRoutes(rg)
}

// setupPermissionManagementRoutes configures permission management routes
func (r *APIRoutes) setupPermissionManagementRoutes(rg *gin.RouterGroup) {
	permissions := rg.Group("/permissions")
	{
		// Permission granting
		permissions.POST("/grant", r.permissionHandler.GrantPermission)
		permissions.POST("/batch-grant", r.permissionHandler.BatchGrantPermissions)
		
		// Permission revoking
		permissions.DELETE("/revoke", r.permissionHandler.RevokePermission)
		permissions.DELETE("/revoke-specific", r.permissionHandler.RevokeSpecificPermission)
		permissions.DELETE("/users/:user_id/revoke-all", r.permissionHandler.RevokeAllUserPermissions)
		
		// Permission checking and listing
		permissions.POST("/check", r.permissionHandler.CheckPermission)
		permissions.GET("", r.permissionHandler.ListPermissions)
		permissions.GET("/users/:user_id", r.permissionHandler.GetUserPermissions)
		
		// Statistics and monitoring
		permissions.GET("/stats", r.permissionHandler.GetPermissionStats)
	}
}

// ==================== HELPER METHODS ====================

// RequireOrganizationScopedPermission creates middleware for nested organization resources
// Sử dụng org_id từ URL parameter để validate organization scope
func (r *APIRoutes) RequireOrganizationScopedPermission(resourceType, action string, getResourceID func(*gin.Context) string) gin.HandlerFunc {
	return gin.HandlerFunc(func(c *gin.Context) {
		// Extract org_id from URL path 
		orgID := c.Param("org_id")
		if orgID == "" {
			c.JSON(400, gin.H{"error": "Organization ID required"})
			c.Abort()
			return
		}
		
		// Validate user has access to this organization
		// This is handled automatically by the authorization middleware through hierarchy:
		// 1. System admin (S:resource:action) - access all
		// 2. Org admin (O:org-id:resource:action) - access only this org
		// 3. Resource-specific (R:resource:action) - check resource_ids
		
		// Store org_id in context for handlers to use
		c.Set("organization_id", orgID)
		
		// Call the standard permission check
		r.authzMW.RequirePermission(resourceType, action, getResourceID)(c)
	})
}

// GetOrgResourceID creates function to get resource ID from nested route
// Pattern: /organizations/:org_id/products/:id -> returns product ID
func GetOrgResourceID(paramName string) func(*gin.Context) string {
	return middleware.GetResourceIDFromParam(paramName)
}

// RequireOrganizationPermission creates middleware for organization-scoped resources
// 
// Sử dụng cho resources thuộc organization (products, licenses, policies, machines)
// 
// Permission Hierarchy Logic:
// 1. S:resource:action - System admin có quyền trên tất cả resources
// 2. O:org-uuid:resource:action - Org admin có quyền trên tất cả resources trong org đó
// 3. R:resource:action - Resource-specific permissions với resource_ids constraints
//
// Ví dụ:
// - User có "S:product:read" -> có thể read tất cả products (system admin)
// - User có "O:org-123:product:read" -> có thể read tất cả products trong org-123  
// - User có "R:product:read" với resource_ids=["prod-1","prod-2"] -> chỉ read được prod-1, prod-2
// - User có "R:product:read" với resource_ids=[] (empty) -> wildcard, read được tất cả products họ có quyền
//
// Authorization middleware sẽ tự động check theo hierarchy này và stop khi tìm thấy permission đầu tiên
func (r *APIRoutes) RequireOrganizationPermission(resourceType, action string, getResourceID func(*gin.Context) string) gin.HandlerFunc {
	// The existing RequirePermission already handles organization hierarchy automatically
	// through buildPermissionScopes() which includes:
	// - System scope (S)
	// - Organization scope from current user context (O:uuid) 
	// - Resource scope (R)
	return r.authzMW.RequirePermission(resourceType, action, getResourceID)
}

// withCORS applies CORS headers to routes
func (r *APIRoutes) withCORS() gin.HandlerFunc {
	return gin.HandlerFunc(func(c *gin.Context) {
		c.Header("Access-Control-Allow-Origin", "*")
		c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Content-Type, Authorization")
		
		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}
		
		c.Next()
	})
}

// Simple health check handler
func (r *APIRoutes) healthHandler(c *gin.Context) {
	c.JSON(200, gin.H{
		"status": "healthy",
		"service": "gokeys-api",
		"timestamp": time.Now().Unix(),
	})
}

// Simple metrics handler
func (r *APIRoutes) metricsHandler(c *gin.Context) {
	c.JSON(200, gin.H{
		"message": "Metrics endpoint - TODO: implement Prometheus integration",
	})
}
