package dto

import (
	"time"

	"github.com/gokeys/gokeys/internal/domain/entities"
	"github.com/google/uuid"
)

// ==================== REQUEST DTOs ====================

// GrantPermissionRequest represents a request to grant permissions to a user
type GrantPermissionRequest struct {
	UserID       string    `json:"user_id" binding:"required,uuid" example:"550e8400-e29b-41d4-a716-************"`
	Scope        string    `json:"scope" binding:"required" example:"O:550e8400-e29b-41d4-a716-************"`
	ResourceType string    `json:"resource_type" binding:"required" example:"license"`
	Actions      []string  `json:"actions" binding:"required,min=1" example:"read,update"`
	ResourceIDs  []string  `json:"resource_ids,omitempty" example:"license-123,license-456"`
	ExpiresAt    *string   `json:"expires_at,omitempty" example:"2024-12-31T23:59:59Z"`
	Reason       string    `json:"reason,omitempty" example:"Granting license management permissions for Q4 2024"`
}

// BatchGrantPermissionRequest represents a request to grant multiple permissions efficiently
type BatchGrantPermissionRequest struct {
	UserID  string              `json:"user_id" binding:"required,uuid" example:"550e8400-e29b-41d4-a716-************"`
	Grants  []PermissionGrant   `json:"grants" binding:"required,min=1"`
	Reason  string              `json:"reason,omitempty" example:"Initial role setup for new team member"`
}

// PermissionGrant represents a single permission grant in a batch operation
type PermissionGrant struct {
	Scope        string   `json:"scope" binding:"required" example:"O:550e8400-e29b-41d4-a716-************"`
	ResourceType string   `json:"resource_type" binding:"required" example:"license"`
	Actions      []string `json:"actions" binding:"required,min=1" example:"read,update"`
	ResourceIDs  []string `json:"resource_ids,omitempty" example:"license-123,license-456"`
	ExpiresAt    *string  `json:"expires_at,omitempty" example:"2024-12-31T23:59:59Z"`
}

// RevokePermissionRequest represents a request to revoke permissions
type RevokePermissionRequest struct {
	UserID       string `json:"user_id" binding:"required,uuid" example:"550e8400-e29b-41d4-a716-************"`
	Scope        string `json:"scope" binding:"required" example:"O:550e8400-e29b-41d4-a716-************"`
	ResourceType string `json:"resource_type" binding:"required" example:"license"`
	Reason       string `json:"reason,omitempty" example:"User left the organization"`
}

// RevokeSpecificPermissionRequest represents a request to revoke a specific permission key
type RevokeSpecificPermissionRequest struct {
	UserID        string `json:"user_id" binding:"required,uuid" example:"550e8400-e29b-41d4-a716-************"`
	PermissionKey string `json:"permission_key" binding:"required" example:"O:550e8400-e29b-41d4-a716-************:license:read"`
	Reason        string `json:"reason,omitempty" example:"Removing specific read access"`
}

// CheckPermissionRequest represents a request to check if user has specific permission
type CheckPermissionRequest struct {
	UserID       string `json:"user_id" binding:"required,uuid" example:"550e8400-e29b-41d4-a716-************"`
	Scope        string `json:"scope" binding:"required" example:"O:550e8400-e29b-41d4-a716-************"`
	ResourceType string `json:"resource_type" binding:"required" example:"license"`
	Action       string `json:"action" binding:"required" example:"read"`
	ResourceID   string `json:"resource_id,omitempty" example:"license-123"`
}

// ListPermissionsRequest represents a request to list permissions with filters
type ListPermissionsRequest struct {
	UserID         string `form:"user_id" example:"550e8400-e29b-41d4-a716-************"`
	ScopePattern   string `form:"scope_pattern" example:"O:*"`
	ResourceType   string `form:"resource_type" example:"license"`
	Action         string `form:"action" example:"read"`
	IncludeExpired bool   `form:"include_expired" example:"false"`
	Page           int    `form:"page" example:"1"`
	PageSize       int    `form:"page_size" example:"20"`
}

// ==================== RESPONSE DTOs ====================

// PermissionResponse represents a permission in API responses
type PermissionResponse struct {
	ID            string    `json:"id" example:"550e8400-e29b-41d4-a716-************"`
	UserID        string    `json:"user_id" example:"550e8400-e29b-41d4-a716-************"`
	PermissionKey string    `json:"permission_key" example:"O:550e8400-e29b-41d4-a716-************:license:read"`
	Scope         string    `json:"scope" example:"O:550e8400-e29b-41d4-a716-************"`
	ResourceType  string    `json:"resource_type" example:"license"`
	Action        string    `json:"action" example:"read"`
	ResourceIDs   []string  `json:"resource_ids,omitempty" example:"license-123,license-456"`
	GrantedBy     *string   `json:"granted_by,omitempty" example:"550e8400-e29b-41d4-a716-446655440002"`
	GrantedAt     time.Time `json:"granted_at" example:"2024-01-15T10:30:00Z"`
	ExpiresAt     *string   `json:"expires_at,omitempty" example:"2024-12-31T23:59:59Z"`
	IsActive      bool      `json:"is_active" example:"true"`
	CreatedAt     time.Time `json:"created_at" example:"2024-01-15T10:30:00Z"`
	UpdatedAt     time.Time `json:"updated_at" example:"2024-01-15T10:30:00Z"`
}

// ListPermissionsResponse represents a paginated list of permissions
type ListPermissionsResponse struct {
	Permissions []PermissionResponse `json:"permissions"`
	Pagination  PaginationResponse   `json:"pagination"`
}

// PaginationResponse represents pagination metadata
type PaginationResponse struct {
	Page       int   `json:"page" example:"1"`
	PageSize   int   `json:"page_size" example:"20"`
	Total      int64 `json:"total" example:"150"`
	TotalPages int   `json:"total_pages" example:"8"`
}

// GrantPermissionResponse represents response after granting permissions
type GrantPermissionResponse struct {
	Message      string               `json:"message" example:"Permissions granted successfully"`
	Permissions  []PermissionResponse `json:"permissions"`
	GrantedCount int                  `json:"granted_count" example:"3"`
}

// RevokePermissionResponse represents response after revoking permissions
type RevokePermissionResponse struct {
	Message      string `json:"message" example:"Permissions revoked successfully"`
	RevokedCount int64  `json:"revoked_count" example:"5"`
}

// CheckPermissionResponse represents response for permission check
type CheckPermissionResponse struct {
	HasPermission    bool                  `json:"has_permission" example:"true"`
	MatchedPermission *PermissionResponse  `json:"matched_permission,omitempty"`
	CheckedScopes    []string             `json:"checked_scopes" example:"O:550e8400-e29b-41d4-a716-************,S"`
	CheckedKeys      []string             `json:"checked_keys" example:"O:550e8400-e29b-41d4-a716-************:license:read,S:license:read"`
}

// PermissionStatsResponse represents permission statistics
type PermissionStatsResponse struct {
	TotalPermissions     int64                    `json:"total_permissions" example:"1250"`
	ActivePermissions    int64                    `json:"active_permissions" example:"1180"`
	ExpiredPermissions   int64                    `json:"expired_permissions" example:"70"`
	UsersWithPermissions int64                    `json:"users_with_permissions" example:"45"`
	ScopeBreakdown       map[string]int64         `json:"scope_breakdown" example:"{\"system\":5,\"organization\":1000,\"resource\":175}"`
	ResourceBreakdown    map[string]int64         `json:"resource_breakdown" example:"{\"license\":800,\"product\":200,\"user\":150}"`
	ActionBreakdown      map[string]int64         `json:"action_breakdown" example:"{\"read\":600,\"update\":400,\"delete\":180}"`
	ExpiringIn30Days     int64                    `json:"expiring_in_30_days" example:"25"`
}

// UserPermissionSummaryResponse represents a summary of user's permissions
type UserPermissionSummaryResponse struct {
	UserID              string                   `json:"user_id" example:"550e8400-e29b-41d4-a716-************"`
	TotalPermissions    int                      `json:"total_permissions" example:"15"`
	SystemPermissions   int                      `json:"system_permissions" example:"2"`
	OrgPermissions      int                      `json:"org_permissions" example:"10"`
	ResourcePermissions int                      `json:"resource_permissions" example:"3"`
	ExpiringPermissions int                      `json:"expiring_permissions" example:"1"`
	Permissions         []PermissionResponse     `json:"permissions"`
	Organizations       []string                 `json:"organizations" example:"550e8400-e29b-41d4-a716-************,550e8400-e29b-41d4-a716-446655440002"`
	EffectiveScopes     []string                 `json:"effective_scopes" example:"S,O:550e8400-e29b-41d4-a716-************,R"`
}

// ==================== UTILITY FUNCTIONS ====================

// ToPermissionResponse converts a domain Permission entity to PermissionResponse DTO
func ToPermissionResponse(permission *entities.Permission) PermissionResponse {
	scope, resourceType, action := ParsePermissionKey(permission.PermissionKey)
	
	var grantedBy *string
	if permission.GrantedBy != nil {
		grantedByStr := permission.GrantedBy.String()
		grantedBy = &grantedByStr
	}

	var expiresAt *string
	if permission.ExpiresAt != nil {
		expiresAtStr := permission.ExpiresAt.Format(time.RFC3339)
		expiresAt = &expiresAtStr
	}

	return PermissionResponse{
		ID:            permission.ID.String(),
		UserID:        permission.UserID.String(),
		PermissionKey: permission.PermissionKey,
		Scope:         scope,
		ResourceType:  resourceType,
		Action:        action,
		ResourceIDs:   permission.ResourceIDs,
		GrantedBy:     grantedBy,
		GrantedAt:     permission.GrantedAt,
		ExpiresAt:     expiresAt,
		IsActive:      permission.IsActive(),
		CreatedAt:     permission.CreatedAt,
		UpdatedAt:     permission.UpdatedAt,
	}
}

// ToPermissionResponseList converts a list of Permission entities to PermissionResponse DTOs
func ToPermissionResponseList(permissions []*entities.Permission) []PermissionResponse {
	responses := make([]PermissionResponse, len(permissions))
	for i, permission := range permissions {
		responses[i] = ToPermissionResponse(permission)
	}
	return responses
}

// ParsePermissionKey parses a flattened permission key into components
// Example: "O:550e8400-e29b-41d4-a716-************:license:read" -> ("O:550e8400...", "license", "read")
func ParsePermissionKey(permissionKey string) (scope, resourceType, action string) {
	// Split on colon, but handle organization scope which contains UUID
	parts := []string{}
	current := ""
	
	for i, char := range permissionKey {
		if char == ':' {
			if len(parts) == 0 && current == "O" {
				// This is organization scope, continue until we have the full UUID
				current += string(char)
			} else if len(parts) == 1 && len(current) > 2 {
				// We have scope with UUID, now split normally
				parts = append(parts, current)
				current = ""
			} else {
				// Normal split
				parts = append(parts, current)
				current = ""
			}
		} else {
			current += string(char)
		}
		
		// Handle last part
		if i == len(permissionKey)-1 {
			parts = append(parts, current)
		}
	}
	
	// Simple split approach - more reliable
	colonCount := 0
	for _, char := range permissionKey {
		if char == ':' {
			colonCount++
		}
	}
	
	if colonCount == 2 {
		// Simple case: S:resource:action or R:resource:action
		simpleParts := []string{}
		current = ""
		for _, char := range permissionKey {
			if char == ':' {
				simpleParts = append(simpleParts, current)
				current = ""
			} else {
				current += string(char)
			}
		}
		simpleParts = append(simpleParts, current)
		
		if len(simpleParts) == 3 {
			return simpleParts[0], simpleParts[1], simpleParts[2]
		}
	}
	
	if colonCount == 3 {
		// Organization case: O:uuid:resource:action
		// Find the last two colons
		lastColon := -1
		secondLastColon := -1
		
		for i := len(permissionKey) - 1; i >= 0; i-- {
			if permissionKey[i] == ':' {
				if lastColon == -1 {
					lastColon = i
				} else if secondLastColon == -1 {
					secondLastColon = i
					break
				}
			}
		}
		
		if lastColon > 0 && secondLastColon > 0 {
			scope = permissionKey[:secondLastColon]
			resourceType = permissionKey[secondLastColon+1:lastColon]
			action = permissionKey[lastColon+1:]
			return scope, resourceType, action
		}
	}
	
	// Fallback - return the whole key as scope
	return permissionKey, "", ""
}

// ValidateScope validates permission scope format
func ValidateScope(scope string) bool {
	if scope == entities.ScopeSystem || scope == entities.ScopeResource {
		return true
	}
	
	// Check organization scope format: O:uuid
	if len(scope) > 2 && scope[:2] == "O:" {
		// Try to parse the UUID part
		uuidPart := scope[2:]
		_, err := uuid.Parse(uuidPart)
		return err == nil
	}
	
	return false
}

// ValidateResourceType validates resource type
func ValidateResourceType(resourceType string) bool {
	validTypes := []string{
		entities.ResourceTypeAll,
		entities.ResourceTypeOrganization,
		entities.ResourceTypeProduct,
		entities.ResourceTypePolicy,
		entities.ResourceTypeLicense,
		entities.ResourceTypeMachine,
		entities.ResourceTypeUser,
		entities.ResourceTypeAPIToken,
		entities.ResourceTypeSession,
	}
	
	for _, validType := range validTypes {
		if resourceType == validType {
			return true
		}
	}
	
	return false
}

// ValidateAction validates permission action
func ValidateAction(action string) bool {
	validActions := []string{
		entities.ActionAll,
		entities.ActionCreate,
		entities.ActionRead,
		entities.ActionUpdate,
		entities.ActionDelete,
		entities.ActionValidate,
		entities.ActionCheckout,
	}
	
	for _, validAction := range validActions {
		if action == validAction {
			return true
		}
	}
	
	return false
}

// BuildPaginationResponse builds pagination metadata
func BuildPaginationResponse(page, pageSize int, total int64) PaginationResponse {
	totalPages := int((total + int64(pageSize) - 1) / int64(pageSize))
	return PaginationResponse{
		Page:       page,
		PageSize:   pageSize,
		Total:      total,
		TotalPages: totalPages,
	}
}