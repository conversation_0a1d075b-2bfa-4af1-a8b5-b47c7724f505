package dto

import (
	"time"

	"github.com/google/uuid"
)

// CreateRoleRequest represents a request to create a role
type CreateRoleRequest struct {
	Name           string     `json:"name" binding:"required,min=3,max=50"`
	DisplayName    string     `json:"display_name" binding:"required,min=3,max=100"`
	Description    string     `json:"description" binding:"max=500"`
	Scope          string     `json:"scope" binding:"required,oneof=system organization custom"`
	OrganizationID *uuid.UUID `json:"organization_id,omitempty"`
	Permissions    []string   `json:"permissions" binding:"required,min=1"`
}

// UpdateRoleRequest represents a request to update a role
type UpdateRoleRequest struct {
	DisplayName *string  `json:"display_name,omitempty" binding:"omitempty,min=3,max=100"`
	Description *string  `json:"description,omitempty" binding:"omitempty,max=500"`
	Permissions []string `json:"permissions,omitempty" binding:"omitempty,min=1"`
	IsActive    *bool    `json:"is_active,omitempty"`
}

// AssignRoleRequest represents a request to assign a role to a user
type AssignRoleRequest struct {
	UserID         uuid.UUID  `json:"user_id" binding:"required"`
	OrganizationID *uuid.UUID `json:"organization_id,omitempty"`
	ExpiresAt      *string    `json:"expires_at,omitempty"` // RFC3339 format
	Reason         string     `json:"reason" binding:"max=500"`
}

// RevokeRoleRequest represents a request to revoke a role from a user
type RevokeRoleRequest struct {
	Reason string `json:"reason" binding:"max=500"`
}

// RoleResponse represents a role in API responses
type RoleResponse struct {
	ID             uuid.UUID  `json:"id"`
	Name           string     `json:"name"`
	DisplayName    string     `json:"display_name"`
	Description    string     `json:"description"`
	Scope          string     `json:"scope"`
	OrganizationID *uuid.UUID `json:"organization_id,omitempty"`
	Permissions    []string   `json:"permissions"`
	IsSystem       bool       `json:"is_system"`
	IsActive       bool       `json:"is_active"`
	CreatedBy      *uuid.UUID `json:"created_by,omitempty"`
	CreatedAt      time.Time  `json:"created_at"`
	UpdatedAt      time.Time  `json:"updated_at"`
}

// UserRoleResponse represents a user role assignment
type UserRoleResponse struct {
	ID             uuid.UUID      `json:"id"`
	UserID         uuid.UUID      `json:"user_id"`
	RoleID         uuid.UUID      `json:"role_id"`
	OrganizationID *uuid.UUID     `json:"organization_id,omitempty"`
	GrantedBy      uuid.UUID      `json:"granted_by"`
	GrantedAt      time.Time      `json:"granted_at"`
	ExpiresAt      *time.Time     `json:"expires_at,omitempty"`
	Reason         string         `json:"reason,omitempty"`
	IsActive       bool           `json:"is_active"`
	Role           *RoleResponse  `json:"role,omitempty"`
}

// RoleListResponse represents a paginated list of roles
type RoleListResponse struct {
	Roles      []RoleResponse `json:"roles"`
	Pagination Pagination     `json:"pagination"`
}

// UserRoleListResponse represents a list of user role assignments
type UserRoleListResponse struct {
	UserRoles []UserRoleResponse `json:"user_roles"`
}

// Pagination represents pagination information
type Pagination struct {
	Page     int `json:"page"`
	PageSize int `json:"page_size"`
	Total    int `json:"total"`
}