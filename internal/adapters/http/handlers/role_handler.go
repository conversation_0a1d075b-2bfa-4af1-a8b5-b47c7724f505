package handlers

import (
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/gokeys/gokeys/internal/adapters/http/dto"
	"github.com/gokeys/gokeys/internal/adapters/http/middleware"
	"github.com/gokeys/gokeys/internal/domain/entities"
	"github.com/gokeys/gokeys/internal/domain/repositories"
	"github.com/gokeys/gokeys/internal/domain/services/permission"
	"github.com/google/uuid"
	"github.com/lib/pq"
)

// RoleHandler handles role management endpoints
type RoleHandler struct {
	roleRepo       repositories.RoleRepository
	userRoleRepo   repositories.UserRoleRepository
	permissionSvc  *permission.EnhancedPermissionService
}

// NewRoleHandler creates a new role handler
func NewRoleHandler(
	roleRepo repositories.RoleRepository,
	userRoleRepo repositories.UserRoleRepository,
	permissionSvc *permission.EnhancedPermissionService,
) *RoleHandler {
	return &RoleHandler{
		roleRepo:      roleRepo,
		userRoleRepo:  userRoleRepo,
		permissionSvc: permissionSvc,
	}
}

// CreateRole creates a new role
// @Summary Create a new role
// @Description Create a new role with specified permissions
// @Tags roles
// @Accept json
// @Produce json
// @Param body body dto.CreateRoleRequest true "Role details"
// @Success 201 {object} dto.RoleResponse
// @Failure 400 {object} dto.ErrorResponse
// @Failure 401 {object} dto.ErrorResponse
// @Failure 403 {object} dto.ErrorResponse
// @Router /api/v1/admin/roles [post]
func (h *RoleHandler) CreateRole(c *gin.Context) {
	var req dto.CreateRoleRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		middleware.SendError(c, http.StatusBadRequest, "INVALID_REQUEST", "Invalid request format", err)
		return
	}

	// Get auth context
	authCtx, _ := middleware.GetAuthContext(c)
	
	// Determine scope
	var scope entities.RoleScope
	var orgID *uuid.UUID
	
	switch req.Scope {
	case "system":
		scope = entities.RoleScopeSystem
	case "organization":
		scope = entities.RoleScopeOrganization
		if req.OrganizationID == nil {
			middleware.SendError(c, http.StatusBadRequest, "INVALID_REQUEST", "Organization ID required for organization-scoped role", nil)
			return
		}
		orgID = req.OrganizationID
	case "custom":
		scope = entities.RoleScopeCustom
		orgID = req.OrganizationID
	default:
		middleware.SendError(c, http.StatusBadRequest, "INVALID_REQUEST", "Invalid role scope", nil)
		return
	}

	// Create role
	role := &entities.Role{
		Name:           req.Name,
		DisplayName:    req.DisplayName,
		Description:    req.Description,
		Scope:          scope,
		OrganizationID: orgID,
		Permissions:    pq.StringArray(req.Permissions),
		IsActive:       true,
		CreatedBy:      &authCtx.User.ID,
	}

	if err := h.roleRepo.Create(c.Request.Context(), role); err != nil {
		middleware.SendError(c, http.StatusInternalServerError, "CREATE_FAILED", "Failed to create role", err)
		return
	}

	// Convert to response
	resp := h.toRoleResponse(role)
	c.JSON(http.StatusCreated, resp)
}

// GetRole retrieves a role by ID
// @Summary Get role by ID
// @Description Get role details by ID
// @Tags roles
// @Accept json
// @Produce json
// @Param id path string true "Role ID"
// @Success 200 {object} dto.RoleResponse
// @Failure 404 {object} dto.ErrorResponse
// @Router /api/v1/admin/roles/{id} [get]
func (h *RoleHandler) GetRole(c *gin.Context) {
	roleID, err := uuid.Parse(c.Param("id"))
	if err != nil {
		middleware.SendError(c, http.StatusBadRequest, "INVALID_ID", "Invalid role ID", nil)
		return
	}

	role, err := h.roleRepo.GetByID(c.Request.Context(), roleID)
	if err != nil {
		middleware.SendError(c, http.StatusNotFound, "NOT_FOUND", "Role not found", nil)
		return
	}

	resp := h.toRoleResponse(role)
	c.JSON(http.StatusOK, resp)
}

// UpdateRole updates a role
// @Summary Update role
// @Description Update role details
// @Tags roles
// @Accept json
// @Produce json
// @Param id path string true "Role ID"
// @Param body body dto.UpdateRoleRequest true "Role updates"
// @Success 200 {object} dto.RoleResponse
// @Failure 400 {object} dto.ErrorResponse
// @Failure 403 {object} dto.ErrorResponse
// @Failure 404 {object} dto.ErrorResponse
// @Router /api/v1/admin/roles/{id} [put]
func (h *RoleHandler) UpdateRole(c *gin.Context) {
	roleID, err := uuid.Parse(c.Param("id"))
	if err != nil {
		middleware.SendError(c, http.StatusBadRequest, "INVALID_ID", "Invalid role ID", nil)
		return
	}

	var req dto.UpdateRoleRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		middleware.SendError(c, http.StatusBadRequest, "INVALID_REQUEST", "Invalid request format", err)
		return
	}

	// Get existing role
	role, err := h.roleRepo.GetByID(c.Request.Context(), roleID)
	if err != nil {
		middleware.SendError(c, http.StatusNotFound, "NOT_FOUND", "Role not found", nil)
		return
	}

	// Update fields
	if req.DisplayName != nil {
		role.DisplayName = *req.DisplayName
	}
	if req.Description != nil {
		role.Description = *req.Description
	}
	if req.Permissions != nil {
		role.Permissions = pq.StringArray(req.Permissions)
	}
	if req.IsActive != nil {
		role.IsActive = *req.IsActive
	}

	if err := h.roleRepo.Update(c.Request.Context(), role); err != nil {
		middleware.SendError(c, http.StatusInternalServerError, "UPDATE_FAILED", "Failed to update role", err)
		return
	}

	resp := h.toRoleResponse(role)
	c.JSON(http.StatusOK, resp)
}

// DeleteRole deletes a role
// @Summary Delete role
// @Description Delete a role (soft delete)
// @Tags roles
// @Accept json
// @Produce json
// @Param id path string true "Role ID"
// @Success 204
// @Failure 400 {object} dto.ErrorResponse
// @Failure 403 {object} dto.ErrorResponse
// @Failure 404 {object} dto.ErrorResponse
// @Router /api/v1/admin/roles/{id} [delete]
func (h *RoleHandler) DeleteRole(c *gin.Context) {
	roleID, err := uuid.Parse(c.Param("id"))
	if err != nil {
		middleware.SendError(c, http.StatusBadRequest, "INVALID_ID", "Invalid role ID", nil)
		return
	}

	if err := h.roleRepo.Delete(c.Request.Context(), roleID); err != nil {
		if err.Error() == "cannot delete system role" {
			middleware.SendError(c, http.StatusForbidden, "FORBIDDEN", err.Error(), nil)
			return
		}
		middleware.SendError(c, http.StatusInternalServerError, "DELETE_FAILED", "Failed to delete role", err)
		return
	}

	c.Status(http.StatusNoContent)
}

// ListRoles lists roles with pagination
// @Summary List roles
// @Description List roles with filtering and pagination
// @Tags roles
// @Accept json
// @Produce json
// @Param page query int false "Page number" default(1)
// @Param page_size query int false "Page size" default(20)
// @Param scope query string false "Role scope filter"
// @Param organization_id query string false "Organization ID filter"
// @Param is_active query bool false "Active status filter"
// @Param search query string false "Search term"
// @Success 200 {object} dto.RoleListResponse
// @Failure 400 {object} dto.ErrorResponse
// @Router /api/v1/admin/roles [get]
func (h *RoleHandler) ListRoles(c *gin.Context) {
	filter := repositories.DefaultListFilter()
	
	// Parse query parameters
	if page := c.Query("page"); page != "" {
		if p, err := strconv.Atoi(page); err == nil {
			filter.Page = p
		}
	}
	if pageSize := c.Query("page_size"); pageSize != "" {
		if ps, err := strconv.Atoi(pageSize); err == nil {
			filter.PageSize = ps
		}
	}
	if search := c.Query("search"); search != "" {
		filter.Search = search
	}
	
	// Apply filters
	if scope := c.Query("scope"); scope != "" {
		filter.Filters["scope"] = scope
	}
	if orgID := c.Query("organization_id"); orgID != "" {
		if orgUUID, err := uuid.Parse(orgID); err == nil {
			filter.OrganizationID = &orgUUID
		}
	}
	if isActive := c.Query("is_active"); isActive != "" {
		if active, err := strconv.ParseBool(isActive); err == nil {
			filter.Filters["is_active"] = active
		}
	}

	// Get roles
	roles, total, err := h.roleRepo.List(c.Request.Context(), filter)
	if err != nil {
		middleware.SendError(c, http.StatusInternalServerError, "LIST_FAILED", "Failed to list roles", err)
		return
	}

	// Convert to response
	resp := dto.RoleListResponse{
		Roles: make([]dto.RoleResponse, len(roles)),
		Pagination: dto.Pagination{
			Page:     filter.Page,
			PageSize: filter.PageSize,
			Total:    int(total),
		},
	}

	for i, role := range roles {
		resp.Roles[i] = h.toRoleResponse(role)
	}

	c.JSON(http.StatusOK, resp)
}

// AssignRole assigns a role to a user
// @Summary Assign role to user
// @Description Assign a role to a user with optional organization context
// @Tags roles
// @Accept json
// @Produce json
// @Param id path string true "Role ID"
// @Param body body dto.AssignRoleRequest true "Assignment details"
// @Success 200 {object} dto.UserRoleResponse
// @Failure 400 {object} dto.ErrorResponse
// @Failure 403 {object} dto.ErrorResponse
// @Failure 404 {object} dto.ErrorResponse
// @Router /api/v1/admin/roles/{id}/assign [post]
func (h *RoleHandler) AssignRole(c *gin.Context) {
	roleID, err := uuid.Parse(c.Param("id"))
	if err != nil {
		middleware.SendError(c, http.StatusBadRequest, "INVALID_ID", "Invalid role ID", nil)
		return
	}

	var req dto.AssignRoleRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		middleware.SendError(c, http.StatusBadRequest, "INVALID_REQUEST", "Invalid request format", err)
		return
	}

	// Get auth context
	authCtx, _ := middleware.GetAuthContext(c)

	// Parse expiration
	var expiresAt *time.Time
	if req.ExpiresAt != nil {
		t, err := time.Parse(time.RFC3339, *req.ExpiresAt)
		if err != nil {
			middleware.SendError(c, http.StatusBadRequest, "INVALID_REQUEST", "Invalid expiration date", nil)
			return
		}
		expiresAt = &t
	}

	// Assign role
	assignReq := permission.AssignRoleRequest{
		UserID:         req.UserID,
		RoleID:         roleID,
		OrganizationID: req.OrganizationID,
		GrantedBy:      authCtx.User.ID,
		ExpiresAt:      expiresAt,
		Reason:         req.Reason,
	}

	userRole, err := h.permissionSvc.AssignRole(c.Request.Context(), assignReq)
	if err != nil {
		middleware.SendError(c, http.StatusInternalServerError, "ASSIGN_FAILED", "Failed to assign role", err)
		return
	}

	resp := h.toUserRoleResponse(userRole)
	c.JSON(http.StatusOK, resp)
}

// RevokeRole revokes a role from a user
// @Summary Revoke role from user
// @Description Revoke a role assignment from a user
// @Tags roles
// @Accept json
// @Produce json
// @Param id path string true "User Role ID"
// @Param body body dto.RevokeRoleRequest true "Revocation details"
// @Success 204
// @Failure 400 {object} dto.ErrorResponse
// @Failure 403 {object} dto.ErrorResponse
// @Failure 404 {object} dto.ErrorResponse
// @Router /api/v1/admin/roles/assignments/{id}/revoke [delete]
func (h *RoleHandler) RevokeRole(c *gin.Context) {
	userRoleID, err := uuid.Parse(c.Param("id"))
	if err != nil {
		middleware.SendError(c, http.StatusBadRequest, "INVALID_ID", "Invalid user role ID", nil)
		return
	}

	var req dto.RevokeRoleRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		middleware.SendError(c, http.StatusBadRequest, "INVALID_REQUEST", "Invalid request format", err)
		return
	}

	// Get auth context
	authCtx, _ := middleware.GetAuthContext(c)

	// Revoke role
	revokeReq := permission.RevokeRoleRequest{
		UserRoleID: userRoleID,
		RevokedBy:  authCtx.User.ID,
		Reason:     req.Reason,
	}

	if err := h.permissionSvc.RevokeRole(c.Request.Context(), revokeReq); err != nil {
		middleware.SendError(c, http.StatusInternalServerError, "REVOKE_FAILED", "Failed to revoke role", err)
		return
	}

	c.Status(http.StatusNoContent)
}

// GetUserRoles gets all roles assigned to a user
// @Summary Get user roles
// @Description Get all roles assigned to a user
// @Tags roles
// @Accept json
// @Produce json
// @Param user_id query string true "User ID"
// @Param organization_id query string false "Organization ID filter"
// @Success 200 {object} dto.UserRoleListResponse
// @Failure 400 {object} dto.ErrorResponse
// @Router /api/v1/admin/roles/users [get]
func (h *RoleHandler) GetUserRoles(c *gin.Context) {
	userIDStr := c.Query("user_id")
	if userIDStr == "" {
		middleware.SendError(c, http.StatusBadRequest, "INVALID_REQUEST", "User ID required", nil)
		return
	}

	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		middleware.SendError(c, http.StatusBadRequest, "INVALID_ID", "Invalid user ID", nil)
		return
	}

	var userRoles []*entities.UserRole
	if orgIDStr := c.Query("organization_id"); orgIDStr != "" {
		orgID, err := uuid.Parse(orgIDStr)
		if err != nil {
			middleware.SendError(c, http.StatusBadRequest, "INVALID_ID", "Invalid organization ID", nil)
			return
		}
		userRoles, err = h.userRoleRepo.GetUserRolesByOrganization(c.Request.Context(), userID, orgID)
		if err != nil {
			middleware.SendError(c, http.StatusInternalServerError, "QUERY_FAILED", "Failed to query user roles", err)
			return
		}
	} else {
		userRoles, err = h.userRoleRepo.GetUserRoles(c.Request.Context(), userID)
		if err != nil {
			middleware.SendError(c, http.StatusInternalServerError, "QUERY_FAILED", "Failed to query user roles", err)
			return
		}
	}

	// Convert to response
	resp := dto.UserRoleListResponse{
		UserRoles: make([]dto.UserRoleResponse, len(userRoles)),
	}

	for i, ur := range userRoles {
		resp.UserRoles[i] = h.toUserRoleResponse(ur)
	}

	c.JSON(http.StatusOK, resp)
}

// toRoleResponse converts entity to DTO
func (h *RoleHandler) toRoleResponse(role *entities.Role) dto.RoleResponse {
	resp := dto.RoleResponse{
		ID:             role.ID,
		Name:           role.Name,
		DisplayName:    role.DisplayName,
		Description:    role.Description,
		Scope:          string(role.Scope),
		OrganizationID: role.OrganizationID,
		Permissions:    role.Permissions,
		IsSystem:       role.IsSystem,
		IsActive:       role.IsActive,
		CreatedAt:      role.CreatedAt,
		UpdatedAt:      role.UpdatedAt,
	}

	if role.CreatedBy != nil {
		resp.CreatedBy = role.CreatedBy
	}

	return resp
}

// toUserRoleResponse converts entity to DTO
func (h *RoleHandler) toUserRoleResponse(ur *entities.UserRole) dto.UserRoleResponse {
	resp := dto.UserRoleResponse{
		ID:             ur.ID,
		UserID:         ur.UserID,
		RoleID:         ur.RoleID,
		OrganizationID: ur.OrganizationID,
		GrantedBy:      ur.GrantedBy,
		GrantedAt:      ur.GrantedAt,
		ExpiresAt:      ur.ExpiresAt,
		Reason:         ur.Reason,
		IsActive:       ur.IsActive(),
	}

	if ur.Role != nil {
		roleResp := h.toRoleResponse(ur.Role)
		resp.Role = &roleResp
	}

	return resp
}