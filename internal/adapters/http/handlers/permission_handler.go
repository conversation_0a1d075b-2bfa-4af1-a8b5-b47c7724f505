package handlers

import (
	"fmt"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/gokeys/gokeys/internal/adapters/http/dto"
	"github.com/gokeys/gokeys/internal/adapters/http/middleware"
	"github.com/gokeys/gokeys/internal/domain/entities"
	"github.com/gokeys/gokeys/internal/domain/repositories"
	"github.com/google/uuid"
	"github.com/rs/zerolog/log"
)

// PermissionHandler handles permission management operations
// 
// Chức năng chính:
// - Grant/revoke permissions với support cho batch operations
// - List và search permissions với advanced filtering  
// - Check permissions với detailed response
// - Permission statistics và monitoring
// - Admin operations cho permission management
type PermissionHandler struct {
	permissionRepo repositories.PermissionRepository
	userRepo       repositories.UserRepository
}

// NewPermissionHandler creates a new permission handler
func NewPermissionHandler(
	permissionRepo repositories.PermissionRepository,
	userRepo repositories.UserRepository,
) *PermissionHandler {
	return &PermissionHandler{
		permissionRepo: permissionRepo,
		userRepo:       userRepo,
	}
}

// ==================== PERMISSION GRANTING ====================

// GrantPermission grants permissions to a user
// @Summary Grant permissions
// @Description Grant specific permissions to a user with optional expiration
// @Tags Permissions
// @Accept json
// @Produce json
// @Param request body dto.GrantPermissionRequest true "Grant permission request"
// @Success 201 {object} dto.GrantPermissionResponse
// @Failure 400 {object} middleware.ErrorResponse
// @Failure 403 {object} middleware.ErrorResponse
// @Failure 500 {object} middleware.ErrorResponse
// @Router /admin/permissions/grant [post]
func (h *PermissionHandler) GrantPermission(c *gin.Context) {
	var req dto.GrantPermissionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		middleware.SendError(c, http.StatusBadRequest, "INVALID_REQUEST", "Invalid request format", err)
		return
	}

	// Validate request
	if err := h.validateGrantRequest(&req); err != nil {
		middleware.SendError(c, http.StatusBadRequest, "VALIDATION_ERROR", "Validation failed", err)
		return
	}

	// Parse user ID
	userID, err := uuid.Parse(req.UserID)
	if err != nil {
		middleware.SendError(c, http.StatusBadRequest, "INVALID_USER_ID", "Invalid user ID format", err)
		return
	}

	// Verify user exists
	_, err = h.userRepo.GetByID(c.Request.Context(), userID)
	if err != nil {
		middleware.SendError(c, http.StatusBadRequest, "USER_NOT_FOUND", "User not found", err)
		return
	}

	// Parse expiration if provided
	var expiresAt *time.Time
	if req.ExpiresAt != nil && *req.ExpiresAt != "" {
		parsed, err := time.Parse(time.RFC3339, *req.ExpiresAt)
		if err != nil {
			middleware.SendError(c, http.StatusBadRequest, "INVALID_EXPIRATION", "Invalid expiration date format", err)
			return
		}
		expiresAt = &parsed
	}

	// Get granter ID from auth context
	authCtx, exists := middleware.GetAuthContext(c)
	if !exists {
		middleware.SendError(c, http.StatusUnauthorized, "AUTHENTICATION_REQUIRED", "Authentication required", nil)
		return
	}
	granterID := &authCtx.User.ID

	// Grant permissions using repository
	permission, err := h.permissionRepo.GrantPermissionWithResources(
		c.Request.Context(),
		userID,
		req.Scope,
		req.ResourceType,
		req.Actions,
		req.ResourceIDs,
		granterID,
		expiresAt,
	)
	if err != nil {
		log.Error().Err(err).
			Str("user_id", req.UserID).
			Str("scope", req.Scope).
			Str("resource_type", req.ResourceType).
			Strs("actions", req.Actions).
			Msg("Failed to grant permissions")
		middleware.SendError(c, http.StatusInternalServerError, "GRANT_FAILED", "Failed to grant permissions", err)
		return
	}

	// Get all permissions for response (to show all granted permissions)
	allPermissions, err := h.permissionRepo.GetUserPermissions(c.Request.Context(), req.UserID)
	if err != nil {
		log.Error().Err(err).Str("user_id", req.UserID).Msg("Failed to fetch permissions after grant")
		// Continue with single permission response
		allPermissions = []*entities.Permission{permission}
	}

	// Filter to only show recently granted permissions (same scope/resource)
	grantedPermissions := h.filterRecentlyGranted(allPermissions, req.Scope, req.ResourceType, req.Actions)

	response := dto.GrantPermissionResponse{
		Message:      "Permissions granted successfully",
		Permissions:  dto.ToPermissionResponseList(grantedPermissions),
		GrantedCount: len(grantedPermissions),
	}

	c.JSON(http.StatusCreated, response)
}

// BatchGrantPermissions grants multiple permissions efficiently
// @Summary Batch grant permissions
// @Description Grant multiple permissions to a user in a single operation
// @Tags Permissions
// @Accept json  
// @Produce json
// @Param request body dto.BatchGrantPermissionRequest true "Batch grant permission request"
// @Success 201 {object} dto.GrantPermissionResponse
// @Failure 400 {object} middleware.ErrorResponse
// @Failure 403 {object} middleware.ErrorResponse
// @Failure 500 {object} middleware.ErrorResponse
// @Router /admin/permissions/batch-grant [post]
func (h *PermissionHandler) BatchGrantPermissions(c *gin.Context) {
	var req dto.BatchGrantPermissionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		middleware.SendError(c, http.StatusBadRequest, "INVALID_REQUEST", "Invalid request format", err)
		return
	}

	// Validate request
	if err := h.validateBatchGrantRequest(&req); err != nil {
		middleware.SendError(c, http.StatusBadRequest, "VALIDATION_ERROR", "Validation failed", err)
		return
	}

	// Parse user ID
	userID, err := uuid.Parse(req.UserID)
	if err != nil {
		middleware.SendError(c, http.StatusBadRequest, "INVALID_USER_ID", "Invalid user ID format", err)
		return
	}

	// Verify user exists
	_, err = h.userRepo.GetByID(c.Request.Context(), userID)
	if err != nil {
		middleware.SendError(c, http.StatusBadRequest, "USER_NOT_FOUND", "User not found", err)
		return
	}

	// Get granter ID from auth context
	authCtx, exists := middleware.GetAuthContext(c)
	if !exists {
		middleware.SendError(c, http.StatusUnauthorized, "AUTHENTICATION_REQUIRED", "Authentication required", nil)
		return
	}
	granterID := &authCtx.User.ID

	// Convert DTOs to repository grants
	grants := make([]repositories.PermissionGrant, len(req.Grants))
	for i, grant := range req.Grants {
		var expiresAt *time.Time
		if grant.ExpiresAt != nil && *grant.ExpiresAt != "" {
			parsed, err := time.Parse(time.RFC3339, *grant.ExpiresAt)
			if err != nil {
				middleware.SendError(c, http.StatusBadRequest, "INVALID_EXPIRATION", fmt.Sprintf("Invalid expiration date format in grant %d", i+1), err)
				return
			}
			expiresAt = &parsed
		}

		grants[i] = repositories.PermissionGrant{
			UserID:       userID,
			Scope:        grant.Scope,
			ResourceType: grant.ResourceType,
			Actions:      grant.Actions,
			ResourceIDs:  grant.ResourceIDs,
			GrantedBy:    granterID,
			ExpiresAt:    expiresAt,
		}
	}

	// Batch grant permissions
	err = h.permissionRepo.BatchGrantPermissions(c.Request.Context(), grants)
	if err != nil {
		log.Error().Err(err).
			Str("user_id", req.UserID).
			Int("grant_count", len(grants)).
			Msg("Failed to batch grant permissions")
		middleware.SendError(c, http.StatusInternalServerError, "BATCH_GRANT_FAILED", "Failed to batch grant permissions", err)
		return
	}

	// Get updated permissions for response
	allPermissions, err := h.permissionRepo.GetUserPermissions(c.Request.Context(), req.UserID)
	if err != nil {
		log.Error().Err(err).Str("user_id", req.UserID).Msg("Failed to fetch permissions after batch grant")
		allPermissions = []*entities.Permission{}
	}

	// Calculate total granted count
	totalGranted := 0
	for _, grant := range req.Grants {
		totalGranted += len(grant.Actions)
	}

	response := dto.GrantPermissionResponse{
		Message:      "Permissions batch granted successfully",
		Permissions:  dto.ToPermissionResponseList(allPermissions),
		GrantedCount: totalGranted,
	}

	c.JSON(http.StatusCreated, response)
}

// ==================== PERMISSION REVOKING ====================

// RevokePermission revokes specific permissions
// @Summary Revoke permissions
// @Description Revoke specific permissions from a user
// @Tags Permissions
// @Accept json
// @Produce json
// @Param request body dto.RevokePermissionRequest true "Revoke permission request"
// @Success 200 {object} dto.RevokePermissionResponse
// @Failure 400 {object} middleware.ErrorResponse
// @Failure 403 {object} middleware.ErrorResponse
// @Failure 500 {object} middleware.ErrorResponse
// @Router /admin/permissions/revoke [delete]
func (h *PermissionHandler) RevokePermission(c *gin.Context) {
	var req dto.RevokePermissionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		middleware.SendError(c, http.StatusBadRequest, "INVALID_REQUEST", "Invalid request format", err)
		return
	}

	// Validate request
	if err := h.validateRevokeRequest(&req); err != nil {
		middleware.SendError(c, http.StatusBadRequest, "VALIDATION_ERROR", "Validation failed", err)
		return
	}

	// Revoke permissions
	err := h.permissionRepo.RevokePermission(c.Request.Context(), req.UserID, req.Scope, req.ResourceType)
	if err != nil {
		log.Error().Err(err).
			Str("user_id", req.UserID).
			Str("scope", req.Scope).
			Str("resource_type", req.ResourceType).
			Msg("Failed to revoke permissions")
		middleware.SendError(c, http.StatusInternalServerError, "REVOKE_FAILED", "Failed to revoke permissions", err)
		return
	}

	response := dto.RevokePermissionResponse{
		Message:      "Permissions revoked successfully",
		RevokedCount: 1, // TODO: Get actual count from repository
	}

	c.JSON(http.StatusOK, response)
}

// RevokeSpecificPermission revokes a specific permission by key
// @Summary Revoke specific permission
// @Description Revoke a specific permission by permission key
// @Tags Permissions
// @Accept json
// @Produce json
// @Param request body dto.RevokeSpecificPermissionRequest true "Revoke specific permission request"
// @Success 200 {object} dto.RevokePermissionResponse
// @Failure 400 {object} middleware.ErrorResponse
// @Failure 403 {object} middleware.ErrorResponse
// @Failure 500 {object} middleware.ErrorResponse
// @Router /admin/permissions/revoke-specific [delete]
func (h *PermissionHandler) RevokeSpecificPermission(c *gin.Context) {
	var req dto.RevokeSpecificPermissionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		middleware.SendError(c, http.StatusBadRequest, "INVALID_REQUEST", "Invalid request format", err)
		return
	}

	// Validate permission key format
	scope, resourceType, action := dto.ParsePermissionKey(req.PermissionKey)
	if scope == "" || resourceType == "" || action == "" {
		middleware.SendError(c, http.StatusBadRequest, "INVALID_PERMISSION_KEY", "Invalid permission key format", nil)
		return
	}

	// Revoke specific permission
	err := h.permissionRepo.RevokeSpecificPermission(c.Request.Context(), req.UserID, req.PermissionKey)
	if err != nil {
		log.Error().Err(err).
			Str("user_id", req.UserID).
			Str("permission_key", req.PermissionKey).
			Msg("Failed to revoke specific permission")
		middleware.SendError(c, http.StatusInternalServerError, "REVOKE_FAILED", "Failed to revoke specific permission", err)
		return
	}

	response := dto.RevokePermissionResponse{
		Message:      "Specific permission revoked successfully",
		RevokedCount: 1,
	}

	c.JSON(http.StatusOK, response)
}

// RevokeAllUserPermissions revokes all permissions for a user
// @Summary Revoke all user permissions
// @Description Revoke all permissions for a specific user
// @Tags Permissions
// @Param user_id path string true "User ID"
// @Success 200 {object} dto.RevokePermissionResponse
// @Failure 400 {object} middleware.ErrorResponse
// @Failure 403 {object} middleware.ErrorResponse
// @Failure 500 {object} middleware.ErrorResponse
// @Router /admin/permissions/users/{user_id}/revoke-all [delete]
func (h *PermissionHandler) RevokeAllUserPermissions(c *gin.Context) {
	userID := c.Param("user_id")
	if userID == "" {
		middleware.SendError(c, http.StatusBadRequest, "MISSING_USER_ID", "User ID is required", nil)
		return
	}

	// Validate UUID format
	if _, err := uuid.Parse(userID); err != nil {
		middleware.SendError(c, http.StatusBadRequest, "INVALID_USER_ID", "Invalid user ID format", err)
		return
	}

	// Revoke all permissions
	err := h.permissionRepo.RevokeUserPermissions(c.Request.Context(), userID)
	if err != nil {
		log.Error().Err(err).Str("user_id", userID).Msg("Failed to revoke all user permissions")
		middleware.SendError(c, http.StatusInternalServerError, "REVOKE_ALL_FAILED", "Failed to revoke all user permissions", err)
		return
	}

	response := dto.RevokePermissionResponse{
		Message:      "All user permissions revoked successfully",
		RevokedCount: 0, // TODO: Get actual count
	}

	c.JSON(http.StatusOK, response)
}

// ==================== PERMISSION CHECKING ====================

// CheckPermission checks if user has specific permission
// @Summary Check permission
// @Description Check if a user has a specific permission with detailed response
// @Tags Permissions
// @Accept json
// @Produce json
// @Param request body dto.CheckPermissionRequest true "Check permission request"
// @Success 200 {object} dto.CheckPermissionResponse
// @Failure 400 {object} middleware.ErrorResponse
// @Failure 500 {object} middleware.ErrorResponse
// @Router /admin/permissions/check [post]
func (h *PermissionHandler) CheckPermission(c *gin.Context) {
	var req dto.CheckPermissionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		middleware.SendError(c, http.StatusBadRequest, "INVALID_REQUEST", "Invalid request format", err)
		return
	}

	// Validate request
	if !dto.ValidateScope(req.Scope) {
		middleware.SendError(c, http.StatusBadRequest, "INVALID_SCOPE", "Invalid scope format", nil)
		return
	}
	if !dto.ValidateResourceType(req.ResourceType) {
		middleware.SendError(c, http.StatusBadRequest, "INVALID_RESOURCE_TYPE", "Invalid resource type", nil)
		return
	}
	if !dto.ValidateAction(req.Action) {
		middleware.SendError(c, http.StatusBadRequest, "INVALID_ACTION", "Invalid action", nil)
		return
	}

	// Check permission
	hasPermission, err := h.permissionRepo.HasPermission(c.Request.Context(), req.UserID, req.Scope, req.ResourceType, req.Action)
	if err != nil {
		log.Error().Err(err).
			Str("user_id", req.UserID).
			Str("scope", req.Scope).
			Str("resource_type", req.ResourceType).
			Str("action", req.Action).
			Msg("Failed to check permission")
		middleware.SendError(c, http.StatusInternalServerError, "PERMISSION_CHECK_FAILED", "Failed to check permission", err)
		return
	}

	// Build detailed response
	response := dto.CheckPermissionResponse{
		HasPermission: hasPermission,
		CheckedScopes: h.buildCheckedScopes(req.Scope),
		CheckedKeys:   h.buildCheckedKeys(req.Scope, req.ResourceType, req.Action),
	}

	// If user has permission, find the matching permission for details
	if hasPermission {
		userPermissions, err := h.permissionRepo.GetUserPermissions(c.Request.Context(), req.UserID)
		if err == nil {
			response.MatchedPermission = h.findMatchingPermission(userPermissions, req.Scope, req.ResourceType, req.Action)
		}
	}

	c.JSON(http.StatusOK, response)
}

// ==================== PERMISSION LISTING ====================

// ListPermissions lists permissions with filtering and pagination
// @Summary List permissions
// @Description List permissions with advanced filtering and pagination
// @Tags Permissions
// @Param user_id query string false "User ID filter"
// @Param scope_pattern query string false "Scope pattern filter (e.g., O:*)"
// @Param resource_type query string false "Resource type filter"
// @Param action query string false "Action filter"
// @Param include_expired query bool false "Include expired permissions"
// @Param page query int false "Page number (default: 1)"
// @Param page_size query int false "Page size (default: 20)"
// @Success 200 {object} dto.ListPermissionsResponse
// @Failure 400 {object} middleware.ErrorResponse
// @Failure 500 {object} middleware.ErrorResponse
// @Router /admin/permissions [get]
func (h *PermissionHandler) ListPermissions(c *gin.Context) {
	var req dto.ListPermissionsRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		middleware.SendError(c, http.StatusBadRequest, "INVALID_QUERY", "Invalid query parameters", err)
		return
	}

	// Set defaults
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	}
	if req.PageSize > 100 {
		req.PageSize = 100 // Limit max page size
	}

	// Get permissions based on filters
	var permissions []*entities.Permission
	var total int64
	var err error

	if req.UserID != "" {
		// Get user-specific permissions
		permissions, err = h.permissionRepo.GetUserPermissions(c.Request.Context(), req.UserID)
		if err != nil {
			log.Error().Err(err).Str("user_id", req.UserID).Msg("Failed to get user permissions")
			middleware.SendError(c, http.StatusInternalServerError, "LIST_FAILED", "Failed to list permissions", err)
			return
		}
		
		// Apply client-side filtering and pagination for user permissions
		permissions = h.filterPermissions(permissions, req)
		total = int64(len(permissions))
		
		// Apply pagination
		start := (req.Page - 1) * req.PageSize
		end := start + req.PageSize
		if start >= len(permissions) {
			permissions = []*entities.Permission{}
		} else if end > len(permissions) {
			permissions = permissions[start:]
		} else {
			permissions = permissions[start:end]
		}
	} else if req.ScopePattern != "" {
		// Get permissions by scope pattern
		permissions, err = h.permissionRepo.GetPermissionsByScope(c.Request.Context(), req.ScopePattern)
		if err != nil {
			log.Error().Err(err).Str("scope_pattern", req.ScopePattern).Msg("Failed to get permissions by scope")
			middleware.SendError(c, http.StatusInternalServerError, "LIST_FAILED", "Failed to list permissions", err)
			return
		}
		
		// Apply filtering and pagination
		permissions = h.filterPermissions(permissions, req)
		total = int64(len(permissions))
		
		// Apply pagination
		start := (req.Page - 1) * req.PageSize
		end := start + req.PageSize
		if start >= len(permissions) {
			permissions = []*entities.Permission{}
		} else if end > len(permissions) {
			permissions = permissions[start:]
		} else {
			permissions = permissions[start:end]
		}
	} else {
		// This would require a more complex query - for now return an error
		middleware.SendError(c, http.StatusBadRequest, "FILTER_REQUIRED", "Either user_id or scope_pattern filter is required", nil)
		return
	}

	response := dto.ListPermissionsResponse{
		Permissions: dto.ToPermissionResponseList(permissions),
		Pagination:  dto.BuildPaginationResponse(req.Page, req.PageSize, total),
	}

	c.JSON(http.StatusOK, response)
}

// GetUserPermissions gets all permissions for a specific user
// @Summary Get user permissions
// @Description Get all permissions for a specific user with summary
// @Tags Permissions
// @Param user_id path string true "User ID"
// @Success 200 {object} dto.UserPermissionSummaryResponse
// @Failure 400 {object} middleware.ErrorResponse
// @Failure 500 {object} middleware.ErrorResponse
// @Router /admin/permissions/users/{user_id} [get]
func (h *PermissionHandler) GetUserPermissions(c *gin.Context) {
	userID := c.Param("user_id")
	if userID == "" {
		middleware.SendError(c, http.StatusBadRequest, "MISSING_USER_ID", "User ID is required", nil)
		return
	}

	// Validate UUID format
	if _, err := uuid.Parse(userID); err != nil {
		middleware.SendError(c, http.StatusBadRequest, "INVALID_USER_ID", "Invalid user ID format", err)
		return
	}

	// Get user permissions
	permissions, err := h.permissionRepo.GetUserPermissions(c.Request.Context(), userID)
	if err != nil {
		log.Error().Err(err).Str("user_id", userID).Msg("Failed to get user permissions")
		middleware.SendError(c, http.StatusInternalServerError, "GET_PERMISSIONS_FAILED", "Failed to get user permissions", err)
		return
	}

	// Build summary
	response := h.buildUserPermissionSummary(userID, permissions)

	c.JSON(http.StatusOK, response)
}

// ==================== STATISTICS & MONITORING ====================

// GetPermissionStats gets permission statistics
// @Summary Get permission statistics
// @Description Get comprehensive permission statistics for monitoring
// @Tags Permissions
// @Success 200 {object} dto.PermissionStatsResponse
// @Failure 500 {object} middleware.ErrorResponse
// @Router /admin/permissions/stats [get]
func (h *PermissionHandler) GetPermissionStats(c *gin.Context) {
	stats, err := h.permissionRepo.GetPermissionStats(c.Request.Context())
	if err != nil {
		log.Error().Err(err).Msg("Failed to get permission statistics")
		middleware.SendError(c, http.StatusInternalServerError, "STATS_FAILED", "Failed to get permission statistics", err)
		return
	}

	// Build detailed stats response
	response := dto.PermissionStatsResponse{
		TotalPermissions:     getInt64FromMap(stats, "total_permissions"),
		ActivePermissions:    getInt64FromMap(stats, "active_permissions"),
		ExpiredPermissions:   getInt64FromMap(stats, "expired_permissions"),
		UsersWithPermissions: getInt64FromMap(stats, "users_with_permissions"),
		ScopeBreakdown:       make(map[string]int64),
		ResourceBreakdown:    make(map[string]int64),
		ActionBreakdown:      make(map[string]int64),
		ExpiringIn30Days:     0, // TODO: Implement this query
	}

	c.JSON(http.StatusOK, response)
}

// ==================== HELPER METHODS ====================

// validateGrantRequest validates grant permission request
func (h *PermissionHandler) validateGrantRequest(req *dto.GrantPermissionRequest) error {
	if !dto.ValidateScope(req.Scope) {
		return fmt.Errorf("invalid scope format")
	}
	if !dto.ValidateResourceType(req.ResourceType) {
		return fmt.Errorf("invalid resource type")
	}
	for _, action := range req.Actions {
		if !dto.ValidateAction(action) {
			return fmt.Errorf("invalid action: %s", action)
		}
	}
	return nil
}

// validateBatchGrantRequest validates batch grant request
func (h *PermissionHandler) validateBatchGrantRequest(req *dto.BatchGrantPermissionRequest) error {
	for i, grant := range req.Grants {
		if !dto.ValidateScope(grant.Scope) {
			return fmt.Errorf("invalid scope format in grant %d", i+1)
		}
		if !dto.ValidateResourceType(grant.ResourceType) {
			return fmt.Errorf("invalid resource type in grant %d", i+1)
		}
		for _, action := range grant.Actions {
			if !dto.ValidateAction(action) {
				return fmt.Errorf("invalid action %s in grant %d", action, i+1)
			}
		}
	}
	return nil
}

// validateRevokeRequest validates revoke permission request
func (h *PermissionHandler) validateRevokeRequest(req *dto.RevokePermissionRequest) error {
	if !dto.ValidateScope(req.Scope) {
		return fmt.Errorf("invalid scope format")
	}
	if !dto.ValidateResourceType(req.ResourceType) {
		return fmt.Errorf("invalid resource type")
	}
	return nil
}

// filterRecentlyGranted filters permissions to recently granted ones
func (h *PermissionHandler) filterRecentlyGranted(permissions []*entities.Permission, scope, resourceType string, actions []string) []*entities.Permission {
	var filtered []*entities.Permission
	for _, perm := range permissions {
		permScope, permResourceType, permAction := dto.ParsePermissionKey(perm.PermissionKey)
		if permScope == scope && permResourceType == resourceType {
			for _, action := range actions {
				if permAction == action {
					filtered = append(filtered, perm)
					break
				}
			}
		}
	}
	return filtered
}

// buildCheckedScopes builds list of scopes that would be checked
func (h *PermissionHandler) buildCheckedScopes(scope string) []string {
	scopes := []string{entities.ScopeSystem}
	if scope != "" && scope != entities.ScopeSystem {
		scopes = append(scopes, scope)
	}
	scopes = append(scopes, entities.ScopeResource)
	return scopes
}

// buildCheckedKeys builds list of permission keys that would be checked
func (h *PermissionHandler) buildCheckedKeys(scope, resourceType, action string) []string {
	var keys []string
	
	// Add specific scope keys
	if scope != "" {
		keys = append(keys, entities.BuildPermissionKey(scope, resourceType, action))
		keys = append(keys, entities.BuildPermissionKey(scope, resourceType, entities.ActionAll))
		keys = append(keys, entities.BuildPermissionKey(scope, entities.ResourceTypeAll, action))
		keys = append(keys, entities.BuildPermissionKey(scope, entities.ResourceTypeAll, entities.ActionAll))
	}
	
	// Add system scope keys
	keys = append(keys, entities.BuildPermissionKey(entities.ScopeSystem, resourceType, action))
	keys = append(keys, entities.BuildPermissionKey(entities.ScopeSystem, resourceType, entities.ActionAll))
	keys = append(keys, entities.BuildPermissionKey(entities.ScopeSystem, entities.ResourceTypeAll, action))
	keys = append(keys, entities.BuildPermissionKey(entities.ScopeSystem, entities.ResourceTypeAll, entities.ActionAll))
	
	return keys
}

// findMatchingPermission finds the permission that matches the check
func (h *PermissionHandler) findMatchingPermission(permissions []*entities.Permission, scope, resourceType, action string) *dto.PermissionResponse {
	checkedKeys := h.buildCheckedKeys(scope, resourceType, action)
	
	for _, key := range checkedKeys {
		for _, perm := range permissions {
			if perm.PermissionKey == key && perm.IsActive() {
				response := dto.ToPermissionResponse(perm)
				return &response
			}
		}
	}
	
	return nil
}

// filterPermissions applies client-side filtering to permissions
func (h *PermissionHandler) filterPermissions(permissions []*entities.Permission, req dto.ListPermissionsRequest) []*entities.Permission {
	var filtered []*entities.Permission
	
	for _, perm := range permissions {
		// Skip expired permissions unless requested
		if !req.IncludeExpired && perm.IsExpired() {
			continue
		}
		
		// Parse permission key for filtering
		_, resourceType, action := dto.ParsePermissionKey(perm.PermissionKey)
		
		// Apply resource type filter
		if req.ResourceType != "" && resourceType != req.ResourceType {
			continue
		}
		
		// Apply action filter
		if req.Action != "" && action != req.Action {
			continue
		}
		
		filtered = append(filtered, perm)
	}
	
	return filtered
}

// buildUserPermissionSummary builds user permission summary
func (h *PermissionHandler) buildUserPermissionSummary(userID string, permissions []*entities.Permission) dto.UserPermissionSummaryResponse {
	summary := dto.UserPermissionSummaryResponse{
		UserID:              userID,
		TotalPermissions:    len(permissions),
		SystemPermissions:   0,
		OrgPermissions:      0,
		ResourcePermissions: 0,
		ExpiringPermissions: 0,
		Permissions:         dto.ToPermissionResponseList(permissions),
		Organizations:       []string{},
		EffectiveScopes:     []string{},
	}

	orgMap := make(map[string]bool)
	scopeMap := make(map[string]bool)
	
	for _, perm := range permissions {
		if !perm.IsActive() {
			continue
		}
		
		scope, _, _ := dto.ParsePermissionKey(perm.PermissionKey)
		
		// Count by scope type
		if scope == entities.ScopeSystem {
			summary.SystemPermissions++
		} else if len(scope) > 2 && scope[:2] == "O:" {
			summary.OrgPermissions++
			orgMap[scope[2:]] = true // Extract org ID
		} else if scope == entities.ScopeResource {
			summary.ResourcePermissions++
		}
		
		// Track effective scopes
		scopeMap[scope] = true
		
		// Check if expiring in 30 days
		if perm.ExpiresAt != nil && time.Until(*perm.ExpiresAt) <= 30*24*time.Hour {
			summary.ExpiringPermissions++
		}
	}
	
	// Build organization list
	for orgID := range orgMap {
		summary.Organizations = append(summary.Organizations, orgID)
	}
	
	// Build effective scopes list
	for scope := range scopeMap {
		summary.EffectiveScopes = append(summary.EffectiveScopes, scope)
	}
	
	return summary
}

// getInt64FromMap safely gets int64 from map[string]any
func getInt64FromMap(m map[string]any, key string) int64 {
	if val, exists := m[key]; exists {
		if i64, ok := val.(int64); ok {
			return i64
		}
		if i, ok := val.(int); ok {
			return int64(i)
		}
	}
	return 0
}