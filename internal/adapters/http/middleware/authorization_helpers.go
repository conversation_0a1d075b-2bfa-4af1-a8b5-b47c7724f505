package middleware

import (
	"github.com/gin-gonic/gin"
)

// Helper functions for extracting IDs from request context

// GetResourceIDFromParam extracts resource ID from route parameter
// Usage: authz.RequirePermission("license", "read", GetResourceIDFromParam("id"))
func GetResourceIDFromParam(paramName string) func(*gin.Context) string {
	return func(c *gin.Context) string {
		return c.Param(paramName)
	}
}

// GetResourceIDFromHeader extracts resource ID from header
func GetResourceIDFromHeader(headerName string) func(*gin.Context) string {
	return func(c *gin.Context) string {
		return c.GetHeader(headerName)
	}
}

// GetResourceIDFromBody extracts resource ID from request body
func GetResourceIDFromBody(fieldName string) func(*gin.Context) string {
	return func(c *gin.Context) string {
		var body map[string]interface{}
		if err := c.ShouldBindJSON(&body); err != nil {
			return ""
		}
		
		if id, ok := body[fieldName].(string); ok {
			return id
		}
		
		return ""
	}
}