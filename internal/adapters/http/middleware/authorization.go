package middleware

import (
	"context"
	"fmt"
	"net/http"
	"strings"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/gokeys/gokeys/internal/domain/entities"
	"github.com/gokeys/gokeys/internal/domain/repositories"
	"github.com/rs/zerolog/log"
)

// AuthorizationMiddleware handles permission-based authorization with intelligent caching
// 
// Tính năng chính:
// - Ki<PERSON><PERSON> tra permissions theo hierarchy (system -> org -> resource -> owner)
// - Caching thông minh với TTL để giảm database load
// - Hỗ trợ org-scoped permissions với UUID đầy đủ
// - Resource-specific permission checking
//
// Permission key format: "{scope}:{resource_type}:{action}"
// Ví dụ: "org:550e8400-e29b-41d4-a716-************:license:read"
type AuthorizationMiddleware struct {
	permissionRepo repositories.PermissionRepository
	cache          *PermissionCache
}

// PermissionCache cung cấp caching hiệu quả cho permission lookups
// 
// <PERSON><PERSON> chế hoạt động:
// - Mỗi user có một cache entry riêng
// - <PERSON><PERSON> tự động expire sau TTL
// - Background goroutine cleanup expired entries
// - Thread-safe với RWMutex
type PermissionCache struct {
	mu       sync.RWMutex                    // Lock để đảm bảo thread safety
	userPerms map[string]*userPermissionCache // Map user_id -> permissions cache
	ttl      time.Duration                   // Time-to-live cho cache entries
}

// userPermissionCache lưu permissions của một user cụ thể
// 
// Cấu trúc:
// - permissions: map permission_key -> có permission hay không
// - expiresAt: thời điểm cache entry hết hạn
type userPermissionCache struct {
	permissions map[string]bool // Map permission_key -> hasPermission
	expiresAt   time.Time       // Cache expiration time
}

// NewAuthorizationMiddleware tạo authorization middleware với caching
//
// Parameters:
// - permissionRepo: Repository để truy cập permissions từ database
// - cacheTTL: Thời gian cache entry tồn tại (ví dụ: 5*time.Minute)
//
// Hoạt động:
// 1. Khởi tạo cache với TTL được chỉ định
// 2. Start background goroutine để cleanup expired cache entries
// 3. Return middleware instance sẵn sàng sử dụng
func NewAuthorizationMiddleware(permissionRepo repositories.PermissionRepository, cacheTTL time.Duration) *AuthorizationMiddleware {
	cache := &PermissionCache{
		userPerms: make(map[string]*userPermissionCache),
		ttl:       cacheTTL,
	}
	
	// Start background goroutine để cleanup expired cache entries
	// Chạy mỗi TTL interval để xóa các entries đã hết hạn
	go cache.cleanup()
	
	return &AuthorizationMiddleware{
		permissionRepo: permissionRepo,
		cache:          cache,
	}
}

// RequirePermission tạo middleware kiểm tra permission với hierarchy đầy đủ
//
// Parameters:
// - resourceType: Loại resource (license, product, user, *, etc.)  
// - action: Hành động (read, write, delete, *, etc.)
// - getResourceID: Function để extract resource ID (optional, có thể nil)
//
// Cơ chế hoạt động:
// 1. Luôn check theo hierarchy: S -> O:uuid -> R (không cần specify scope)
// 2. Nếu có getResourceID, sẽ check resource-specific permissions
// 3. Sử dụng cache để optimize performance
// 4. Stop early khi tìm thấy permission đầu tiên
//
// Đơn giản hóa:
// - Một method duy nhất cho mọi permission checks
// - Tự động check tất cả scopes theo hierarchy
// - Support cả general và resource-specific permissions
//
// Ví dụ usage:
// router.Use(authz.RequirePermission("license", "read", nil)) // General permission
// router.Use(authz.RequirePermission("license", "read", GetResourceIDFromParam("id"))) // Specific resource
func (am *AuthorizationMiddleware) RequirePermission(resourceType, action string, getResourceID func(*gin.Context) string) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Lấy authentication context từ authentication middleware
		authCtx, exists := GetAuthContext(c)
		if !exists {
			am.sendUnauthorized(c)
			return
		}

		userID := authCtx.User.ID.String()
		
		// Extract resource ID nếu có
		var resourceID *string
		if getResourceID != nil {
			id := getResourceID(c)
			if id == "" {
				am.sendBadRequest(c, "Invalid or missing resource ID")
				return
			}
			resourceID = &id
		}

		// Extract organization ID từ context (nếu có)
		var orgID *string
		if orgIDValue, exists := c.Get("organization_id"); exists {
			if orgIDStr, ok := orgIDValue.(string); ok && orgIDStr != "" {
				orgID = &orgIDStr
			}
		}

		// Build permission hierarchy scopes với org_id từ URL
		scopes := am.buildPermissionScopes(authCtx, orgID)
		
		// Check từng scope theo hierarchy cho đến khi tìm thấy permission
		hasPermission := false
		var cacheKey string
		
		for _, scope := range scopes {
			// Build cache key cho scope này
			scopeKey := entities.BuildPermissionKey(scope, resourceType, action)
			
			// FAST PATH: Check cache trước (nếu không có resource ID cụ thể)
			if resourceID == nil {
				if am.cache.getPermission(userID, scopeKey) {
					hasPermission = true
					break
				}
			}
			
			// SLOW PATH: Check database với hierarchy logic
			if ok, err := am.checkPermissionHierarchy(c.Request.Context(), userID, scope, resourceType, action, resourceID); err == nil && ok {
				hasPermission = true
				cacheKey = scopeKey // Cache key của scope match
				break
			}
		}

		// Cache kết quả nếu không có resource ID cụ thể
		if resourceID == nil && cacheKey != "" {
			am.cache.setPermission(userID, cacheKey, hasPermission)
		}

		if !hasPermission {
			am.sendForbidden(c, fmt.Sprintf("Permission denied: %s %s", action, resourceType))
			return
		}

		// Permission check passed -> continue
		c.Next()
	}
}

// RequireSystemAdmin yêu cầu system-wide admin permissions
//
// System admin có quyền cao nhất - sẽ được check tự động trong hierarchy
//
// Usage: router.Use(authz.RequireSystemAdmin())
func (am *AuthorizationMiddleware) RequireSystemAdmin() gin.HandlerFunc {
	return am.RequirePermission(entities.ResourceTypeAll, entities.ActionAll, nil)
}

// PreloadUserPermissions load tất cả permissions của user vào cache
//
// Cơ chế hoạt động:
// 1. Check xem user đã có trong cache chưa
// 2. Nếu chưa có, query database một lần để lấy tất cả permissions
// 3. Load vào cache để các request tiếp theo không cần query DB
// 4. Continue processing request
//
// Performance benefits:
// - Giảm database queries từ N queries xuống 1 query per user session
// - Các permission checks tiếp theo sẽ hit cache (rất nhanh)
// - Đặc biệt hiệu quả cho users có nhiều permissions
//
// Usage recommendation:
// - Đặt sau authentication middleware
// - Đặt trước các route cần nhiều permission checks
//
// Ví dụ:
// router.Use(authMiddleware.RequireAuthentication())
// router.Use(authzMiddleware.PreloadUserPermissions())
// router.GET("/dashboard", dashboardHandler) // Sẽ có sẵn permissions trong cache
func (am *AuthorizationMiddleware) PreloadUserPermissions() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Lấy authentication context
		authCtx, exists := GetAuthContext(c)
		if !exists {
			// User chưa authenticate -> skip preloading, continue processing
			c.Next()
			return
		}

		userID := authCtx.User.ID.String()
		
		// Check xem user đã có trong cache chưa
		// Nếu có và chưa expire -> skip loading
		if !am.cache.hasUser(userID) {
			// Load tất cả permissions của user từ database
			permissions, err := am.permissionRepo.GetUserPermissions(c.Request.Context(), userID)
			if err != nil {
				log.Error().Err(err).Str("user_id", userID).Msg("Failed to preload permissions")
				// Không fail request, chỉ log error và continue
				// Individual permission checks sẽ fallback to database
				c.Next()
				return
			}
			
			// Load permissions vào cache
			am.cache.loadUserPermissions(userID, permissions)
		}

		// Continue to next middleware/handler
		c.Next()
	}
}

// ==================== HELPER METHODS ====================

// checkPermissionHierarchy kiểm tra permission theo hierarchy với wildcard support
//
// Parameters:
// - userID: ID của user cần check permission
// - scope: Scope permission (system, org:uuid, resource, owner)
// - resourceType: Loại resource cần check
// - action: Action cần check
// - resourceID: ID resource cụ thể (nil nếu check tổng quát)
//
// Cơ chế hoạt động:
// 1. Build các permission keys theo hierarchy (từ cụ thể đến tổng quát)
// 2. Check từng key theo thứ tự cho đến khi tìm thấy match
// 3. Với org scopes, xử lý đặc biệt format "org:uuid"
// 4. Nếu có resourceID, check thêm resource access constraints
//
// Hierarchy order:
// - scope:resourceType:action (most specific)
// - scope:resourceType:* 
// - scope:*:action
// - scope:*:* (most general)
func (am *AuthorizationMiddleware) checkPermissionHierarchy(ctx context.Context, userID, scope, resourceType, action string, resourceID *string) (bool, error) {
	// Build permission keys theo hierarchy từ cụ thể đến tổng quát
	keys := am.buildPermissionKeysHierarchy(scope, resourceType, action)
	
	// Check từng key cho đến khi tìm thấy permission
	for _, key := range keys {
		parts := strings.Split(key, ":")
		if len(parts) != 3 {
			continue // Invalid key format -> skip
		}
		
		keyScope, keyResourceType, keyAction := parts[0], parts[1], parts[2]
		
		// Xử lý đặc biệt cho org scopes với UUID format
		if strings.HasPrefix(keyScope, "O:") && keyScope != "O" {
			// Check exact org scope match với UUID đầy đủ
			// Ví dụ: "O:550e8400-e29b-41d4-a716-************"
			if hasPermission, err := am.permissionRepo.HasPermission(ctx, userID, keyScope, keyResourceType, keyAction); err == nil && hasPermission {
				return am.checkResourceAccess(ctx, userID, key, resourceID)
			}
		} else {
			// Check các scopes khác (S, R)
			if hasPermission, err := am.permissionRepo.HasPermission(ctx, userID, keyScope, keyResourceType, keyAction); err == nil && hasPermission {
				return am.checkResourceAccess(ctx, userID, key, resourceID)
			}
		}
	}
	
	// Không tìm thấy permission nào match
	return false, nil
}

// checkResourceAccess kiểm tra xem user có access đến resource ID cụ thể không
//
// Parameters:
// - userID: ID của user
// - permissionKey: Permission key đã được confirmed có trong database
// - resourceID: ID của resource cần check (nil nếu không cần check resource cụ thể)
//
// Cơ chế hoạt động:
// 1. Nếu resourceID là nil -> return true (general permission, không cần check resource cụ thể)
// 2. Query tất cả permissions của user từ database
// 3. Tìm permission với key tương ứng
// 4. Check xem resourceID có trong danh sách resource_ids được phép không
//
// Resource access logic:
// - Nếu resource_ids = [] (empty) -> wildcard access (tất cả resources)
// - Nếu resource_ids = ["id1", "id2"] -> chỉ access được id1, id2
func (am *AuthorizationMiddleware) checkResourceAccess(ctx context.Context, userID, permissionKey string, resourceID *string) (bool, error) {
	if resourceID == nil {
		// General permission check (không cần resource ID cụ thể)
		return true, nil
	}
	
	// Get user permissions để check resource_ids constraints
	permissions, err := am.permissionRepo.GetUserPermissions(ctx, userID)
	if err != nil {
		return false, err
	}
	
	// Tìm permission với key tương ứng
	for _, perm := range permissions {
		if perm.PermissionKey == permissionKey && perm.IsActive() {
			// Check resource access constraints
			// MatchesResource sẽ check:
			// - Nếu ResourceIDs empty -> wildcard (allow all)
			// - Nếu ResourceIDs có data -> check resourceID có trong list không
			return perm.MatchesResource("", *resourceID), nil
		}
	}
	
	// Không tìm thấy permission với key tương ứng
	return false, nil
}

// buildPermissionKeysHierarchy tạo danh sách permission keys theo thứ tự hierarchy
//
// Input: scope="org:uuid123", resourceType="license", action="read"
// Output: [
//   "org:uuid123:license:read",     // Most specific
//   "org:uuid123:license:*",        // Resource type specific, all actions
//   "org:uuid123:*:read",           // Action specific, all resource types  
//   "org:uuid123:*:*"               // Most general, all resources + actions
// ]
//
// Thứ tự này đảm bảo:
// 1. Check permission cụ thể nhất trước
// 2. Fallback to wildcard permissions
// 3. Tối ưu performance (stop early khi tìm thấy match)
func (am *AuthorizationMiddleware) buildPermissionKeysHierarchy(scope, resourceType, action string) []string {
	var keys []string
	
	// 1. Check exact match trước (most specific)
	keys = append(keys, entities.BuildPermissionKey(scope, resourceType, action))
	
	// 2. Wildcard variations theo thứ tự ưu tiên
	keys = append(keys, entities.BuildPermissionKey(scope, resourceType, entities.ActionAll))      // scope:resource:*
	keys = append(keys, entities.BuildPermissionKey(scope, entities.ResourceTypeAll, action))     // scope:*:action
	keys = append(keys, entities.BuildPermissionKey(scope, entities.ResourceTypeAll, entities.ActionAll)) // scope:*:*
	
	return keys
}

// buildPermissionScopes tạo danh sách scopes để check theo thứ tự ưu tiên
//
// Hierarchy (từ cao đến thấp) - OPTIMIZED FORMAT:
// 1. S - System admin (highest priority)
// 2. O:uuid - Organization admin for specific org
// 3. R - Resource permissions (includes owner permissions)
//
// Returns: ["S", "O:uuid123", "R"]
//
// Cách sử dụng:
// - Loop qua các scopes theo thứ tự
// - Check permission với mỗi scope
// - Stop khi tìm thấy permission đầu tiên
//
// Performance benefits:
// - Shorter permission keys -> less storage, faster lookups
// - Merged owner into resource scope -> simplified logic
func (am *AuthorizationMiddleware) buildPermissionScopes(authCtx *AuthContext, orgID *string) []string {
	scopes := []string{entities.ScopeSystem} // "S" - System admin luôn có priority cao nhất
	
	// Add organization scope nếu có org_id từ URL path (không phải từ user's current org)
	if orgID != nil && *orgID != "" {
		orgScope := entities.BuildOrgScope(*orgID) // "O:uuid" từ URL parameter
		scopes = append(scopes, orgScope)
	}
	
	// Add resource scope (includes owner permissions)
	scopes = append(scopes, entities.ScopeResource) // "R"
	return scopes
}

// ==================== CACHE METHODS ====================

// getPermission lấy permission từ cache (thread-safe read)
//
// Returns:
// - true: User có permission và cache chưa expire
// - false: User không có permission hoặc cache đã expire
//
// Performance: O(1) lookup với RLock (cho phép concurrent reads)
func (c *PermissionCache) getPermission(userID, permissionKey string) bool {
	c.mu.RLock()
	defer c.mu.RUnlock()
	
	// Check xem user có trong cache không
	userCache, exists := c.userPerms[userID]
	if !exists || time.Now().After(userCache.expiresAt) {
		return false // Cache miss hoặc expired
	}
	
	// Return permission result từ cache
	return userCache.permissions[permissionKey]
}

// setPermission lưu kết quả permission check vào cache (thread-safe write)
//
// Parameters:
// - userID: ID của user
// - permissionKey: Permission key (format: scope:resource:action)
// - hasPermission: Kết quả permission check
//
// Cơ chế hoạt động:
// 1. Lock cache để đảm bảo thread safety
// 2. Tạo/update user cache entry với TTL mới
// 3. Lưu permission result vào user cache
func (c *PermissionCache) setPermission(userID, permissionKey string, hasPermission bool) {
	c.mu.Lock()
	defer c.mu.Unlock()
	
	// Get hoặc create user cache entry
	userCache, exists := c.userPerms[userID]
	if !exists || time.Now().After(userCache.expiresAt) {
		// Tạo cache entry mới với TTL
		userCache = &userPermissionCache{
			permissions: make(map[string]bool),
			expiresAt:   time.Now().Add(c.ttl),
		}
		c.userPerms[userID] = userCache
	}
	
	// Lưu permission result
	userCache.permissions[permissionKey] = hasPermission
}

// loadUserPermissions load tất cả permissions của user vào cache một lần
//
// Parameters:
// - userID: ID của user
// - permissions: Danh sách tất cả permissions của user từ database
//
// Use case:
// - Called by PreloadUserPermissions middleware
// - Giảm số lượng database queries từ N xuống 1
// - Hiệu quả cho users có nhiều permissions
//
// Note: Chỉ load active permissions (chưa expire)
func (c *PermissionCache) loadUserPermissions(userID string, permissions []*entities.Permission) {
	c.mu.Lock()
	defer c.mu.Unlock()
	
	// Tạo cache entry mới
	userCache := &userPermissionCache{
		permissions: make(map[string]bool),
		expiresAt:   time.Now().Add(c.ttl),
	}
	
	// Load tất cả active permissions vào cache
	for _, perm := range permissions {
		if perm.IsActive() { // Chỉ cache permissions chưa expire
			userCache.permissions[perm.PermissionKey] = true
		}
	}
	
	// Update cache
	c.userPerms[userID] = userCache
}

// hasUser check xem user có trong cache và chưa expire không
//
// Returns:
// - true: User có trong cache và chưa expire
// - false: User không có trong cache hoặc đã expire
//
// Use case: Check trước khi load permissions từ database
func (c *PermissionCache) hasUser(userID string) bool {
	c.mu.RLock()
	defer c.mu.RUnlock()
	
	userCache, exists := c.userPerms[userID]
	return exists && time.Now().Before(userCache.expiresAt)
}

// cleanup background goroutine để xóa expired cache entries
//
// Cơ chế hoạt động:
// 1. Chạy mỗi TTL interval (ví dụ: mỗi 5 phút)
// 2. Lock cache để đảm bảo thread safety
// 3. Loop qua tất cả user cache entries
// 4. Xóa các entries đã expire để giải phóng memory
//
// Performance benefits:
// - Tránh memory leak khi có nhiều users
// - Đảm bảo cache không tăng trưởng vô hạn
// - Background processing không ảnh hưởng request performance
func (c *PermissionCache) cleanup() {
	ticker := time.NewTicker(c.ttl)
	defer ticker.Stop()
	
	for range ticker.C {
		c.mu.Lock()
		now := time.Now()
		
		// Xóa tất cả expired user cache entries
		for userID, userCache := range c.userPerms {
			if now.After(userCache.expiresAt) {
				delete(c.userPerms, userID)
			}
		}
		
		c.mu.Unlock()
	}
}

// ==================== RESPONSE HELPERS ====================

// sendUnauthorized gửi HTTP 401 Unauthorized response
//
// Use case: User chưa authenticate (chưa login)
// Error code: AUTHENTICATION_REQUIRED
//
// Client action: Redirect to login page hoặc show login form
func (am *AuthorizationMiddleware) sendUnauthorized(c *gin.Context) {
	c.JSON(http.StatusUnauthorized, gin.H{
		"error": gin.H{
			"code":    "AUTHENTICATION_REQUIRED",
			"message": "Authentication required to access this resource",
		},
	})
	c.Abort() // Stop middleware chain
}

// sendForbidden gửi HTTP 403 Forbidden response
//
// Use case: User đã authenticate nhưng không có permission
// Error code: INSUFFICIENT_PERMISSIONS
//
// Parameters:
// - message: Custom message mô tả permission bị thiếu
//
// Client action: Show "Access Denied" message, không redirect
func (am *AuthorizationMiddleware) sendForbidden(c *gin.Context, message string) {
	c.JSON(http.StatusForbidden, gin.H{
		"error": gin.H{
			"code":    "INSUFFICIENT_PERMISSIONS",
			"message": message,
		},
	})
	c.Abort() // Stop middleware chain
}

// sendBadRequest gửi HTTP 400 Bad Request response
//
// Use case: Request không hợp lệ (thiếu resource ID, org ID, etc.)
// Error code: INVALID_REQUEST
//
// Parameters:
// - message: Mô tả lỗi cụ thể
//
// Client action: Fix request format và retry
func (am *AuthorizationMiddleware) sendBadRequest(c *gin.Context, message string) {
	c.JSON(http.StatusBadRequest, gin.H{
		"error": gin.H{
			"code":    "INVALID_REQUEST",
			"message": message,
		},
	})
	c.Abort() // Stop middleware chain
}

// sendInternalError gửi HTTP 500 Internal Server Error response
//
// Use case: Database error, service unavailable, etc.
// Error code: INTERNAL_ERROR
//
// Note: Error details đã được log, không expose cho client
// Client action: Retry sau ít phút hoặc contact support
func (am *AuthorizationMiddleware) sendInternalError(c *gin.Context) {
	c.JSON(http.StatusInternalServerError, gin.H{
		"error": gin.H{
			"code":    "INTERNAL_ERROR",
			"message": "An internal error occurred",
		},
	})
	c.Abort() // Stop middleware chain
}