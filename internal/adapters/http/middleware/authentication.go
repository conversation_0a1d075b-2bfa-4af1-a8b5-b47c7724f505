package middleware

import (
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/gokeys/gokeys/internal/domain/entities"
	"github.com/gokeys/gokeys/internal/domain/repositories"
	"github.com/gokeys/gokeys/internal/domain/services/auth"
	"github.com/google/uuid"
	"github.com/rs/zerolog/log"
	"gorm.io/gorm"
)

// AuthenticationMiddleware handles authentication for HTTP requests
type AuthenticationMiddleware struct {
	userRepo     repositories.UserRepository
	sessionRepo  repositories.SessionRepository
	apiTokenRepo repositories.APITokenRepository
	authService  *auth.AuthService
}

// NewAuthenticationMiddleware creates a new authentication middleware
func NewAuthenticationMiddleware(
	userRepo repositories.UserRepository,
	sessionRepo repositories.SessionRepository,
	apiTokenRepo repositories.APITokenRepository,
	authService *auth.AuthService,
) *AuthenticationMiddleware {
	return &AuthenticationMiddleware{
		userRepo:     userRepo,
		sessionRepo:  sessionRepo,
		apiTokenRepo: apiTokenRepo,
		authService:  authService,
	}
}

// AuthContext holds authentication information for authorization system
type AuthContext struct {
	// Core user information
	User    *entities.User
	Session *entities.Session

	// Authentication method used
	AuthType string // "jwt", "api_token", "session"

	// Organization context for permission hierarchy
	CurrentOrganizationID *string

	// Permissions cache (populated by authorization middleware)
	Permissions []entities.Permission

	// Security metadata
	IPAddress string
	UserAgent string
}

// AuthenticationError represents authentication errors
type AuthenticationError struct {
	Code    string
	Message string
}

func (e *AuthenticationError) Error() string {
	return e.Message
}

// RequireAuthentication middleware that requires valid authentication
func (am *AuthenticationMiddleware) RequireAuthentication() gin.HandlerFunc {
	return func(c *gin.Context) {
		authCtx, err := am.authenticate(c)
		if err != nil {
			log.Error().Err(err).Msg("Authentication failed")
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": gin.H{
					"code":    "AUTHENTICATION_REQUIRED",
					"message": "Valid authentication is required",
				},
			})
			c.Abort()
			return
		}

		// Store auth context in Gin context
		c.Set("auth", authCtx)
		c.Set("user", authCtx.User)
		c.Set("user_id", authCtx.User.ID.String())

		c.Next()
	}
}

// OptionalAuthentication middleware that allows both authenticated and unauthenticated requests
func (am *AuthenticationMiddleware) OptionalAuthentication() gin.HandlerFunc {
	return func(c *gin.Context) {
		authCtx, err := am.authenticate(c)
		if err == nil && authCtx != nil {
			// Store auth context if authentication succeeded
			c.Set("auth", authCtx)
			c.Set("user", authCtx.User)
			c.Set("user_id", authCtx.User.ID.String())
		}
		// Continue regardless of authentication status
		c.Next()
	}
}

// authenticate performs the actual authentication logic
func (am *AuthenticationMiddleware) authenticate(c *gin.Context) (*AuthContext, error) {
	// Try different authentication methods in order of preference

	// 1. Try Bearer token (JWT or API token)
	if authHeader := c.GetHeader("Authorization"); authHeader != "" {
		if strings.HasPrefix(authHeader, "Bearer ") {
			token := strings.TrimPrefix(authHeader, "Bearer ")

			// Check if token has API token prefix
			if am.isAPIToken(token) {
				return am.authenticateAPIToken(c, token)
			}

			// Otherwise, treat as JWT token
			return am.authenticateJWT(c, token)
		}
	}

	// 2. Try legacy X-API-Key header (for backward compatibility)
	if apiKey := c.GetHeader("X-API-Key"); apiKey != "" {
		return am.authenticateAPIToken(c, apiKey)
	}

	// 3. Try session cookie
	if sessionID := am.getSessionFromCookie(c); sessionID != "" {
		return am.authenticateSession(c, sessionID)
	}

	// 4. Try query parameter token (for webhooks/callbacks)
	if token := c.Query("token"); token != "" {
		// Check if it's API token or JWT
		if am.isAPIToken(token) {
			return am.authenticateAPIToken(c, token)
		}
		return am.authenticateJWT(c, token)
	}

	return nil, &AuthenticationError{Code: "NO_CREDENTIALS", Message: "No authentication credentials provided"}
}

// isAPIToken checks if token has API token prefix
func (am *AuthenticationMiddleware) isAPIToken(token string) bool {
	// Check for simplified license manager API token prefix
	// Permissions trong token scopes đã chứa toàn bộ thông tin phân loại
	return strings.HasPrefix(token, "ldr_")
}

// authenticateJWT authenticates using JWT token
func (am *AuthenticationMiddleware) authenticateJWT(c *gin.Context, token string) (*AuthContext, error) {
	claims, err := am.authService.VerifyJWT(token)
	if err != nil {
		return nil, &AuthenticationError{Code: "INVALID_JWT", Message: "Invalid JWT token"}
	}

	userID, ok := claims["user_id"].(string)
	if !ok {
		return nil, &AuthenticationError{Code: "INVALID_JWT_CLAIMS", Message: "Invalid JWT claims"}
	}

	userUUID, err := uuid.Parse(userID)
	if err != nil {
		return nil, &AuthenticationError{Code: "INVALID_USER_ID", Message: "Invalid user ID in token"}
	}

	user, err := am.userRepo.GetByID(c.Request.Context(), userUUID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, &AuthenticationError{Code: "USER_NOT_FOUND", Message: "User not found"}
		}
		return nil, err
	}

	if !user.IsActive() {
		return nil, &AuthenticationError{Code: "USER_INACTIVE", Message: "User account is inactive"}
	}

	if user.IsBanned() {
		return nil, &AuthenticationError{Code: "USER_BANNED", Message: "User account is banned"}
	}

	// Get session if session_id is in claims
	var session *entities.Session
	if sessionID, ok := claims["session_id"].(string); ok {
		sessionUUID, err := uuid.Parse(sessionID)
		if err == nil {
			session, _ = am.sessionRepo.GetByID(c.Request.Context(), sessionUUID)
			if session != nil && session.IsExpired() {
				return nil, &AuthenticationError{Code: "SESSION_EXPIRED", Message: "Session has expired"}
			}
		}
	}

	// Extract organization ID from claims
	var currentOrgID *string
	if orgID, ok := claims["org_id"].(string); ok && orgID != "" {
		currentOrgID = &orgID
	}

	return &AuthContext{
		User:                  user,
		Session:               session,
		AuthType:              "jwt",
		CurrentOrganizationID: currentOrgID,
		Permissions:           []entities.Permission{}, // Will be populated by authorization middleware
		IPAddress:             c.ClientIP(),
		UserAgent:             c.GetHeader("User-Agent"),
	}, nil
}

// authenticateAPIToken authenticates using API token
func (am *AuthenticationMiddleware) authenticateAPIToken(c *gin.Context, token string) (*AuthContext, error) {
	claims, err := am.authService.ValidateAPIToken(token)
	if err != nil {
		return nil, &AuthenticationError{Code: "INVALID_API_TOKEN", Message: "Invalid API token"}
	}

	userID, ok := claims["sub"].(string)
	if !ok {
		return nil, &AuthenticationError{Code: "INVALID_TOKEN_CLAIMS", Message: "Invalid token claims"}
	}

	userUUID, err := uuid.Parse(userID)
	if err != nil {
		return nil, &AuthenticationError{Code: "INVALID_USER_ID", Message: "Invalid user ID in token"}
	}

	user, err := am.userRepo.GetByID(c.Request.Context(), userUUID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, &AuthenticationError{Code: "USER_NOT_FOUND", Message: "User not found"}
		}
		return nil, err
	}

	if !user.IsActive() {
		return nil, &AuthenticationError{Code: "USER_INACTIVE", Message: "User account is inactive"}
	}

	if user.IsBanned() {
		return nil, &AuthenticationError{Code: "USER_BANNED", Message: "User account is banned"}
	}

	// Extract organization ID from token claims
	var currentOrgID *string
	if orgID, ok := claims["org_id"].(string); ok && orgID != "" {
		currentOrgID = &orgID
	}

	// Extract permissions from token claims (if available)
	var permissions []string
	if perms, ok := claims["permissions"].([]interface{}); ok {
		for _, perm := range perms {
			if permStr, ok := perm.(string); ok {
				permissions = append(permissions, permStr)
			}
		}
	}

	return &AuthContext{
		User:                  user,
		AuthType:              "api_token",
		CurrentOrganizationID: currentOrgID,
		Permissions:           []entities.Permission{}, // Will be populated by authorization middleware
		IPAddress:             c.ClientIP(),
		UserAgent:             c.GetHeader("User-Agent"),
	}, nil
}

// authenticateSession authenticates using session cookie
func (am *AuthenticationMiddleware) authenticateSession(c *gin.Context, sessionID string) (*AuthContext, error) {
	sessionUUID, err := uuid.Parse(sessionID)
	if err != nil {
		return nil, &AuthenticationError{Code: "INVALID_SESSION_ID", Message: "Invalid session ID format"}
	}

	session, err := am.sessionRepo.GetByID(c.Request.Context(), sessionUUID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, &AuthenticationError{Code: "SESSION_NOT_FOUND", Message: "Session not found"}
		}
		return nil, err
	}

	if session.IsExpired() {
		return nil, &AuthenticationError{Code: "SESSION_EXPIRED", Message: "Session has expired"}
	}

	user, err := am.userRepo.GetByID(c.Request.Context(), session.UserID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, &AuthenticationError{Code: "USER_NOT_FOUND", Message: "User not found"}
		}
		return nil, err
	}

	if !user.IsActive() {
		return nil, &AuthenticationError{Code: "USER_INACTIVE", Message: "User account is inactive"}
	}

	if user.IsBanned() {
		return nil, &AuthenticationError{Code: "USER_BANNED", Message: "User account is banned"}
	}

	// Update session last used time
	session.UpdateLastUsed()
	am.sessionRepo.Update(c.Request.Context(), session)

	// For session-based auth, org ID might come from user profile or session data
	// This would need to be implemented based on your user management system
	var currentOrgID *string
	// TODO: Extract from user's default organization or session data

	return &AuthContext{
		User:                  user,
		Session:               session,
		AuthType:              "session",
		CurrentOrganizationID: currentOrgID,
		Permissions:           []entities.Permission{}, // Will be populated by authorization middleware
		IPAddress:             c.ClientIP(),
		UserAgent:             c.GetHeader("User-Agent"),
	}, nil
}

// getSessionFromCookie extracts session ID from cookie
func (am *AuthenticationMiddleware) getSessionFromCookie(c *gin.Context) string {
	cookie, err := c.Cookie("session_id")
	if err != nil {
		return ""
	}
	return cookie
}

// GetAuthContext retrieves the authentication context from Gin context
func GetAuthContext(c *gin.Context) (*AuthContext, bool) {
	auth, exists := c.Get("auth")
	if !exists {
		return nil, false
	}
	authCtx, ok := auth.(*AuthContext)
	return authCtx, ok
}

// GetCurrentUser retrieves the current user from Gin context
func GetCurrentUser(c *gin.Context) (*entities.User, bool) {
	user, exists := c.Get("user")
	if !exists {
		return nil, false
	}
	u, ok := user.(*entities.User)
	return u, ok
}

// GetCurrentUserID retrieves the current user ID from Gin context
func GetCurrentUserID(c *gin.Context) (string, bool) {
	userID, exists := c.Get("user_id")
	if !exists {
		return "", false
	}
	id, ok := userID.(string)
	return id, ok
}
