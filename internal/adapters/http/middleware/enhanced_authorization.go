package middleware

import (
	"context"
	"fmt"
	"net/http"
	"strings"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/gokeys/gokeys/internal/domain/entities"
	"github.com/gokeys/gokeys/internal/domain/services/permission"
	"github.com/google/uuid"
	"github.com/rs/zerolog/log"
)

// EnhancedAuthorizationMiddleware handles RBAC + permission-based authorization
// 
// Tính năng chính:
// - <PERSON><PERSON><PERSON> tra permissions theo hierarchy (system -> org -> resource)
// - Hỗ trợ RBAC với role assignments
// - Permission delegation support
// - Caching thông minh với TTL
// - OPA policy evaluation (optional)
type EnhancedAuthorizationMiddleware struct {
	permissionService *permission.EnhancedPermissionService
	cache            *EffectivePermissionCache
}

// EffectivePermissionCache caches user's effective permissions (direct + role-based + delegated)
type EffectivePermissionCache struct {
	mu        sync.RWMutex
	userPerms map[string]*userEffectivePermissions
	ttl       time.Duration
}

type userEffectivePermissions struct {
	permissions []permission.EffectivePermission
	expiresAt   time.Time
}

// NewEnhancedAuthorizationMiddleware creates new enhanced authorization middleware
func NewEnhancedAuthorizationMiddleware(
	permissionService *permission.EnhancedPermissionService,
	cacheTTL time.Duration,
) *EnhancedAuthorizationMiddleware {
	cache := &EffectivePermissionCache{
		userPerms: make(map[string]*userEffectivePermissions),
		ttl:       cacheTTL,
	}
	
	// Start background cleanup
	go cache.cleanup()
	
	return &EnhancedAuthorizationMiddleware{
		permissionService: permissionService,
		cache:            cache,
	}
}

// RequirePermission checks for permission with full RBAC support
func (m *EnhancedAuthorizationMiddleware) RequirePermission(resourceType, action string, getResourceID func(*gin.Context) string) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get authentication context
		authCtx, exists := GetAuthContext(c)
		if !exists {
			m.sendUnauthorized(c)
			return
		}

		userID := authCtx.User.ID
		
		// Extract resource ID if provided
		var resourceID *string
		if getResourceID != nil {
			id := getResourceID(c)
			if id == "" {
				m.sendBadRequest(c, "Invalid or missing resource ID")
				return
			}
			resourceID = &id
		}

		// Extract organization ID from URL path (not from user context)
		var orgID *uuid.UUID
		if orgIDStr := c.Param("org_id"); orgIDStr != "" {
			if orgUUID, err := uuid.Parse(orgIDStr); err == nil {
				orgID = &orgUUID
			}
		} else if orgIDStr := c.Param("organization_id"); orgIDStr != "" {
			if orgUUID, err := uuid.Parse(orgIDStr); err == nil {
				orgID = &orgUUID
			}
		}

		// Build scope based on context
		scope := m.determineScope(orgID)
		
		// Check permission with context
		req := permission.CheckPermissionRequest{
			UserID:         userID,
			Scope:          scope,
			ResourceType:   resourceType,
			Action:         action,
			ResourceID:     resourceID,
			OrganizationID: orgID,
			Context: map[string]interface{}{
				"ip":         c.ClientIP(),
				"user_agent": c.Request.UserAgent(),
			},
		}
		
		hasPermission, err := m.checkPermissionWithCache(c.Request.Context(), req)
		if err != nil {
			log.Error().Err(err).Msg("Failed to check permission")
			m.sendInternalError(c)
			return
		}
		
		if !hasPermission {
			m.sendForbidden(c, fmt.Sprintf("Permission denied: %s %s", action, resourceType))
			return
		}

		// Permission granted
		c.Next()
	}
}

// RequireRole checks if user has a specific role
func (m *EnhancedAuthorizationMiddleware) RequireRole(roleName string) gin.HandlerFunc {
	return func(c *gin.Context) {
		authCtx, exists := GetAuthContext(c)
		if !exists {
			m.sendUnauthorized(c)
			return
		}

		userID := authCtx.User.ID
		
		// Extract organization context
		var orgID *uuid.UUID
		if orgIDStr := c.Param("org_id"); orgIDStr != "" {
			if orgUUID, err := uuid.Parse(orgIDStr); err == nil {
				orgID = &orgUUID
			}
		}
		
		// Get user's effective permissions
		effectivePerms, err := m.getEffectivePermissionsWithCache(c.Request.Context(), userID, orgID)
		if err != nil {
			log.Error().Err(err).Msg("Failed to get effective permissions")
			m.sendInternalError(c)
			return
		}
		
		// Check if user has required role
		hasRole := false
		for _, perm := range effectivePerms {
			if perm.Source == "role" && strings.Contains(perm.PermissionKey, roleName) {
				hasRole = true
				break
			}
		}
		
		if !hasRole {
			m.sendForbidden(c, fmt.Sprintf("Role required: %s", roleName))
			return
		}
		
		c.Next()
	}
}

// RequireSystemAdmin requires system administrator role
func (m *EnhancedAuthorizationMiddleware) RequireSystemAdmin() gin.HandlerFunc {
	return m.RequirePermission(entities.ResourceTypeAll, entities.ActionAll, nil)
}

// RequireOrgAdmin requires organization administrator role
func (m *EnhancedAuthorizationMiddleware) RequireOrgAdmin() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Extract org ID from path
		orgIDStr := c.Param("org_id")
		if orgIDStr == "" {
			orgIDStr = c.Param("organization_id")
		}
		
		if orgIDStr == "" {
			m.sendBadRequest(c, "Organization ID required")
			return
		}
		
		// Check org admin permission
		m.RequirePermission(entities.ResourceTypeAll, entities.ActionAll, nil)(c)
	}
}

// PreloadUserPermissions loads user's effective permissions into cache
func (m *EnhancedAuthorizationMiddleware) PreloadUserPermissions() gin.HandlerFunc {
	return func(c *gin.Context) {
		authCtx, exists := GetAuthContext(c)
		if !exists {
			c.Next()
			return
		}

		userID := authCtx.User.ID
		
		// Try to determine org context
		var orgID *uuid.UUID
		if orgIDStr := c.Param("org_id"); orgIDStr != "" {
			if orgUUID, err := uuid.Parse(orgIDStr); err == nil {
				orgID = &orgUUID
			}
		}
		
		// Preload permissions
		_, err := m.getEffectivePermissionsWithCache(c.Request.Context(), userID, orgID)
		if err != nil {
			log.Error().Err(err).Str("user_id", userID.String()).Msg("Failed to preload permissions")
		}
		
		c.Next()
	}
}

// ==================== HELPER METHODS ====================

// checkPermissionWithCache checks permission with caching
func (m *EnhancedAuthorizationMiddleware) checkPermissionWithCache(ctx context.Context, req permission.CheckPermissionRequest) (bool, error) {
	// For resource-specific checks, always go to service (no caching)
	if req.ResourceID != nil {
		return m.permissionService.CheckPermission(ctx, req)
	}
	
	// For general permissions, check cache first
	cacheKey := fmt.Sprintf("%s:%s:%s:%s", req.UserID, req.Scope, req.ResourceType, req.Action)
	
	// Try cache
	if m.cache.hasPermission(req.UserID.String(), cacheKey) {
		return true, nil
	}
	
	// Cache miss - check via service
	hasPermission, err := m.permissionService.CheckPermission(ctx, req)
	if err != nil {
		return false, err
	}
	
	// Cache result
	if hasPermission {
		m.cache.setPermission(req.UserID.String(), cacheKey)
	}
	
	return hasPermission, nil
}

// getEffectivePermissionsWithCache gets user's effective permissions with caching
func (m *EnhancedAuthorizationMiddleware) getEffectivePermissionsWithCache(ctx context.Context, userID uuid.UUID, orgID *uuid.UUID) ([]permission.EffectivePermission, error) {
	cacheKey := userID.String()
	if orgID != nil {
		cacheKey += ":" + orgID.String()
	}
	
	// Check cache
	if perms := m.cache.getEffectivePermissions(cacheKey); perms != nil {
		return perms, nil
	}
	
	// Cache miss - get from service
	perms, err := m.permissionService.GetUserEffectivePermissions(ctx, userID, orgID)
	if err != nil {
		return nil, err
	}
	
	// Cache result
	m.cache.setEffectivePermissions(cacheKey, perms)
	
	return perms, nil
}

// determineScope determines the permission scope based on context
func (m *EnhancedAuthorizationMiddleware) determineScope(orgID *uuid.UUID) string {
	if orgID != nil {
		return entities.BuildOrgScope(orgID.String())
	}
	return entities.ScopeResource
}

// ==================== CACHE METHODS ====================

// hasPermission checks if permission exists in cache
func (c *EffectivePermissionCache) hasPermission(userID, permissionKey string) bool {
	c.mu.RLock()
	defer c.mu.RUnlock()
	
	userCache, exists := c.userPerms[userID]
	if !exists || time.Now().After(userCache.expiresAt) {
		return false
	}
	
	// Check if permission key exists in effective permissions
	for _, perm := range userCache.permissions {
		if perm.PermissionKey == permissionKey {
			return true
		}
	}
	
	return false
}

// setPermission adds a permission to cache
func (c *EffectivePermissionCache) setPermission(userID, permissionKey string) {
	c.mu.Lock()
	defer c.mu.Unlock()
	
	userCache, exists := c.userPerms[userID]
	if !exists || time.Now().After(userCache.expiresAt) {
		// Don't create new entry with single permission
		// Wait for full permissions load
		return
	}
	
	// Add to existing permissions if not already there
	found := false
	for _, perm := range userCache.permissions {
		if perm.PermissionKey == permissionKey {
			found = true
			break
		}
	}
	
	if !found {
		userCache.permissions = append(userCache.permissions, permission.EffectivePermission{
			PermissionKey: permissionKey,
			Source:        "cached",
		})
	}
}

// getEffectivePermissions gets cached effective permissions
func (c *EffectivePermissionCache) getEffectivePermissions(cacheKey string) []permission.EffectivePermission {
	c.mu.RLock()
	defer c.mu.RUnlock()
	
	userCache, exists := c.userPerms[cacheKey]
	if !exists || time.Now().After(userCache.expiresAt) {
		return nil
	}
	
	return userCache.permissions
}

// setEffectivePermissions caches effective permissions
func (c *EffectivePermissionCache) setEffectivePermissions(cacheKey string, perms []permission.EffectivePermission) {
	c.mu.Lock()
	defer c.mu.Unlock()
	
	c.userPerms[cacheKey] = &userEffectivePermissions{
		permissions: perms,
		expiresAt:   time.Now().Add(c.ttl),
	}
}

// cleanup removes expired cache entries
func (c *EffectivePermissionCache) cleanup() {
	ticker := time.NewTicker(c.ttl)
	defer ticker.Stop()
	
	for range ticker.C {
		c.mu.Lock()
		now := time.Now()
		
		for key, cache := range c.userPerms {
			if now.After(cache.expiresAt) {
				delete(c.userPerms, key)
			}
		}
		
		c.mu.Unlock()
	}
}

// InvalidateUser removes user from cache
func (c *EffectivePermissionCache) InvalidateUser(userID string) {
	c.mu.Lock()
	defer c.mu.Unlock()
	
	// Remove all entries for this user
	for key := range c.userPerms {
		if strings.HasPrefix(key, userID) {
			delete(c.userPerms, key)
		}
	}
}

// ==================== RESPONSE HELPERS ====================

func (m *EnhancedAuthorizationMiddleware) sendUnauthorized(c *gin.Context) {
	c.JSON(http.StatusUnauthorized, gin.H{
		"error": gin.H{
			"code":    "AUTHENTICATION_REQUIRED",
			"message": "Authentication required to access this resource",
		},
	})
	c.Abort()
}

func (m *EnhancedAuthorizationMiddleware) sendForbidden(c *gin.Context, message string) {
	c.JSON(http.StatusForbidden, gin.H{
		"error": gin.H{
			"code":    "INSUFFICIENT_PERMISSIONS",
			"message": message,
		},
	})
	c.Abort()
}

func (m *EnhancedAuthorizationMiddleware) sendBadRequest(c *gin.Context, message string) {
	c.JSON(http.StatusBadRequest, gin.H{
		"error": gin.H{
			"code":    "INVALID_REQUEST",
			"message": message,
		},
	})
	c.Abort()
}

func (m *EnhancedAuthorizationMiddleware) sendInternalError(c *gin.Context) {
	c.JSON(http.StatusInternalServerError, gin.H{
		"error": gin.H{
			"code":    "INTERNAL_ERROR",
			"message": "An internal error occurred",
		},
	})
	c.Abort()
}