package repositories

import (
	"context"
	"fmt"

	"github.com/gokeys/gokeys/internal/domain/entities"
	"github.com/gokeys/gokeys/internal/domain/repositories"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

// RoleRepository implements PostgreSQL storage for roles
type RoleRepository struct {
	*repositories.BaseRepositoryImpl[entities.Role]
}

// NewRoleRepository creates a new role repository instance
func NewRoleRepository(db *gorm.DB) repositories.RoleRepository {
	return &RoleRepository{
		BaseRepositoryImpl: repositories.NewBaseRepository[entities.Role](db),
	}
}

// GetByName retrieves a role by name and optional organization ID
func (r *RoleRepository) GetByName(ctx context.Context, name string, organizationID *uuid.UUID) (*entities.Role, error) {
	var role entities.Role
	query := r.GetDB().WithContext(ctx).Where("name = ?", name)
	
	// For org-scoped roles, match organization ID
	// For system roles, organization_id should be NULL
	if organizationID != nil {
		query = query.Where("organization_id = ?", *organizationID)
	} else {
		query = query.Where("organization_id IS NULL")
	}
	
	err := query.First(&role).Error
	if err != nil {
		return nil, err
	}
	
	return &role, nil
}

// GetSystemRoles retrieves all system-scoped roles
func (r *RoleRepository) GetSystemRoles(ctx context.Context) ([]*entities.Role, error) {
	var roles []*entities.Role
	err := r.GetDB().WithContext(ctx).
		Where("scope = ? AND is_active = ?", entities.RoleScopeSystem, true).
		Order("name ASC").
		Find(&roles).Error
		
	if err != nil {
		return nil, fmt.Errorf("failed to get system roles: %w", err)
	}
	
	return roles, nil
}

// GetOrganizationRoles retrieves all roles for a specific organization
func (r *RoleRepository) GetOrganizationRoles(ctx context.Context, organizationID uuid.UUID) ([]*entities.Role, error) {
	var roles []*entities.Role
	err := r.GetDB().WithContext(ctx).
		Where("organization_id = ? AND is_active = ?", organizationID, true).
		Order("name ASC").
		Find(&roles).Error
		
	if err != nil {
		return nil, fmt.Errorf("failed to get organization roles: %w", err)
	}
	
	return roles, nil
}

// GetActiveRoles retrieves all active roles
func (r *RoleRepository) GetActiveRoles(ctx context.Context) ([]*entities.Role, error) {
	var roles []*entities.Role
	err := r.GetDB().WithContext(ctx).
		Where("is_active = ?", true).
		Order("scope ASC, name ASC").
		Find(&roles).Error
		
	if err != nil {
		return nil, fmt.Errorf("failed to get active roles: %w", err)
	}
	
	return roles, nil
}

// List retrieves roles with filtering and pagination
func (r *RoleRepository) List(ctx context.Context, filter repositories.ListFilter) ([]*entities.Role, int64, error) {
	query := r.buildListQuery(ctx, filter)
	
	var roles []*entities.Role
	var total int64
	
	// Count total records
	if err := query.Model(&entities.Role{}).Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count roles: %w", err)
	}
	
	// Apply pagination
	if filter.PageSize > 0 {
		offset := (filter.Page - 1) * filter.PageSize
		query = query.Offset(offset).Limit(filter.PageSize)
	}
	
	// Apply sorting
	if filter.SortBy != "" {
		order := filter.SortBy
		if filter.SortOrder == "DESC" {
			order += " DESC"
		} else {
			order += " ASC"
		}
		query = query.Order(order)
	} else {
		query = query.Order("name ASC")
	}
	
	// Execute query with preloads
	if err := query.
		Preload("Organization").
		Preload("Creator").
		Find(&roles).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to list roles: %w", err)
	}
	
	return roles, total, nil
}

// buildListQuery builds the query for listing roles with filters
func (r *RoleRepository) buildListQuery(ctx context.Context, filter repositories.ListFilter) *gorm.DB {
	query := r.GetDB().WithContext(ctx)
	
	// Apply organization filter
	if filter.OrganizationID != nil {
		query = query.Where("organization_id = ?", *filter.OrganizationID)
	}
	
	// Apply search filter
	if filter.Search != "" {
		searchPattern := "%" + filter.Search + "%"
		query = query.Where("name ILIKE ? OR display_name ILIKE ? OR description ILIKE ?", 
			searchPattern, searchPattern, searchPattern)
	}
	
	// Apply custom filters
	for key, value := range filter.Filters {
		switch key {
		case "scope":
			query = query.Where("scope = ?", value)
		case "is_system":
			query = query.Where("is_system = ?", value)
		case "is_active":
			query = query.Where("is_active = ?", value)
		case "created_by":
			query = query.Where("created_by = ?", value)
		default:
			// Handle any other filters
			query = query.Where(key+" = ?", value)
		}
	}
	
	// Include deleted records if requested
	if filter.IncludeDeleted {
		query = query.Unscoped()
	}
	
	return query
}

// Create creates a new role with validation
func (r *RoleRepository) Create(ctx context.Context, role *entities.Role) error {
	// Ensure role ID is set
	if role.ID == uuid.Nil {
		role.ID = uuid.New()
	}
	
	// Validate organization consistency
	if role.Scope == entities.RoleScopeOrganization && role.OrganizationID == nil {
		return fmt.Errorf("organization-scoped role must have organization_id")
	}
	if role.Scope == entities.RoleScopeSystem && role.OrganizationID != nil {
		return fmt.Errorf("system-scoped role cannot have organization_id")
	}
	
	return r.BaseRepositoryImpl.Create(ctx, role)
}

// Update updates a role with validation
func (r *RoleRepository) Update(ctx context.Context, role *entities.Role) error {
	// Prevent updating system roles
	var existing entities.Role
	if err := r.GetDB().WithContext(ctx).First(&existing, "id = ?", role.ID).Error; err != nil {
		return fmt.Errorf("role not found: %w", err)
	}
	
	if existing.IsSystem {
		return fmt.Errorf("cannot update system role")
	}
	
	// Update only allowed fields
	updates := map[string]interface{}{
		"display_name": role.DisplayName,
		"description":  role.Description,
		"permissions":  role.Permissions,
		"conditions":   role.Conditions,
		"is_active":    role.IsActive,
		"updated_at":   gorm.Expr("CURRENT_TIMESTAMP"),
	}
	
	return r.GetDB().WithContext(ctx).
		Model(&entities.Role{}).
		Where("id = ?", role.ID).
		Updates(updates).Error
}

// Delete soft deletes a role
func (r *RoleRepository) Delete(ctx context.Context, id uuid.UUID) error {
	// Prevent deleting system roles
	var role entities.Role
	if err := r.GetDB().WithContext(ctx).First(&role, "id = ?", id).Error; err != nil {
		return fmt.Errorf("role not found: %w", err)
	}
	
	if role.IsSystem {
		return fmt.Errorf("cannot delete system role")
	}
	
	// Soft delete by setting is_active = false
	return r.GetDB().WithContext(ctx).
		Model(&entities.Role{}).
		Where("id = ?", id).
		Update("is_active", false).Error
}

// InitializeSystemRoles creates predefined system roles if they don't exist
func (r *RoleRepository) InitializeSystemRoles(ctx context.Context) error {
	systemRoles := []entities.Role{
		entities.SystemAdminRole,
		entities.OrgAdminRole,
		entities.OrgManagerRole,
		entities.OrgMemberRole,
	}
	
	for _, role := range systemRoles {
		// Check if role exists
		var count int64
		err := r.GetDB().WithContext(ctx).
			Model(&entities.Role{}).
			Where("name = ? AND scope = ?", role.Name, role.Scope).
			Count(&count).Error
			
		if err != nil {
			return fmt.Errorf("failed to check role %s: %w", role.Name, err)
		}
		
		// Create if doesn't exist
		if count == 0 {
			role.ID = uuid.New()
			if err := r.Create(ctx, &role); err != nil {
				return fmt.Errorf("failed to create system role %s: %w", role.Name, err)
			}
		}
	}
	
	return nil
}