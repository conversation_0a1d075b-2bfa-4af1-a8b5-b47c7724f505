package repositories

import (
	"github.com/gokeys/gokeys/internal/domain/repositories"
	"gorm.io/gorm"
)

// RepositoryFactory creates and manages all repository instances
type RepositoryFactory struct {
	db *gorm.DB

	// Repository instances
	organization        repositories.OrganizationRepository
	product             repositories.ProductRepository
	policy              repositories.PolicyRepository
	license             repositories.LicenseRepository
	machine             repositories.MachineRepository
	user                repositories.UserRepository
	session             repositories.SessionRepository
	apiToken            repositories.APITokenRepository
	usersOrganization   repositories.UsersOrganizationRepository
	permission          repositories.PermissionRepository
	role                repositories.RoleRepository
	userRole            repositories.UserRoleRepository
	permissionAuditLog  repositories.PermissionAuditLogRepository
	
}

// NewRepositoryFactory creates a new repository factory
func NewRepositoryFactory(db *gorm.DB) *RepositoryFactory {
	return &RepositoryFactory{
		db: db,
	}
}

// Organization returns the organization repository
func (f *RepositoryFactory) Organization() repositories.OrganizationRepository {
	if f.organization == nil {
		f.organization = NewOrganizationRepository(f.db)
	}
	return f.organization
}

// Product returns the product repository
func (f *RepositoryFactory) Product() repositories.ProductRepository {
	if f.product == nil {
		f.product = NewProductRepository(f.db)
	}
	return f.product
}

// Policy returns the policy repository
func (f *RepositoryFactory) Policy() repositories.PolicyRepository {
	if f.policy == nil {
		f.policy = NewPolicyRepository(f.db)
	}
	return f.policy
}

// License returns the license repository
func (f *RepositoryFactory) License() repositories.LicenseRepository {
	if f.license == nil {
		f.license = NewLicenseRepository(f.db)
	}
	return f.license
}

// Machine returns the machine repository
func (f *RepositoryFactory) Machine() repositories.MachineRepository {
	if f.machine == nil {
		f.machine = NewMachineRepository(f.db)
	}
	return f.machine
}

// User returns the user repository
func (f *RepositoryFactory) User() repositories.UserRepository {
	if f.user == nil {
		f.user = NewUserRepository(f.db)
	}
	return f.user
}

// APIToken returns the API token repository
func (f *RepositoryFactory) APIToken() repositories.APITokenRepository {
	if f.apiToken == nil {
		f.apiToken = NewAPITokenRepository(f.db)
	}
	return f.apiToken
}

// UsersOrganization returns the users organization repository
func (f *RepositoryFactory) UsersOrganization() repositories.UsersOrganizationRepository {
	if f.usersOrganization == nil {
		f.usersOrganization = NewPostgresUsersOrganizationRepository(f.db)
	}
	return f.usersOrganization
}

// Permission returns the permission repository
func (f *RepositoryFactory) Permission() repositories.PermissionRepository {
	if f.permission == nil {
		f.permission = NewPostgresPermissionRepository(f.db)
	}
	return f.permission
}

// Session returns the session repository
func (f *RepositoryFactory) Session() repositories.SessionRepository {
	if f.session == nil {
		f.session = NewSessionRepository(f.db)
	}
	return f.session
}

// Role returns the role repository
func (f *RepositoryFactory) Role() repositories.RoleRepository {
	if f.role == nil {
		f.role = NewRoleRepository(f.db)
	}
	return f.role
}

// UserRole returns the user role repository
func (f *RepositoryFactory) UserRole() repositories.UserRoleRepository {
	if f.userRole == nil {
		f.userRole = NewUserRoleRepository(f.db)
	}
	return f.userRole
}

// PermissionAuditLog returns the permission audit log repository
func (f *RepositoryFactory) PermissionAuditLog() repositories.PermissionAuditLogRepository {
	if f.permissionAuditLog == nil {
		f.permissionAuditLog = NewPermissionAuditLogRepository(f.db)
	}
	return f.permissionAuditLog
}



// GetDB returns the underlying database connection
func (f *RepositoryFactory) GetDB() *gorm.DB {
	return f.db
}

// Transaction wraps operations in a database transaction
func (f *RepositoryFactory) Transaction(fn func(*RepositoryFactory) error) error {
	return f.db.Transaction(func(tx *gorm.DB) error {
		txFactory := NewRepositoryFactory(tx)
		return fn(txFactory)
	})
}
