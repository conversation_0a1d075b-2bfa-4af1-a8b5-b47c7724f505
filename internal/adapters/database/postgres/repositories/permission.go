package repositories

import (
	"context"
	"fmt"
	"time"

	"github.com/gokeys/gokeys/internal/domain/entities"
	"github.com/gokeys/gokeys/internal/domain/repositories"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

// PostgresPermissionRepository implements optimized permission repository with flattened keys
//
// Thiết kế tối ưu:
// - Sử dụng flattened permission keys để O(1) lookups
// - Batch operations để giảm database round trips  
// - Index-optimized queries cho performance cao
// - Support hierarchy checking (System -> Org -> Resource)
type PostgresPermissionRepository struct {
	db *gorm.DB
}

// NewPostgresPermissionRepository creates a new optimized permission repository
func NewPostgresPermissionRepository(db *gorm.DB) repositories.PermissionRepository {
	return &PostgresPermissionRepository{db: db}
}

// ==================== CORE CRUD OPERATIONS ====================

// Create creates a new permission
func (r *PostgresPermissionRepository) Create(ctx context.Context, permission *entities.Permission) error {
	return r.db.WithContext(ctx).Create(permission).Error
}

// GetByID gets a permission by ID
func (r *PostgresPermissionRepository) GetByID(ctx context.Context, id string) (*entities.Permission, error) {
	var permission entities.Permission

	err := r.db.WithContext(ctx).
		Where("id = ?", id).
		First(&permission).Error

	if err != nil {
		return nil, err
	}

	return &permission, nil
}

// Update updates an existing permission
func (r *PostgresPermissionRepository) Update(ctx context.Context, permission *entities.Permission) error {
	return r.db.WithContext(ctx).Save(permission).Error
}

// Delete deletes a permission by ID
func (r *PostgresPermissionRepository) Delete(ctx context.Context, id string) error {
	return r.db.WithContext(ctx).Delete(&entities.Permission{}, "id = ?", id).Error
}

// ==================== USER PERMISSION QUERIES ====================

// GetUserPermissions gets all active permissions for a user
// Optimized với single query và index trên (user_id, expires_at)
func (r *PostgresPermissionRepository) GetUserPermissions(ctx context.Context, userID string) ([]*entities.Permission, error) {
	var permissions []*entities.Permission

	err := r.db.WithContext(ctx).
		Where("user_id = ?", userID).
		Where("expires_at IS NULL OR expires_at > ?", time.Now()).
		Order("permission_key ASC"). // Consistent ordering cho cache stability
		Find(&permissions).Error

	return permissions, err
}

// GetUserPermissionKeys gets only permission keys for a user (lightweight query)
// Sử dụng cho cache warming và permission checking nhanh
func (r *PostgresPermissionRepository) GetUserPermissionKeys(ctx context.Context, userID string) ([]string, error) {
	var keys []string

	err := r.db.WithContext(ctx).
		Model(&entities.Permission{}).
		Select("permission_key").
		Where("user_id = ?", userID).
		Where("expires_at IS NULL OR expires_at > ?", time.Now()).
		Pluck("permission_key", &keys).Error

	return keys, err
}

// ==================== PERMISSION CHECKING ====================

// HasPermission checks if user has specific permission with hierarchy support
// Optimized với prepared statement và index trên (user_id, permission_key)
func (r *PostgresPermissionRepository) HasPermission(ctx context.Context, userID, scope, resourceType, action string) (bool, error) {
	// Build permission keys theo hierarchy order (specific -> general)
	keys := r.buildPermissionKeysHierarchy(scope, resourceType, action)

	var count int64
	err := r.db.WithContext(ctx).
		Model(&entities.Permission{}).
		Where("user_id = ?", userID).
		Where("permission_key IN ?", keys).
		Where("expires_at IS NULL OR expires_at > ?", time.Now()).
		Count(&count).Error

	return count > 0, err
}

// HasAnyPermission checks if user has any of the specified permissions
// Efficient batch checking cho multiple permission validation
func (r *PostgresPermissionRepository) HasAnyPermission(ctx context.Context, userID string, permissionKeys []string) (bool, error) {
	if len(permissionKeys) == 0 {
		return false, nil
	}

	var count int64
	err := r.db.WithContext(ctx).
		Model(&entities.Permission{}).
		Where("user_id = ?", userID).
		Where("permission_key IN ?", permissionKeys).
		Where("expires_at IS NULL OR expires_at > ?", time.Now()).
		Count(&count).Error

	return count > 0, err
}

// CheckResourceAccess checks if user can access specific resource
// Kiểm tra resource_ids constraints cho resource-level permissions
func (r *PostgresPermissionRepository) CheckResourceAccess(ctx context.Context, userID string, permissionKey string, resourceID string) (bool, error) {
	var permission entities.Permission

	err := r.db.WithContext(ctx).
		Where("user_id = ? AND permission_key = ?", userID, permissionKey).
		Where("expires_at IS NULL OR expires_at > ?", time.Now()).
		First(&permission).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return false, nil
		}
		return false, err
	}

	// Check resource access constraints
	return permission.MatchesResource("", resourceID), nil
}

// ==================== PERMISSION MANAGEMENT ====================

// GrantPermission grants permissions to a user (optimized batch insert)
func (r *PostgresPermissionRepository) GrantPermission(ctx context.Context, userID uuid.UUID, scope, resourceType string, actions []string, grantedBy *uuid.UUID, expiresAt *time.Time) (*entities.Permission, error) {
	return r.GrantPermissionWithResources(ctx, userID, scope, resourceType, actions, nil, grantedBy, expiresAt)
}

// GrantPermissionWithResources grants permissions with resource ID constraints
// Batch insert để optimize performance khi grant multiple actions
func (r *PostgresPermissionRepository) GrantPermissionWithResources(ctx context.Context, userID uuid.UUID, scope, resourceType string, actions []string, resourceIDs []string, grantedBy *uuid.UUID, expiresAt *time.Time) (*entities.Permission, error) {
	if len(actions) == 0 {
		return nil, fmt.Errorf("no actions specified")
	}

	var permissions []*entities.Permission
	now := time.Now()

	// Build permissions cho tất cả actions
	for _, action := range actions {
		permissionKey := entities.BuildPermissionKey(scope, resourceType, action)
		permission := &entities.Permission{
			UserID:        userID,
			PermissionKey: permissionKey,
			ResourceIDs:   resourceIDs,
			GrantedBy:     grantedBy,
			GrantedAt:     now,
			ExpiresAt:     expiresAt,
		}
		permissions = append(permissions, permission)
	}

	// Batch insert để optimize performance
	err := r.db.WithContext(ctx).CreateInBatches(permissions, 100).Error
	if err != nil {
		return nil, fmt.Errorf("failed to grant permissions: %w", err)
	}

	// Return first permission for compatibility
	return permissions[0], nil
}

// RevokePermission revokes specific permission scope/resource/action combination
func (r *PostgresPermissionRepository) RevokePermission(ctx context.Context, userID, scope, resourceType string) error {
	// Pattern match để revoke tất cả actions cho scope/resource
	keyPattern := fmt.Sprintf("%s:%s:%%", scope, resourceType)
	
	result := r.db.WithContext(ctx).
		Where("user_id = ? AND permission_key LIKE ?", userID, keyPattern).
		Delete(&entities.Permission{})

	return result.Error
}

// RevokeSpecificPermission revokes exact permission key
func (r *PostgresPermissionRepository) RevokeSpecificPermission(ctx context.Context, userID string, permissionKey string) error {
	result := r.db.WithContext(ctx).
		Where("user_id = ? AND permission_key = ?", userID, permissionKey).
		Delete(&entities.Permission{})

	return result.Error
}

// Interface compliance - need to add these missing methods that are used by handlers

// RevokeUserPermissions revokes all permissions for a user
func (r *PostgresPermissionRepository) RevokeUserPermissions(ctx context.Context, userID string) error {
	return r.db.WithContext(ctx).
		Where("user_id = ?", userID).
		Delete(&entities.Permission{}).Error
}

// RevokeOrganizationPermissions revokes all permissions for an organization
// Sử dụng khi delete organization để cleanup permissions
func (r *PostgresPermissionRepository) RevokeOrganizationPermissions(ctx context.Context, orgID string) error {
	orgScope := entities.BuildOrgScope(orgID)
	keyPattern := fmt.Sprintf("%s:%%", orgScope) // "O:uuid:%"
	
	result := r.db.WithContext(ctx).
		Where("permission_key LIKE ?", keyPattern).
		Delete(&entities.Permission{})

	return result.Error
}

// ==================== QUERY & SEARCH OPERATIONS ====================

// GetPermissionsByScope gets permissions by scope pattern (e.g., "O:uuid" for org permissions)
func (r *PostgresPermissionRepository) GetPermissionsByScope(ctx context.Context, scopePattern string) ([]*entities.Permission, error) {
	var permissions []*entities.Permission

	keyPattern := fmt.Sprintf("%s:%%", scopePattern)
	err := r.db.WithContext(ctx).
		Where("permission_key LIKE ?", keyPattern).
		Where("expires_at IS NULL OR expires_at > ?", time.Now()).
		Order("user_id, permission_key").
		Find(&permissions).Error

	return permissions, err
}

// GetUsersWithPermission gets all users who have specific permission (for admin queries)
func (r *PostgresPermissionRepository) GetUsersWithPermission(ctx context.Context, scope, resourceType, action string) ([]*entities.Permission, error) {
	// Build all possible permission keys that would grant this permission
	keys := r.buildPermissionKeysHierarchy(scope, resourceType, action)
	
	var permissions []*entities.Permission
	err := r.db.WithContext(ctx).
		Where("permission_key IN ?", keys).
		Where("expires_at IS NULL OR expires_at > ?", time.Now()).
		Order("user_id, permission_key").
		Find(&permissions).Error

	return permissions, err
}

// GetUsersByPermissionKey gets users with exact permission key
func (r *PostgresPermissionRepository) GetUsersByPermissionKey(ctx context.Context, permissionKey string) ([]*entities.Permission, error) {
	var permissions []*entities.Permission

	err := r.db.WithContext(ctx).
		Where("permission_key = ?", permissionKey).
		Where("expires_at IS NULL OR expires_at > ?", time.Now()).
		Order("user_id").
		Find(&permissions).Error

	return permissions, err
}

// ==================== MAINTENANCE OPERATIONS ====================

// GetExpiredPermissions gets all expired permissions
func (r *PostgresPermissionRepository) GetExpiredPermissions(ctx context.Context) ([]*entities.Permission, error) {
	var permissions []*entities.Permission

	err := r.db.WithContext(ctx).
		Where("expires_at IS NOT NULL AND expires_at <= ?", time.Now()).
		Order("expires_at ASC").
		Find(&permissions).Error

	return permissions, err
}

// CleanExpiredPermissions removes all expired permissions
func (r *PostgresPermissionRepository) CleanExpiredPermissions(ctx context.Context) (int64, error) {
	result := r.db.WithContext(ctx).
		Where("expires_at IS NOT NULL AND expires_at <= ?", time.Now()).
		Delete(&entities.Permission{})

	return result.RowsAffected, result.Error
}

// GetPermissionStats returns permission statistics for monitoring
func (r *PostgresPermissionRepository) GetPermissionStats(ctx context.Context) (map[string]any, error) {
	stats := make(map[string]any)

	// Total permissions
	var totalCount int64
	r.db.WithContext(ctx).Model(&entities.Permission{}).Count(&totalCount)
	stats["total_permissions"] = totalCount

	// Active permissions
	var activeCount int64
	r.db.WithContext(ctx).Model(&entities.Permission{}).
		Where("expires_at IS NULL OR expires_at > ?", time.Now()).
		Count(&activeCount)
	stats["active_permissions"] = activeCount

	// Expired permissions
	var expiredCount int64
	r.db.WithContext(ctx).Model(&entities.Permission{}).
		Where("expires_at IS NOT NULL AND expires_at <= ?", time.Now()).
		Count(&expiredCount)
	stats["expired_permissions"] = expiredCount

	// Unique users with permissions
	var uniqueUsers int64
	r.db.WithContext(ctx).Model(&entities.Permission{}).
		Distinct("user_id").
		Where("expires_at IS NULL OR expires_at > ?", time.Now()).
		Count(&uniqueUsers)
	stats["users_with_permissions"] = uniqueUsers

	return stats, nil
}

// ==================== HELPER METHODS ====================

// buildPermissionKeysHierarchy builds permission keys in hierarchy order
// Returns keys từ specific nhất đến general nhất để optimize permission checking
//
// Input: scope="O:uuid123", resourceType="license", action="read"
// Output: [
//   "O:uuid123:license:read",    // Exact match
//   "O:uuid123:license:*",       // All actions on license in org
//   "O:uuid123:*:read",          // Read any resource in org  
//   "O:uuid123:*:*",             // All actions on all resources in org
//   "S:license:read",            // System admin license read
//   "S:license:*",               // System admin all license actions
//   "S:*:read",                  // System admin read anything
//   "S:*:*"                      // System admin everything
// ]
func (r *PostgresPermissionRepository) buildPermissionKeysHierarchy(scope, resourceType, action string) []string {
	var keys []string

	// 1. Specific scope permissions (theo thứ tự ưu tiên)
	if scope != "" {
		keys = append(keys, entities.BuildPermissionKey(scope, resourceType, action))           // Exact match
		keys = append(keys, entities.BuildPermissionKey(scope, resourceType, entities.ActionAll))      // scope:resource:*
		keys = append(keys, entities.BuildPermissionKey(scope, entities.ResourceTypeAll, action))     // scope:*:action  
		keys = append(keys, entities.BuildPermissionKey(scope, entities.ResourceTypeAll, entities.ActionAll)) // scope:*:*
	}

	// 2. System permissions (highest priority fallback)
	keys = append(keys, entities.BuildPermissionKey(entities.ScopeSystem, resourceType, action))           // S:resource:action
	keys = append(keys, entities.BuildPermissionKey(entities.ScopeSystem, resourceType, entities.ActionAll))      // S:resource:*
	keys = append(keys, entities.BuildPermissionKey(entities.ScopeSystem, entities.ResourceTypeAll, action))     // S:*:action
	keys = append(keys, entities.BuildPermissionKey(entities.ScopeSystem, entities.ResourceTypeAll, entities.ActionAll)) // S:*:*

	return keys
}


// BatchGrantPermissions grants multiple permissions efficiently  
// Sử dụng cho setup initial permissions hoặc role-based permission assignment
func (r *PostgresPermissionRepository) BatchGrantPermissions(ctx context.Context, grants []repositories.PermissionGrant) error {
	if len(grants) == 0 {
		return nil
	}

	var permissions []*entities.Permission
	now := time.Now()

	for _, grant := range grants {
		for _, action := range grant.Actions {
			permissionKey := entities.BuildPermissionKey(grant.Scope, grant.ResourceType, action)
			permission := &entities.Permission{
				UserID:        grant.UserID,
				PermissionKey: permissionKey,
				ResourceIDs:   grant.ResourceIDs,
				GrantedBy:     grant.GrantedBy,
				GrantedAt:     now,
				ExpiresAt:     grant.ExpiresAt,
			}
			permissions = append(permissions, permission)
		}
	}

	// Use transaction để ensure atomicity
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		return tx.CreateInBatches(permissions, 100).Error
	})
}