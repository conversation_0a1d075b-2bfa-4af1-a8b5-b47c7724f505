package repositories

import (
	"context"
	"fmt"

	"github.com/gokeys/gokeys/internal/domain/entities"
	"github.com/gokeys/gokeys/internal/domain/repositories"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

// PermissionAuditLogRepository implements PostgreSQL storage for permission audit logs
type PermissionAuditLogRepository struct {
	db *gorm.DB
}

// NewPermissionAuditLogRepository creates a new permission audit log repository instance
func NewPermissionAuditLogRepository(db *gorm.DB) repositories.PermissionAuditLogRepository {
	return &PermissionAuditLogRepository{
		db: db,
	}
}

// LogAction creates a new audit log entry
func (r *PermissionAuditLogRepository) LogAction(ctx context.Context, log *entities.PermissionAuditLog) error {
	if log.ID == uuid.Nil {
		log.ID = uuid.New()
	}
	
	return r.db.WithContext(ctx).Create(log).Error
}

// GetByID retrieves an audit log entry by ID
func (r *PermissionAuditLogRepository) GetByID(ctx context.Context, id uuid.UUID) (*entities.PermissionAuditLog, error) {
	var log entities.PermissionAuditLog
	err := r.db.WithContext(ctx).
		Preload("Actor").
		Preload("TargetUser").
		Preload("Role").
		First(&log, "id = ?", id).Error
		
	if err != nil {
		return nil, err
	}
	
	return &log, nil
}

// GetUserAuditLog retrieves audit logs for a specific user with pagination
func (r *PermissionAuditLogRepository) GetUserAuditLog(ctx context.Context, userID uuid.UUID, filter repositories.ListFilter) ([]*entities.PermissionAuditLog, int64, error) {
	query := r.db.WithContext(ctx).Where("target_user_id = ?", userID)
	
	// Apply filters
	query = r.applyFilters(query, filter)
	
	var logs []*entities.PermissionAuditLog
	var total int64
	
	// Count total records
	if err := query.Model(&entities.PermissionAuditLog{}).Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count audit logs: %w", err)
	}
	
	// Apply pagination
	if filter.PageSize > 0 {
		offset := (filter.Page - 1) * filter.PageSize
		query = query.Offset(offset).Limit(filter.PageSize)
	}
	
	// Apply sorting
	if filter.SortBy != "" {
		order := filter.SortBy
		if filter.SortOrder == "DESC" {
			order += " DESC"
		} else {
			order += " ASC"
		}
		query = query.Order(order)
	} else {
		query = query.Order("created_at DESC")
	}
	
	// Execute query with preloads
	if err := query.
		Preload("Actor").
		Preload("TargetUser").
		Preload("Role").
		Find(&logs).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to get user audit logs: %w", err)
	}
	
	return logs, total, nil
}

// GetActorAuditLog retrieves audit logs for actions performed by a specific actor
func (r *PermissionAuditLogRepository) GetActorAuditLog(ctx context.Context, actorID uuid.UUID, filter repositories.ListFilter) ([]*entities.PermissionAuditLog, int64, error) {
	query := r.db.WithContext(ctx).Where("actor_id = ?", actorID)
	
	// Apply filters
	query = r.applyFilters(query, filter)
	
	var logs []*entities.PermissionAuditLog
	var total int64
	
	// Count total records
	if err := query.Model(&entities.PermissionAuditLog{}).Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count audit logs: %w", err)
	}
	
	// Apply pagination
	if filter.PageSize > 0 {
		offset := (filter.Page - 1) * filter.PageSize
		query = query.Offset(offset).Limit(filter.PageSize)
	}
	
	// Apply sorting
	if filter.SortBy != "" {
		order := filter.SortBy
		if filter.SortOrder == "DESC" {
			order += " DESC"
		} else {
			order += " ASC"
		}
		query = query.Order(order)
	} else {
		query = query.Order("created_at DESC")
	}
	
	// Execute query with preloads
	if err := query.
		Preload("Actor").
		Preload("TargetUser").
		Preload("Role").
		Find(&logs).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to get actor audit logs: %w", err)
	}
	
	return logs, total, nil
}

// GetRecentActions retrieves the most recent audit log entries
func (r *PermissionAuditLogRepository) GetRecentActions(ctx context.Context, limit int) ([]*entities.PermissionAuditLog, error) {
	var logs []*entities.PermissionAuditLog
	
	query := r.db.WithContext(ctx).
		Order("created_at DESC").
		Limit(limit)
	
	err := query.
		Preload("Actor").
		Preload("TargetUser").
		Preload("Role").
		Find(&logs).Error
		
	if err != nil {
		return nil, fmt.Errorf("failed to get recent actions: %w", err)
	}
	
	return logs, nil
}

// applyFilters applies common filters to the query
func (r *PermissionAuditLogRepository) applyFilters(query *gorm.DB, filter repositories.ListFilter) *gorm.DB {
	// Apply search filter
	if filter.Search != "" {
		searchPattern := "%" + filter.Search + "%"
		query = query.Where("action ILIKE ? OR permission_key ILIKE ? OR reason ILIKE ?", 
			searchPattern, searchPattern, searchPattern)
	}
	
	// Apply custom filters
	for key, value := range filter.Filters {
		switch key {
		case "action":
			query = query.Where("action = ?", value)
		case "permission_key":
			query = query.Where("permission_key = ?", value)
		case "role_id":
			query = query.Where("role_id = ?", value)
		case "actor_ip":
			query = query.Where("actor_ip = ?", value)
		case "from_date":
			query = query.Where("created_at >= ?", value)
		case "to_date":
			query = query.Where("created_at <= ?", value)
		default:
			// Handle any other filters
			query = query.Where(key+" = ?", value)
		}
	}
	
	return query
}

// CleanupOldLogs removes audit logs older than the specified retention period
func (r *PermissionAuditLogRepository) CleanupOldLogs(ctx context.Context, retentionDays int) (int64, error) {
	// Calculate cutoff date
	cutoffDate := gorm.Expr("CURRENT_TIMESTAMP - INTERVAL '? days'", retentionDays)
	
	result := r.db.WithContext(ctx).
		Where("created_at < ?", cutoffDate).
		Delete(&entities.PermissionAuditLog{})
		
	return result.RowsAffected, result.Error
}

// GetStatistics returns audit log statistics
func (r *PermissionAuditLogRepository) GetStatistics(ctx context.Context, days int) (map[string]interface{}, error) {
	stats := make(map[string]interface{})
	
	// Get action counts
	var actionCounts []struct {
		Action string
		Count  int64
	}
	
	err := r.db.WithContext(ctx).
		Model(&entities.PermissionAuditLog{}).
		Where("created_at > CURRENT_TIMESTAMP - INTERVAL '? days'", days).
		Select("action, COUNT(*) as count").
		Group("action").
		Order("count DESC").
		Scan(&actionCounts).Error
		
	if err != nil {
		return nil, fmt.Errorf("failed to get action statistics: %w", err)
	}
	
	// Get top actors
	var topActors []struct {
		ActorID uuid.UUID
		Count   int64
	}
	
	err = r.db.WithContext(ctx).
		Model(&entities.PermissionAuditLog{}).
		Where("created_at > CURRENT_TIMESTAMP - INTERVAL '? days'", days).
		Select("actor_id, COUNT(*) as count").
		Group("actor_id").
		Order("count DESC").
		Limit(10).
		Scan(&topActors).Error
		
	if err != nil {
		return nil, fmt.Errorf("failed to get top actors: %w", err)
	}
	
	// Get daily activity
	var dailyActivity []struct {
		Date  string
		Count int64
	}
	
	err = r.db.WithContext(ctx).
		Model(&entities.PermissionAuditLog{}).
		Where("created_at > CURRENT_TIMESTAMP - INTERVAL '? days'", days).
		Select("DATE(created_at) as date, COUNT(*) as count").
		Group("DATE(created_at)").
		Order("date DESC").
		Scan(&dailyActivity).Error
		
	if err != nil {
		return nil, fmt.Errorf("failed to get daily activity: %w", err)
	}
	
	stats["action_counts"] = actionCounts
	stats["top_actors"] = topActors
	stats["daily_activity"] = dailyActivity
	
	return stats, nil
}