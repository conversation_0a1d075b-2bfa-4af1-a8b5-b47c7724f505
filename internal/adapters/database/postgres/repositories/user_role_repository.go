package repositories

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/gokeys/gokeys/internal/domain/entities"
	"github.com/gokeys/gokeys/internal/domain/repositories"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

// UserRoleRepository implements PostgreSQL storage for user-role assignments
type UserRoleRepository struct {
	db *gorm.DB
}

// NewUserRoleRepository creates a new user role repository instance
func NewUserRoleRepository(db *gorm.DB) repositories.UserRoleRepository {
	return &UserRoleRepository{
		db: db,
	}
}

// Create creates a new user-role assignment
func (r *UserRoleRepository) Create(ctx context.Context, userRole *entities.UserRole) error {
	if userRole.ID == uuid.Nil {
		userRole.ID = uuid.New()
	}
	
	return r.db.WithContext(ctx).Create(userRole).Error
}

// GetByID retrieves a user-role assignment by ID
func (r *UserRoleRepository) GetByID(ctx context.Context, id uuid.UUID) (*entities.UserRole, error) {
	var userRole entities.UserRole
	err := r.db.WithContext(ctx).
		Preload("User").
		Preload("Role").
		Preload("Organization").
		Preload("Granter").
		First(&userRole, "id = ?", id).Error
		
	if err != nil {
		return nil, err
	}
	
	return &userRole, nil
}

// Update updates a user-role assignment
func (r *UserRoleRepository) Update(ctx context.Context, userRole *entities.UserRole) error {
	// Only allow updating certain fields
	updates := map[string]interface{}{
		"expires_at":  userRole.ExpiresAt,
		"conditions":  userRole.Conditions,
		"reason":      userRole.Reason,
		"updated_at":  gorm.Expr("CURRENT_TIMESTAMP"),
	}
	
	return r.db.WithContext(ctx).
		Model(&entities.UserRole{}).
		Where("id = ?", userRole.ID).
		Updates(updates).Error
}

// Delete deletes a user-role assignment
func (r *UserRoleRepository) Delete(ctx context.Context, id uuid.UUID) error {
	return r.db.WithContext(ctx).
		Delete(&entities.UserRole{}, "id = ?", id).Error
}

// AssignRole assigns a role to a user with context
func (r *UserRoleRepository) AssignRole(
	ctx context.Context,
	userID, roleID uuid.UUID,
	orgID *uuid.UUID,
	grantedBy uuid.UUID,
	expiresAt *time.Time,
	reason string,
) (*entities.UserRole, error) {
	// Check if assignment already exists
	var existingCount int64
	query := r.db.WithContext(ctx).
		Model(&entities.UserRole{}).
		Where("user_id = ? AND role_id = ?", userID, roleID)
		
	if orgID != nil {
		query = query.Where("organization_id = ?", *orgID)
	} else {
		query = query.Where("organization_id IS NULL")
	}
	
	if err := query.Count(&existingCount).Error; err != nil {
		return nil, fmt.Errorf("failed to check existing assignment: %w", err)
	}
	
	if existingCount > 0 {
		return nil, fmt.Errorf("role already assigned to user in this context")
	}
	
	// Create new assignment
	userRole := &entities.UserRole{
		ID:             uuid.New(),
		UserID:         userID,
		RoleID:         roleID,
		OrganizationID: orgID,
		GrantedBy:      grantedBy,
		GrantedAt:      time.Now(),
		ExpiresAt:      expiresAt,
		Reason:         reason,
	}
	
	if err := r.Create(ctx, userRole); err != nil {
		return nil, fmt.Errorf("failed to assign role: %w", err)
	}
	
	// Load relations
	if err := r.db.WithContext(ctx).
		Preload("Role").
		Preload("Organization").
		First(userRole, "id = ?", userRole.ID).Error; err != nil {
		return nil, fmt.Errorf("failed to load role assignment: %w", err)
	}
	
	return userRole, nil
}

// RevokeRole revokes a specific role from a user
func (r *UserRoleRepository) RevokeRole(ctx context.Context, userID, roleID uuid.UUID, orgID *uuid.UUID) error {
	query := r.db.WithContext(ctx).
		Where("user_id = ? AND role_id = ?", userID, roleID)
		
	if orgID != nil {
		query = query.Where("organization_id = ?", *orgID)
	} else {
		query = query.Where("organization_id IS NULL")
	}
	
	result := query.Delete(&entities.UserRole{})
	if result.Error != nil {
		return fmt.Errorf("failed to revoke role: %w", result.Error)
	}
	
	if result.RowsAffected == 0 {
		return fmt.Errorf("role assignment not found")
	}
	
	return nil
}

// RevokeAllUserRoles revokes all roles from a user
func (r *UserRoleRepository) RevokeAllUserRoles(ctx context.Context, userID uuid.UUID) error {
	return r.db.WithContext(ctx).
		Where("user_id = ?", userID).
		Delete(&entities.UserRole{}).Error
}

// RevokeExpiredRoles removes all expired role assignments
func (r *UserRoleRepository) RevokeExpiredRoles(ctx context.Context) (int64, error) {
	result := r.db.WithContext(ctx).
		Where("expires_at IS NOT NULL AND expires_at < ?", time.Now()).
		Delete(&entities.UserRole{})
		
	return result.RowsAffected, result.Error
}

// GetUserRoles gets all roles assigned to a user
func (r *UserRoleRepository) GetUserRoles(ctx context.Context, userID uuid.UUID) ([]*entities.UserRole, error) {
	var userRoles []*entities.UserRole
	err := r.db.WithContext(ctx).
		Preload("Role").
		Preload("Organization").
		Preload("Granter").
		Where("user_id = ?", userID).
		Order("granted_at DESC").
		Find(&userRoles).Error
		
	if err != nil {
		return nil, fmt.Errorf("failed to get user roles: %w", err)
	}
	
	return userRoles, nil
}

// GetUserRolesByOrganization gets all roles assigned to a user in a specific organization
func (r *UserRoleRepository) GetUserRolesByOrganization(ctx context.Context, userID, orgID uuid.UUID) ([]*entities.UserRole, error) {
	var userRoles []*entities.UserRole
	err := r.db.WithContext(ctx).
		Preload("Role").
		Preload("Organization").
		Preload("Granter").
		Where("user_id = ? AND organization_id = ?", userID, orgID).
		Order("granted_at DESC").
		Find(&userRoles).Error
		
	if err != nil {
		return nil, fmt.Errorf("failed to get user organization roles: %w", err)
	}
	
	return userRoles, nil
}

// GetRoleUsers gets all users assigned to a specific role
func (r *UserRoleRepository) GetRoleUsers(ctx context.Context, roleID uuid.UUID) ([]*entities.UserRole, error) {
	var userRoles []*entities.UserRole
	err := r.db.WithContext(ctx).
		Preload("User").
		Preload("Role").
		Preload("Organization").
		Where("role_id = ?", roleID).
		Order("granted_at DESC").
		Find(&userRoles).Error
		
	if err != nil {
		return nil, fmt.Errorf("failed to get role users: %w", err)
	}
	
	return userRoles, nil
}

// GetOrganizationUserRoles gets all user-role assignments in an organization
func (r *UserRoleRepository) GetOrganizationUserRoles(ctx context.Context, orgID uuid.UUID) ([]*entities.UserRole, error) {
	var userRoles []*entities.UserRole
	err := r.db.WithContext(ctx).
		Preload("User").
		Preload("Role").
		Preload("Granter").
		Where("organization_id = ?", orgID).
		Order("user_id, granted_at DESC").
		Find(&userRoles).Error
		
	if err != nil {
		return nil, fmt.Errorf("failed to get organization user roles: %w", err)
	}
	
	return userRoles, nil
}

// HasRole checks if a user has a specific role
func (r *UserRoleRepository) HasRole(ctx context.Context, userID, roleID uuid.UUID, orgID *uuid.UUID) (bool, error) {
	var count int64
	query := r.db.WithContext(ctx).
		Model(&entities.UserRole{}).
		Where("user_id = ? AND role_id = ?", userID, roleID)
		
	if orgID != nil {
		query = query.Where("organization_id = ?", *orgID)
	} else {
		query = query.Where("organization_id IS NULL")
	}
	
	// Check not expired
	query = query.Where("(expires_at IS NULL OR expires_at > ?)", time.Now())
	
	if err := query.Count(&count).Error; err != nil {
		return false, fmt.Errorf("failed to check role: %w", err)
	}
	
	return count > 0, nil
}

// GetUserEffectivePermissions gets all effective permissions from user's roles
func (r *UserRoleRepository) GetUserEffectivePermissions(ctx context.Context, userID uuid.UUID, orgID *uuid.UUID) ([]string, error) {
	// Build query to get active user roles
	query := r.db.WithContext(ctx).
		Table("user_roles ur").
		Joins("JOIN roles r ON ur.role_id = r.id").
		Where("ur.user_id = ?", userID).
		Where("r.is_active = ?", true).
		Where("(ur.expires_at IS NULL OR ur.expires_at > ?)", time.Now())
	
	// Filter by organization if specified
	if orgID != nil {
		// Get roles for specific org OR system roles
		query = query.Where("(ur.organization_id = ? OR r.scope = ?)", *orgID, entities.RoleScopeSystem)
	}
	
	// Select role permissions
	var roleData []struct {
		Permissions []string
		OrgID       *uuid.UUID
		Scope       string
	}
	
	err := query.
		Select("r.permissions, ur.organization_id as org_id, r.scope").
		Scan(&roleData).Error
		
	if err != nil {
		return nil, fmt.Errorf("failed to get user permissions: %w", err)
	}
	
	// Collect all permissions, replacing org placeholders
	permissionMap := make(map[string]bool)
	for _, data := range roleData {
		for _, perm := range data.Permissions {
			// Replace {{org_id}} placeholder if needed
			if data.Scope == string(entities.RoleScopeOrganization) && data.OrgID != nil {
				perm = strings.ReplaceAll(perm, "{{org_id}}", data.OrgID.String())
			}
			permissionMap[perm] = true
		}
	}
	
	// Convert map to slice
	permissions := make([]string, 0, len(permissionMap))
	for perm := range permissionMap {
		permissions = append(permissions, perm)
	}
	
	return permissions, nil
}