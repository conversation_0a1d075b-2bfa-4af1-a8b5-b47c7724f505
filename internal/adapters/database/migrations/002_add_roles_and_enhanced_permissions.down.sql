-- Rollback migration: Remove roles and enhanced permissions
-- =========================================================

-- Drop triggers first
DROP TRIGGER IF EXISTS permission_audit_trigger ON permissions;
DROP TRIGGER IF EXISTS update_roles_updated_at ON roles;
DROP TRIGGER IF EXISTS update_user_roles_updated_at ON user_roles;

-- Drop functions
DROP FUNCTION IF EXISTS log_permission_change();
DROP FUNCTION IF EXISTS get_user_effective_permissions(UUID, UUID);
DROP FUNCTION IF EXISTS can_delegate_permission(UUID, VARCHAR(255), UUID);

-- Drop new columns from permissions table
ALTER TABLE permissions DROP CONSTRAINT IF EXISTS fk_delegated_from;
ALTER TABLE permissions DROP COLUMN IF EXISTS delegation_allowed;
ALTER TABLE permissions DROP COLUMN IF EXISTS delegated_from;
ALTER TABLE permissions DROP COLUMN IF EXISTS max_delegation_depth;
ALTER TABLE permissions DROP COLUMN IF EXISTS conditions;
ALTER TABLE permissions DROP COLUMN IF EXISTS reason;

-- Drop tables in reverse order of dependencies
DROP TABLE IF EXISTS permission_audit_log;
DROP TABLE IF EXISTS user_roles;
DROP TABLE IF EXISTS roles;