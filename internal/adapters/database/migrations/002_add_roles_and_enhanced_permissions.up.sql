-- Migration: Add roles and enhance permissions for flexible authorization
-- =====================================================================

-- ==================================================
-- ROLES - Define reusable permission sets
-- ==================================================
CREATE TABLE roles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- Role identification
    name VARCHAR(255) NOT NULL,
    display_name VARCHAR(255) NOT NULL,
    description TEXT,
    
    -- Role scope: 'system', 'organization', 'custom'
    scope VARCHAR(50) NOT NULL CHECK (scope IN ('system', 'organization', 'custom')),
    
    -- Organization context (NULL for system roles)
    organization_id UUID,
    
    -- Predefined permissions for this role
    -- Format: Array of permission keys like ["S:*:*", "O:org-id:product:read"]
    permissions JSONB DEFAULT '[]',
    
    -- Conditions and constraints for the role
    -- Can include time-based access, IP restrictions, etc.
    conditions JSONB DEFAULT '{}',
    
    -- Metadata
    is_system BOOLEAN DEFAULT FALSE, -- System roles cannot be modified
    is_active BOOLEAN DEFAULT TRUE,
    created_by UUID,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
    
    -- Ensure unique role names within scope
    UNIQUE(name, organization_id),
    
    -- Ensure organization_id is set for org-scoped roles
    CONSTRAINT check_org_scope CHECK (
        (scope = 'organization' AND organization_id IS NOT NULL) OR
        (scope != 'organization' AND organization_id IS NULL)
    )
);

-- ==================================================
-- USER_ROLES - Assign roles to users with context
-- ==================================================
CREATE TABLE user_roles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- Core relationship
    user_id UUID NOT NULL,
    role_id UUID NOT NULL,
    
    -- Organization context for the role assignment
    -- This allows same user to have different roles in different orgs
    organization_id UUID,
    
    -- Assignment metadata
    granted_by UUID NOT NULL,
    granted_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP WITH TIME ZONE,
    
    -- Additional context/conditions for this specific assignment
    conditions JSONB DEFAULT '{}',
    
    -- Reason for assignment (audit trail)
    reason TEXT,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE,
    FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE CASCADE,
    FOREIGN KEY (granted_by) REFERENCES users(id) ON DELETE SET NULL,
    
    -- Prevent duplicate role assignments in same context
    UNIQUE(user_id, role_id, organization_id)
);

-- ==================================================
-- Enhance PERMISSIONS table with delegation support
-- ==================================================
ALTER TABLE permissions ADD COLUMN IF NOT EXISTS delegation_allowed BOOLEAN DEFAULT FALSE;
ALTER TABLE permissions ADD COLUMN IF NOT EXISTS delegated_from UUID;
ALTER TABLE permissions ADD COLUMN IF NOT EXISTS max_delegation_depth INTEGER DEFAULT 0;
ALTER TABLE permissions ADD COLUMN IF NOT EXISTS conditions JSONB DEFAULT '{}';
ALTER TABLE permissions ADD COLUMN IF NOT EXISTS reason TEXT;

-- Add foreign key for delegation tracking
ALTER TABLE permissions 
    ADD CONSTRAINT fk_delegated_from 
    FOREIGN KEY (delegated_from) 
    REFERENCES permissions(id) 
    ON DELETE CASCADE;

-- ==================================================
-- PERMISSION_AUDIT_LOG - Track all permission changes
-- ==================================================
CREATE TABLE permission_audit_log (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- What happened
    action VARCHAR(50) NOT NULL CHECK (action IN ('grant', 'revoke', 'delegate', 'expire', 'update')),
    
    -- Who did it
    actor_id UUID NOT NULL,
    actor_ip INET,
    
    -- What was affected
    target_user_id UUID NOT NULL,
    permission_key VARCHAR(255),
    role_id UUID,
    
    -- Additional details
    old_value JSONB,
    new_value JSONB,
    reason TEXT,
    
    -- When it happened
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (actor_id) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (target_user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE SET NULL
);

-- ==================================================
-- Create default system roles
-- ==================================================
INSERT INTO roles (name, display_name, description, scope, permissions, is_system) VALUES
    ('system_admin', 'System Administrator', 'Full system access', 'system', '["S:*:*"]', true),
    ('org_admin', 'Organization Administrator', 'Full organization access', 'organization', '["O:{{org_id}}:*:*"]', true),
    ('org_manager', 'Organization Manager', 'Manage organization resources', 'organization', 
        '["O:{{org_id}}:product:*", "O:{{org_id}}:license:*", "O:{{org_id}}:user:read"]', true),
    ('org_member', 'Organization Member', 'Basic organization access', 'organization', 
        '["O:{{org_id}}:product:read", "O:{{org_id}}:license:read"]', true);

-- ==================================================
-- Indexes for performance
-- ==================================================

-- Roles indexes
CREATE INDEX idx_roles_organization ON roles(organization_id) WHERE organization_id IS NOT NULL;
CREATE INDEX idx_roles_scope ON roles(scope);
CREATE INDEX idx_roles_active ON roles(is_active) WHERE is_active = true;

-- User roles indexes
CREATE INDEX idx_user_roles_user ON user_roles(user_id);
CREATE INDEX idx_user_roles_role ON user_roles(role_id);
CREATE INDEX idx_user_roles_org ON user_roles(organization_id) WHERE organization_id IS NOT NULL;
CREATE INDEX idx_user_roles_expires ON user_roles(expires_at) WHERE expires_at IS NOT NULL;

-- Enhanced permissions indexes
CREATE INDEX idx_permissions_delegated ON permissions(delegated_from) WHERE delegated_from IS NOT NULL;
CREATE INDEX idx_permissions_delegation_allowed ON permissions(delegation_allowed) WHERE delegation_allowed = true;

-- Audit log indexes
CREATE INDEX idx_audit_log_actor ON permission_audit_log(actor_id);
CREATE INDEX idx_audit_log_target ON permission_audit_log(target_user_id);
CREATE INDEX idx_audit_log_created ON permission_audit_log(created_at);
CREATE INDEX idx_audit_log_action ON permission_audit_log(action);

-- ==================================================
-- Helper functions
-- ==================================================

-- Function to get all effective permissions for a user
CREATE OR REPLACE FUNCTION get_user_effective_permissions(p_user_id UUID, p_org_id UUID DEFAULT NULL)
RETURNS TABLE (
    permission_key VARCHAR(255),
    source VARCHAR(50), -- 'direct', 'role', 'delegated'
    expires_at TIMESTAMP WITH TIME ZONE,
    conditions JSONB
) AS $$
BEGIN
    RETURN QUERY
    -- Direct permissions
    SELECT 
        p.permission_key,
        'direct'::VARCHAR(50) as source,
        p.expires_at,
        p.conditions
    FROM permissions p
    WHERE p.user_id = p_user_id
        AND (p.expires_at IS NULL OR p.expires_at > NOW())
    
    UNION
    
    -- Role-based permissions
    SELECT 
        CASE 
            WHEN r.scope = 'organization' THEN 
                REPLACE(perm::text, '{{org_id}}', ur.organization_id::text)::VARCHAR(255)
            ELSE 
                perm::text::VARCHAR(255)
        END as permission_key,
        'role'::VARCHAR(50) as source,
        ur.expires_at,
        COALESCE(ur.conditions, '{}'::JSONB) as conditions
    FROM user_roles ur
    JOIN roles r ON ur.role_id = r.id
    CROSS JOIN LATERAL jsonb_array_elements_text(r.permissions) as perm
    WHERE ur.user_id = p_user_id
        AND r.is_active = true
        AND (ur.expires_at IS NULL OR ur.expires_at > NOW())
        AND (p_org_id IS NULL OR ur.organization_id = p_org_id OR r.scope = 'system');
END;
$$ LANGUAGE plpgsql;

-- Function to check if user can delegate a permission
CREATE OR REPLACE FUNCTION can_delegate_permission(
    p_user_id UUID, 
    p_permission_key VARCHAR(255),
    p_target_user_id UUID
) RETURNS BOOLEAN AS $$
DECLARE
    v_can_delegate BOOLEAN;
    v_delegation_depth INTEGER;
BEGIN
    -- Check if user has the permission with delegation allowed
    SELECT 
        p.delegation_allowed,
        COALESCE(p.max_delegation_depth, 0)
    INTO 
        v_can_delegate,
        v_delegation_depth
    FROM permissions p
    WHERE p.user_id = p_user_id
        AND p.permission_key = p_permission_key
        AND (p.expires_at IS NULL OR p.expires_at > NOW())
    LIMIT 1;
    
    -- If permission not found or delegation not allowed
    IF NOT FOUND OR NOT v_can_delegate THEN
        RETURN FALSE;
    END IF;
    
    -- Check delegation depth hasn't been exceeded
    -- (Implementation depends on tracking delegation chain)
    
    RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- ==================================================
-- Triggers
-- ==================================================

-- Trigger to log permission changes
CREATE OR REPLACE FUNCTION log_permission_change() RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        INSERT INTO permission_audit_log (action, actor_id, target_user_id, permission_key, new_value)
        VALUES ('grant', NEW.granted_by, NEW.user_id, NEW.permission_key, row_to_json(NEW));
    ELSIF TG_OP = 'DELETE' THEN
        INSERT INTO permission_audit_log (action, actor_id, target_user_id, permission_key, old_value)
        VALUES ('revoke', OLD.granted_by, OLD.user_id, OLD.permission_key, row_to_json(OLD));
    ELSIF TG_OP = 'UPDATE' THEN
        INSERT INTO permission_audit_log (action, actor_id, target_user_id, permission_key, old_value, new_value)
        VALUES ('update', NEW.granted_by, NEW.user_id, NEW.permission_key, row_to_json(OLD), row_to_json(NEW));
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER permission_audit_trigger
    AFTER INSERT OR UPDATE OR DELETE ON permissions
    FOR EACH ROW EXECUTE FUNCTION log_permission_change();

-- Trigger to update timestamps
CREATE TRIGGER update_roles_updated_at
    BEFORE UPDATE ON roles
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_roles_updated_at
    BEFORE UPDATE ON user_roles
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- ==================================================
-- Comments for documentation
-- ==================================================
COMMENT ON TABLE roles IS 'Defines reusable permission sets that can be assigned to users';
COMMENT ON TABLE user_roles IS 'Maps users to roles with organizational context';
COMMENT ON TABLE permission_audit_log IS 'Audit trail for all permission-related changes';
COMMENT ON COLUMN permissions.delegation_allowed IS 'Whether this permission can be delegated to other users';
COMMENT ON COLUMN permissions.delegated_from IS 'Reference to the original permission if this was delegated';
COMMENT ON COLUMN permissions.max_delegation_depth IS 'Maximum number of delegation levels allowed (0 = no sub-delegation)';
COMMENT ON COLUMN permissions.conditions IS 'Additional conditions/constraints for this permission (time-based, IP-based, etc.)';