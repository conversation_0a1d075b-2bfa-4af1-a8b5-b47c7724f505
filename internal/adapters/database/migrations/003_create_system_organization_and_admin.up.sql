-- Create system organization and super admin user
-- This migration creates the default organization and admin user for system setup
-- Created: 2025-07-24

-- ==================================================
-- SYSTEM ORGANIZATION
-- ==================================================
-- Create the system organization (acts as the platform owner)
INSERT INTO organizations (
    id,
    name,
    slug,
    email,
    type,
    status,
    protected,
    settings,
    metadata,
    created_at,
    updated_at
) VALUES (
    'ffffffff-ffff-ffff-ffff-ffffffffffff', -- Fixed UUID for system org
    'GoKeys System',
    'gokeys-system',
    '<EMAIL>',
    'vendor',
    'active',
    true, -- Protected from deletion
    '{"system": true, "can_create_organizations": true, "can_manage_all_resources": true}',
    '{"description": "System organization for GoKeys platform management", "created_by": "migration"}',
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
);

-- ==================================================
-- SUPER ADMIN USER
-- ==================================================
-- Create super admin user (password: admin123 - should be changed in production)
INSERT INTO users (
    id,
    email,
    password, -- bcrypt hash of "admin123"
    first_name,
    last_name,
    status,
    totp_enabled,
    metadata,
    created_at,
    updated_at
) VALUES (
    'eeeeeeee-eeee-eeee-eeee-eeeeeeeeeeee', -- Fixed UUID for super admin
    '<EMAIL>',
    '$2a$10$zFJW0J2fzjhDK3f69rrXaejBEmcRlVB8sZ38HHbBYajZXp.9JJ5Lm', -- bcrypt hash of "admin123"
    'Super',
    'Admin',
    'active',
    false,
    '{"system_user": true, "created_by": "migration", "role": "super_admin"}',
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
);

-- ==================================================
-- ORGANIZATION MEMBERSHIPS
-- ==================================================
-- Super admin belongs to system organization
INSERT INTO users_organizations (user_id, organization_id, invited_by) VALUES
    ('eeeeeeee-eeee-eeee-eeee-eeeeeeeeeeee', 'ffffffff-ffff-ffff-ffff-ffffffffffff', NULL);

-- ==================================================
-- PERMISSIONS
-- ==================================================
-- Super admin has system-wide permissions
INSERT INTO permissions (user_id, scope, resource_type, actions, granted_by) VALUES
    ('eeeeeeee-eeee-eeee-eeee-eeeeeeeeeeee', 'system', '*', ARRAY['*'], NULL);

-- ==================================================
-- DEFAULT VENDOR ORGANIZATION
-- ==================================================
-- Create a default vendor organization for testing/demo
INSERT INTO organizations (
    id,
    name,
    slug,
    email,
    type,
    status,
    protected,
    settings,
    metadata,
    created_at,
    updated_at
) VALUES (
    'cccccccc-cccc-cccc-cccc-cccccccccccc', -- Fixed UUID for demo vendor
    'Demo Vendor',
    'demo-vendor',
    '<EMAIL>',
    'vendor',
    'active',
    false,
    '{"demo": true, "tier": "standard"}',
    '{"description": "Default vendor organization for testing and demos", "created_by": "migration"}',
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
);

-- ==================================================
-- DEMO VENDOR ADMIN
-- ==================================================
-- Create demo vendor admin (password: vendor123)
INSERT INTO users (
    id,
    email,
    password, -- bcrypt hash of "vendor123"
    first_name,
    last_name,
    status,
    totp_enabled,
    metadata,
    created_at,
    updated_at
) VALUES (
    'bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', -- Fixed UUID for demo vendor admin
    '<EMAIL>',
    '$2a$10$gyPA7bImMXeaeBqwvXB2xOh8V3nVN6iip.X3D.qh62en3O7tZSxXm', -- bcrypt hash of "vendor123"
    'Vendor',
    'Admin',
    'active',
    false,
    '{"demo_user": true, "created_by": "migration", "role": "vendor_admin"}',
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
);

-- Demo vendor admin belongs to demo vendor organization
INSERT INTO users_organizations (user_id, organization_id, invited_by) VALUES
    ('bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', 'cccccccc-cccc-cccc-cccc-cccccccccccc', 'eeeeeeee-eeee-eeee-eeee-eeeeeeeeeeee');

-- Demo vendor admin has full permissions within their organization
INSERT INTO permissions (user_id, scope, resource_type, actions, granted_by) VALUES
    ('bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', 'org:cccccccc-cccc-cccc-cccc-cccccccccccc', '*', ARRAY['*'], 'eeeeeeee-eeee-eeee-eeee-eeeeeeeeeeee');

-- ==================================================
-- DEMO CUSTOMER ORGANIZATION
-- ==================================================
-- Create a demo customer organization
INSERT INTO organizations (
    id,
    name,
    slug,
    email,
    type,
    status,
    protected,
    settings,
    metadata,
    created_at,
    updated_at
) VALUES (
    '*************-9999-9999-************', -- Fixed UUID for demo customer
    'Demo Customer Corp',
    'demo-customer',
    '<EMAIL>',
    'customer',
    'active',
    false,
    '{"demo": true, "tier": "basic"}',
    '{"description": "Default customer organization for testing license usage", "created_by": "migration"}',
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
);

-- ==================================================
-- DEMO CUSTOMER USER
-- ==================================================
-- Create demo customer user (password: customer123)
INSERT INTO users (
    id,
    email,
    password, -- bcrypt hash of "customer123"
    first_name,
    last_name,
    status,
    totp_enabled,
    metadata,
    created_at,
    updated_at
) VALUES (
    '*************-8888-8888-************', -- Fixed UUID for demo customer user
    '<EMAIL>',
    '$2a$10$B7E/MQwE7LRYMUXDtN9yJeMItjwaWP9ddtnjslWq0ovrQn6AbLOKG', -- bcrypt hash of "customer123"
    'Customer',
    'User',
    'active',
    false,
    '{"demo_user": true, "created_by": "migration", "role": "customer"}',
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
);

-- Demo customer user belongs to customer organization
INSERT INTO users_organizations (user_id, organization_id, invited_by) VALUES
    ('*************-8888-8888-************', '*************-9999-9999-************', 'eeeeeeee-eeee-eeee-eeee-eeeeeeeeeeee');

-- Demo customer has limited permissions within their organization and ownership-based permissions
INSERT INTO permissions (user_id, scope, resource_type, actions, granted_by) VALUES
    ('*************-8888-8888-************', 'org:*************-9999-9999-************', 'license', ARRAY['read'], 'eeeeeeee-eeee-eeee-eeee-eeeeeeeeeeee'),
    ('*************-8888-8888-************', 'owner', 'license', ARRAY['read', 'validate'], 'eeeeeeee-eeee-eeee-eeee-eeeeeeeeeeee'),
    ('*************-8888-8888-************', 'owner', 'machine', ARRAY['*'], 'eeeeeeee-eeee-eeee-eeee-eeeeeeeeeeee');

-- ==================================================
-- SYSTEM NOTIFICATIONS
-- ==================================================
-- Log the creation of system entities with new permission model
DO $$
BEGIN
    RAISE NOTICE 'System refactored with new permission model:';
    RAISE NOTICE '';
    RAISE NOTICE 'Tables:';
    RAISE NOTICE '- users_organizations: Simple membership tracking';
    RAISE NOTICE '- permissions: Pure resource-based permissions';
    RAISE NOTICE '';
    RAISE NOTICE 'Default users:';
    RAISE NOTICE '- Super Admin: <EMAIL> (password: admin123)';
    RAISE NOTICE '  └─ Permission: system scope with * actions';
    RAISE NOTICE '- Vendor Admin: <EMAIL> (password: vendor123)';
    RAISE NOTICE '  └─ Permission: org:cccccccc-* scope with * actions';
    RAISE NOTICE '- Customer User: <EMAIL> (password: customer123)';
    RAISE NOTICE '  └─ Permissions: org scope (read) + owner scope (validate/manage)';
    RAISE NOTICE '';
    RAISE NOTICE 'Remember to change default passwords in production!';
END $$;