-- Rollback system organization and admin user creation
-- This migration removes the default organization and admin user
-- Created: 2025-07-24

-- Remove user organization relationships first (due to foreign key constraints)
DELETE FROM user_organizations WHERE id IN (
    'dddddddd-dddd-dddd-dddd-dddddddddddd', -- Super admin permission
    'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', -- Demo vendor admin permission
    '77777777-7777-7777-7777-777777777777'  -- Demo customer permission
);

-- Remove users
DELETE FROM users WHERE id IN (
    'eeeeeeee-eeee-eeee-eeee-eeeeeeeeeeee', -- Super admin
    'bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', -- Demo vendor admin
    '88888888-8888-8888-8888-888888888888'  -- Demo customer user
);

-- Remove organizations
DELETE FROM organizations WHERE id IN (
    'ffffffff-ffff-ffff-ffff-ffffffffffff', -- System organization
    'cccccccc-cccc-cccc-cccc-cccccccccccc', -- Demo vendor organization
    '99999999-9999-9999-9999-999999999999'  -- Demo customer organization
);

-- Log the removal
DO $$
BEGIN
    RAISE NOTICE 'System organization and users removed successfully';
END $$;