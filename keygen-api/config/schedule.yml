check_for_license_expirations:
  cron: "*/10 * * * *" # Every 10 mins (https://crontab.guru/every-10-minutes)
  class: "LicenseExpirationsWorker"

check_for_license_overdue_check_ins:
  cron: "*/10 * * * *" # Every 10 mins
  class: "LicenseOverdueCheckInsWorker"

cull_dead_machines:
  cron: "*/2 * * * *" # Every 2 mins
  class: "CullDeadMachinesWorker"

cull_dead_processes:
  cron: "*/2 * * * *" # Every 2 mins
  class: "CullDeadProcessesWorker"

report_account_request_limits:
  cron: "0 0 * * *" # Every day at midnight (6pm CST)
  class: "RequestLimitsReportWorker"
  status: <%= ENV['KEYGEN_MODE'] == 'multiplayer' ? 'enabled' : 'disabled' %>

prune_expired_tokens:
  cron: "0 1 * * *" # Every day at 1am (7pm CST)
  class: "PruneExpiredTokensWorker"

prune_expired_sessions:
  cron: "0 2 * * *" # Every day at 2am (8pm CST)
  class: "PruneExpiredSessionsWorker"

prune_release_download_links:
  cron: "15 2 * * *" # Every day at 2:15am (8:15pm CST)
  class: "PruneReleaseDownloadLinksWorker"

prune_release_upgrade_links:
  cron: "30 2 * * *" # Every day at 2:30am (8:30pm CST)
  class: "PruneReleaseUpgradeLinksWorker"

prune_request_logs:
  cron: "0 3 * * *" # Every day at 3am (9pm CST)
  class: "PruneRequestLogsWorker"

prune_event_logs:
  cron: "0 4 * * *" # Every day at 5am (10pm CST)
  class: "PruneEventLogsWorker"

prune_metrics:
  cron: "0 5 * * *" # Every day at 4am (11pm CST)
  class: "PruneMetricsWorker"

prune_webhook_events:
  cron: "0 6 * * *" # Every day at 6am (12am CST)
  class: "PruneWebhookEventsWorker"

vacuum_analyze_webhook_events:
  cron: "0 7 * * *" # Every day at 7am (1am CST)
  class: "VacuumAnalyzeWebhookEventsWorker"

vacuum_analyze_metrics:
  cron: "0 8 * * *" # Every day at 8am (2am CST)
  class: "VacuumAnalyzeMetricsWorker"

vacuum_analyze_request_logs:
  cron: "0 9 * * *" # Every day at 9am (3am CST)
  class: "VacuumAnalyzeRequestLogsWorker"

vacuum_analyze_event_logs:
  cron: "0 10 * * *" # Every day at 10am (4am CST)
  class: "VacuumAnalyzeEventLogsWorker"
