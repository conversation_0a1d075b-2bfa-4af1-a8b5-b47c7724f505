default: &default
  adapter: postgresql
  pool: <%= ENV['DB_POOL'] || (Sidekiq.server? ? ENV.fetch('SIDEKIQ_CONCURRENCY', 10) : ENV.fetch('RAILS_MAX_THREADS', 2)) %>
  timeout: <%= ENV['DB_TIMEOUT'] || 5000 %>
  prepared_statements: false
  variables:
    statement_timeout: <%= ENV["DB_STATEMENT_TIMEOUT"] || '15s' %>

development:
  <<: *default
  database: keygen_dev

test: &test
  <<: *default
  database: keygen_test<%= ENV['TEST_ENV_NUMBER'] %>

production:
  <<: *default
  url: <%= ENV['DATABASE_CONNECTION_POOL_URL'] || ENV['DATABASE_URL'] %>

cucumber:
  <<: *test
