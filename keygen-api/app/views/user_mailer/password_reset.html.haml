-# frozen_string_literal: true
- if @user.password?
  %p
    A password reset request was issued for a user profile signed up under this
    email address for <strong>#{@account.name}</strong>. To reset your password,
    follow the link below before #{@expiry.strftime("%m/%d/%Y at %-I:%M %p (%Z)")}.
    %br
    %br
    = link_to "https://app.keygen.sh/recover/#{@token}", "https://app.keygen.sh/recover/#{@token}", class: "ellipsis"
  %p
    If you did not request your password to be reset, simply ignore this email and
    your password will continue to stay the same.
- else
  %p
    You've been invited to <strong>#{@account.name}</strong>. Please confirm your email
    and set a password by following the link below before #{@expiry.strftime("%m/%d/%Y at %-I:%M %p (%Z)")}.
    %br
    %br
    = link_to "https://app.keygen.sh/recover/#{@token}", "https://app.keygen.sh/recover/#{@token}", class: "ellipsis"
  %p
    If you or a teammate did not request an invite, simply ignore this email.
