-# frozen_string_literal: true
!!!
%html
  %head
    %meta(content="width=device-width" name="viewport")
    %meta(content="text/html; charset=UTF-8" http-equiv="Content-Type")
    %title= message.subject
    :sass
      img
        border: none
        -ms-interpolation-mode: bicubic
        max-width: 100%
      body
        background-color: #f6f6f6
        font-family: sans-serif
        -webkit-font-smoothing: antialiased
        font-size: 14px
        line-height: 1.4
        margin: 0
        padding: 0
        -ms-text-size-adjust: 100%
        -webkit-text-size-adjust: 100%
      table
        border-collapse: separate
        mso-table-lspace: 0pt
        mso-table-rspace: 0pt
        width: 100%
        td
          font-family: sans-serif
          font-size: 14px
          vertical-align: top
      .body
        background-color: #f6f6f6
        width: 100%
      .container
        display: block
        Margin: 0 auto !important
        max-width: 580px
        padding: 10px
        width: auto !important
        width: 580px
      .content
        box-sizing: border-box
        display: block
        Margin: 0 auto
        max-width: 580px
        padding: 10px
      .main
        background: #fff
        width: 100%
        border-top: 4px solid #00ce75
        border-radius: 3px
        box-shadow: 0 2px 0 #e6e6e6
      .wrapper
        box-sizing: border-box
        padding: 20px
      .footer
        clear: both
        padding-top: 10px
        text-align: center
        width: 100%
        td,
        p,
        span,
        a
          color: #999999
          font-size: 12px
          text-align: center
      h1,
      h2,
      h3,
      h4
        color: #000000
        font-family: sans-serif
        font-weight: 400
        line-height: 1.4
        margin: 0
        Margin-bottom: 30px
      h1
        font-size: 35px
        font-weight: 300
        text-align: center
        text-transform: capitalize
      p,
      ul,
      ol
        font-family: sans-serif
        font-size: 14px
        font-weight: normal
        margin: 0
        Margin-bottom: 15px
        li
          list-style-position: inside
          margin-left: 5px
      a
        color: #00ce75
        text-decoration: underline
        &.ellipsis
          display: block
          text-overflow: ellipsis
          white-space: nowrap
          width: 500px
          overflow: hidden
      hr
        border: 0
        border-bottom: 1px solid #f6f6f6
        Margin: 20px 0
      p:last-of-type
        margin-bottom: 0
      @media only screen and (max-width: 620px)
        table[class=body] h1
          font-size: 28px !important
          margin-bottom: 10px !important
        table[class=body] p,
        table[class=body] ul,
        table[class=body] ol,
        table[class=body] td,
        table[class=body] span,
        table[class=body] a
          font-size: 16px !important
        table[class=body] .wrapper,
        table[class=body] .article
          padding: 10px !important
        table[class=body] .content
          padding: 0 !important
        table[class=body] .container
          padding: 0 !important
          width: 100% !important
        table[class=body] .main
          border-left-width: 0 !important
          border-radius: 0 !important
          border-right-width: 0 !important
        table[class=body] .btn table
          width: 100% !important
        table[class=body] .btn a
          width: 100% !important
        table[class=body] .img-responsive
          height: auto !important
          max-width: 100% !important
          width: auto !important
      @media all
        .ExternalClass
          width: 100%
        .ExternalClass,
        .ExternalClass p,
        .ExternalClass span,
        .ExternalClass font,
        .ExternalClass td,
        .ExternalClass div
          line-height: 100%
        .apple-link a
          color: inherit !important
          font-family: inherit !important
          font-size: inherit !important
          font-weight: inherit !important
          line-height: inherit !important
          text-decoration: none !important
  %body
    %table.body(border="0" cellpadding="0" cellspacing="0")
      %tr
        %td &nbsp;
        %td.container
          .content
            %table.main
              %tr
                %td.wrapper
                  %table(border="0" cellpadding="0" cellspacing="0")
                    %tr
                      %td.content-block
                        %p
                          Hi #{@user.first_name || 'there'},
                        = yield
          .footer
            %table(border="0" cellpadding="0" cellspacing="0")
              %tr
                %td.content-block
                  %span
                    Questions? Reply back or #{link_to 'contact us', "mailto:#{@account.email}"}.
        %td &nbsp;
