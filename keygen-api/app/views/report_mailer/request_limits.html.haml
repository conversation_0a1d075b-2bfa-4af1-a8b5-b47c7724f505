-# frozen_string_literal: true
%h3 Usage report for #{@date.strftime "%m/%d/%Y"}

%p
  There were #{number_with_delimiter @request_count} total requests across #{number_with_delimiter @reports.size} active
  accounts (#{number_with_delimiter @free_user_count} free,
  #{number_with_delimiter @paid_user_count} paid accounts).

%h4 New accounts

- if @new_accounts.any?
  %ul
    - @new_accounts.each do |account|
      %li= account.name
- else
  %p No new accounts for today

%h4 New products

- if @new_products.any?
  %ul
    - @new_products.each do |product|
      %li= product.name
- else
  %p No new products for today

%h4 Account activity

- if @reports.any?
  %ul
    - @reports.each do |report|
      %li
        %strong Account ID:
        %span #{report.account.id} / #{report.account.slug}
        %br
        %strong Contact:
        %span #{report.admin.full_name} / #{mail_to report.admin.email}
        %br
        %strong Requests:
        %span(style="color: #{report.request_limit_exceeded ? '#ff0041' : '#00ce75'}")
          #{number_with_delimiter report.request_count} / #{number_with_delimiter report.request_limit || 'unlimited'}
        %br
        %strong Licenses:
        %span(style="color: #{report.license_limit_exceeded ? '#ff0041' : '#00ce75'}")
          #{number_with_delimiter report.active_licensed_user_count} / #{number_with_delimiter report.license_limit || 'unlimited'} (total #{number_with_delimiter report.license_count} licenses)
        %br
        %strong Products:
        %span(style="color: #{report.product_limit_exceeded ? '#ff0041' : '#00ce75'}")
          #{number_with_delimiter report.product_count} / #{number_with_delimiter report.product_limit || 'unlimited'}
        %br
        %strong Admins:
        %span(style="color: #{report.admin_limit_exceeded ? '#ff0041' : '#00ce75'}")
          #{number_with_delimiter report.admin_count} / #{number_with_delimiter report.admin_limit || 'unlimited'}
        %br
        %strong Plan:
        %span #{report.account.plan.name} @ $#{number_with_delimiter report.account.plan.price&.div(100) || 0} / #{report.account.billing.state}
- else
  %p Nothing to report today
