name: Go CI

on:
  push:
    branches:
      - main
  pull_request:
    branches:
      - main

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - name: Set up Go
        uses: actions/setup-go@v5
        with:
          go-version: '1.24'

      - name: Install dependencies
        run: go mod tidy

      - name: <PERSON><PERSON>
        uses: golangci/golangci-lint-action@v4
        with:
          version: v1.59.1

      - name: Test
        run: go test -v ./...

      - name: Build Docker image
        run: docker build -t licenser-keygen-api-go:latest .
