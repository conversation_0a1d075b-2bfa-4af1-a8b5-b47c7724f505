{"name": "gokeys-frontend", "version": "0.1.0", "private": true, "workspaces": ["apps/*", "packages/*"], "scripts": {"build": "turbo run build", "dev": "turbo run dev", "lint": "turbo run lint", "clean": "turbo run clean", "type-check": "turbo run type-check"}, "devDependencies": {"autoprefixer": "^10.4.21", "postcss": "^8.5.6", "prettier": "^3.6.2", "tailwindcss": "^3.4.0", "turbo": "^2.5.5", "typescript": "5.8.3"}, "packageManager": "pnpm@10.0.0", "engines": {"node": ">=18"}, "dependencies": {"@auth/core": "^0.40.0", "next-auth": "5.0.0-beta.29"}}