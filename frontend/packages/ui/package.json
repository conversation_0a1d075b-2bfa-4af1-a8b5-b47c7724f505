{"name": "@repo/ui", "version": "0.1.0", "private": true, "main": "index.ts", "types": "index.ts", "scripts": {"lint": "eslint . --max-warnings 0", "type-check": "tsc --noEmit"}, "dependencies": {"@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.1.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.468.0", "react": "19.1.0", "react-hook-form": "^7.60.0", "tailwind-merge": "^2.6.0"}, "devDependencies": {"@types/react": "19.1.0", "typescript": "^5.8.3"}}