"use client"

import { useState } from "react"
import { But<PERSON> } from "./ui/button"
import { Badge } from "./ui/badge"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "./ui/card"
import { Input } from "./ui/input"
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "./ui/table"

interface Machine {
  id: string
  name: string
  fingerprint: string
  platform: string
  hostname: string
  license_key: string
  last_heartbeat: string
  status: 'active' | 'inactive' | 'banned'
  ip: string
  cores: number
  created_at: string
}

interface MachineTableProps {
  machines: Machine[]
  onViewDetails?: (machine: Machine) => void
  onDeactivate?: (machineId: string) => void
}

export function MachineTable({ machines, onViewDetails, onDeactivate }: MachineTableProps) {
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState<string>("all")

  const filteredMachines = machines.filter(machine => {
    const matchesSearch = machine.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         machine.hostname.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         machine.platform.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesStatus = statusFilter === "all" || machine.status === statusFilter
    
    return matchesSearch && matchesStatus
  })

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800'
      case 'inactive': return 'bg-gray-100 text-gray-800'
      case 'banned': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getLastSeenStatus = (heartbeat: string) => {
    const lastSeen = new Date(heartbeat)
    const now = new Date()
    const diffInMinutes = (now.getTime() - lastSeen.getTime()) / (1000 * 60)
    
    if (diffInMinutes < 5) return { status: 'Online', color: 'text-green-600' }
    if (diffInMinutes < 60) return { status: 'Recently active', color: 'text-yellow-600' }
    return { status: 'Offline', color: 'text-red-600' }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Registered Machines</CardTitle>
        <CardDescription>
          Manage devices and machines using your licenses
        </CardDescription>
        
        <div className="flex gap-4 pt-4">
          <Input
            placeholder="Search machines..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="max-w-sm"
          />
          
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-emerald-500"
          >
            <option value="all">All Status</option>
            <option value="active">Active</option>
            <option value="inactive">Inactive</option>
            <option value="banned">Banned</option>
          </select>
        </div>
      </CardHeader>
      
      <CardContent>
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Machine</TableHead>
                <TableHead>Platform</TableHead>
                <TableHead>License</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Last Seen</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredMachines.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={6} className="text-center py-8 text-gray-500">
                    No machines found
                  </TableCell>
                </TableRow>
              ) : (
                filteredMachines.map((machine) => {
                  const lastSeenStatus = getLastSeenStatus(machine.last_heartbeat)
                  
                  return (
                    <TableRow key={machine.id}>
                      <TableCell>
                        <div>
                          <div className="font-medium">{machine.name}</div>
                          <div className="text-sm text-gray-500">{machine.hostname}</div>
                          <div className="text-xs text-gray-400 font-mono">
                            {machine.fingerprint.slice(0, 16)}...
                          </div>
                        </div>
                      </TableCell>
                      
                      <TableCell>
                        <div>
                          <div className="font-medium">{machine.platform}</div>
                          <div className="text-sm text-gray-500">{machine.cores} cores</div>
                          <div className="text-xs text-gray-400">{machine.ip}</div>
                        </div>
                      </TableCell>
                      
                      <TableCell>
                        <div className="font-mono text-sm">
                          {machine.license_key.slice(0, 12)}...
                        </div>
                      </TableCell>
                      
                      <TableCell>
                        <Badge className={getStatusColor(machine.status)}>
                          {machine.status}
                        </Badge>
                      </TableCell>
                      
                      <TableCell>
                        <div>
                          <div className={`text-sm font-medium ${lastSeenStatus.color}`}>
                            {lastSeenStatus.status}
                          </div>
                          <div className="text-xs text-gray-500">
                            {formatDate(machine.last_heartbeat)}
                          </div>
                        </div>
                      </TableCell>
                      
                      <TableCell>
                        <div className="flex gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => onViewDetails?.(machine)}
                          >
                            View
                          </Button>
                          {machine.status === 'active' && onDeactivate && (
                            <Button
                              variant="outline"
                              size="sm"
                              className="text-red-600 hover:text-red-700"
                              onClick={() => onDeactivate(machine.id)}
                            >
                              Deactivate
                            </Button>
                          )}
                        </div>
                      </TableCell>
                    </TableRow>
                  )
                })
              )}
            </TableBody>
          </Table>
        </div>
        
        {filteredMachines.length > 0 && (
          <div className="mt-4 text-sm text-gray-500">
            Showing {filteredMachines.length} of {machines.length} machines
          </div>
        )}
      </CardContent>
    </Card>
  )
}