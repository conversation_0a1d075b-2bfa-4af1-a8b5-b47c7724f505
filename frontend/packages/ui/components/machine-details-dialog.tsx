"use client"

import { Bad<PERSON> } from "./ui/badge"
import { <PERSON><PERSON> } from "./ui/button"
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "./ui/card"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "./ui/dialog"

interface MachineComponent {
  id: string
  name: string
  version: string
  type: string
}

interface MachineProcess {
  id: string
  name: string
  pid: number
  cpu_usage: number
  memory_usage: number
}

interface Machine {
  id: string
  name: string
  fingerprint: string
  platform: string
  hostname: string
  license_key: string
  last_heartbeat: string
  status: 'active' | 'inactive' | 'banned'
  ip: string
  cores: number
  created_at: string
  components?: MachineComponent[]
  processes?: MachineProcess[]
  metadata?: Record<string, any>
}

interface MachineDetailsDialogProps {
  machine: Machine | null
  open: boolean
  onClose: () => void
  onDeactivate?: (machineId: string) => void
}

export function MachineDetailsDialog({ 
  machine, 
  open, 
  onClose, 
  onDeactivate 
}: MachineDetailsDialogProps) {
  if (!machine) return null

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800'
      case 'inactive': return 'bg-gray-100 text-gray-800'
      case 'banned': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getLastSeenStatus = (heartbeat: string) => {
    const lastSeen = new Date(heartbeat)
    const now = new Date()
    const diffInMinutes = (now.getTime() - lastSeen.getTime()) / (1000 * 60)
    
    if (diffInMinutes < 5) return { status: 'Online', color: 'text-green-600' }
    if (diffInMinutes < 60) return { status: 'Recently active', color: 'text-yellow-600' }
    return { status: 'Offline', color: 'text-red-600' }
  }

  const lastSeenStatus = getLastSeenStatus(machine.last_heartbeat)

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-3">
            <span>{machine.name}</span>
            <Badge className={getStatusColor(machine.status)}>
              {machine.status}
            </Badge>
          </DialogTitle>
          <DialogDescription>
            Machine details and monitoring information
          </DialogDescription>
        </DialogHeader>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">System Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div>
                <label className="text-sm font-medium text-gray-500">Hostname</label>
                <div className="font-mono">{machine.hostname}</div>
              </div>
              
              <div>
                <label className="text-sm font-medium text-gray-500">Platform</label>
                <div>{machine.platform}</div>
              </div>
              
              <div>
                <label className="text-sm font-medium text-gray-500">CPU Cores</label>
                <div>{machine.cores}</div>
              </div>
              
              <div>
                <label className="text-sm font-medium text-gray-500">IP Address</label>
                <div className="font-mono">{machine.ip}</div>
              </div>
              
              <div>
                <label className="text-sm font-medium text-gray-500">Machine Fingerprint</label>
                <div className="font-mono text-xs break-all">{machine.fingerprint}</div>
              </div>
            </CardContent>
          </Card>

          {/* License & Status */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">License & Status</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div>
                <label className="text-sm font-medium text-gray-500">License Key</label>
                <div className="font-mono text-sm break-all">{machine.license_key}</div>
              </div>
              
              <div>
                <label className="text-sm font-medium text-gray-500">Status</label>
                <div className={`font-medium ${lastSeenStatus.color}`}>
                  {lastSeenStatus.status}
                </div>
              </div>
              
              <div>
                <label className="text-sm font-medium text-gray-500">Last Heartbeat</label>
                <div>{formatDate(machine.last_heartbeat)}</div>
              </div>
              
              <div>
                <label className="text-sm font-medium text-gray-500">Registered</label>
                <div>{formatDate(machine.created_at)}</div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Components */}
        {machine.components && machine.components.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Installed Components</CardTitle>
              <CardDescription>
                Software components and libraries detected on this machine
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                {machine.components.map((component) => (
                  <div key={component.id} className="p-3 border rounded-lg">
                    <div className="font-medium">{component.name}</div>
                    <div className="text-sm text-gray-500">{component.type}</div>
                    <div className="text-xs text-gray-400">v{component.version}</div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Processes */}
        {machine.processes && machine.processes.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Running Processes</CardTitle>
              <CardDescription>
                Active processes using the licensed software
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {machine.processes.map((process) => (
                  <div key={process.id} className="flex items-center justify-between p-3 border rounded-lg">
                    <div>
                      <div className="font-medium">{process.name}</div>
                      <div className="text-sm text-gray-500">PID: {process.pid}</div>
                    </div>
                    <div className="text-right">
                      <div className="text-sm">CPU: {process.cpu_usage.toFixed(1)}%</div>
                      <div className="text-sm text-gray-500">
                        RAM: {(process.memory_usage / 1024 / 1024).toFixed(1)}MB
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Metadata */}
        {machine.metadata && Object.keys(machine.metadata).length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Additional Information</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {Object.entries(machine.metadata).map(([key, value]) => (
                  <div key={key} className="flex justify-between">
                    <span className="text-sm font-medium text-gray-500">{key}:</span>
                    <span className="text-sm font-mono">{String(value)}</span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Actions */}
        <div className="flex justify-end gap-3 pt-4 border-t">
          <Button variant="outline" onClick={onClose}>
            Close
          </Button>
          {machine.status === 'active' && onDeactivate && (
            <Button 
              variant="destructive"
              onClick={() => {
                onDeactivate(machine.id)
                onClose()
              }}
            >
              Deactivate Machine
            </Button>
          )}
        </div>
      </DialogContent>
    </Dialog>
  )
}