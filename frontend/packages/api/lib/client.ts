import axios, { AxiosInstance, AxiosRequestConfig } from 'axios'

export interface ApiClientConfig {
  baseURL: string
  timeout?: number
  accessToken?: string
}

export class ApiClient {
  private client: AxiosInstance

  constructor(config: ApiClientConfig) {
    this.client = axios.create({
      baseURL: config.baseURL,
      timeout: config.timeout || 10000,
      headers: {
        'Content-Type': 'application/json',
      },
    })

    // Add request interceptor for authentication
    this.client.interceptors.request.use((config) => {
      if (config.accessToken) {
        config.headers.Authorization = `Bearer ${config.accessToken}`
      }
      return config
    })

    // Add response interceptor for error handling
    this.client.interceptors.response.use(
      (response) => response,
      (error) => {
        if (error.response?.status === 401) {
          // Handle unauthorized - could trigger logout
          console.error('Unauthorized access - token may be expired')
        }
        return Promise.reject(error)
      }
    )
  }

  setAccessToken(token: string) {
    this.client.defaults.headers.common.Authorization = `Bearer ${token}`
  }

  clearAccessToken() {
    delete this.client.defaults.headers.common.Authorization
  }

  async get<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.client.get(url, config)
    return response.data
  }

  async post<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.client.post(url, data, config)
    return response.data
  }

  async put<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.client.put(url, data, config)
    return response.data
  }

  async patch<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.client.patch(url, data, config)
    return response.data
  }

  async delete<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.client.delete(url, config)
    return response.data
  }
}

// Create default client instance
export const createApiClient = (config: ApiClientConfig) => new ApiClient(config)