"use client"

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import type { 
  LicenseFilters, 
  License, 
  CreateLicenseRequest, 
  UpdateLicenseRequest,
  LicenseService 
} from '../index'

// Query Keys
export const licenseKeys = {
  all: ['licenses'] as const,
  lists: () => [...licenseKeys.all, 'list'] as const,
  list: (filters?: LicenseFilters) => [...licenseKeys.lists(), filters] as const,
  details: () => [...licenseKeys.all, 'detail'] as const,
  detail: (id: string) => [...licenseKeys.details(), id] as const,
}

// Hooks
export function useLicenses(
  licenseService: LicenseService,
  filters?: LicenseFilters
) {
  return useQuery({
    queryKey: licenseKeys.list(filters),
    queryFn: () => licenseService.getAll(filters),
    staleTime: 30 * 1000, // 30 seconds
  })
}

export function useLicense(
  licenseService: LicenseService,
  id: string
) {
  return useQuery({
    queryKey: licenseKeys.detail(id),
    queryFn: () => licenseService.getById(id),
    enabled: !!id,
  })
}

export function useCreateLicense(licenseService: LicenseService) {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (data: CreateLicenseRequest) => licenseService.create(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: licenseKeys.lists() })
    },
  })
}

export function useUpdateLicense(licenseService: LicenseService) {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateLicenseRequest }) =>
      licenseService.update(id, data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: licenseKeys.lists() })
      queryClient.invalidateQueries({ queryKey: licenseKeys.detail(variables.id) })
    },
  })
}

export function useDeleteLicense(licenseService: LicenseService) {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (id: string) => licenseService.delete(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: licenseKeys.lists() })
    },
  })
}

export function useValidateLicense(licenseService: LicenseService) {
  return useMutation({
    mutationFn: (key: string) => licenseService.validate(key),
  })
}

export function useSuspendLicense(licenseService: LicenseService) {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (id: string) => licenseService.suspend(id),
    onSuccess: (_, id) => {
      queryClient.invalidateQueries({ queryKey: licenseKeys.lists() })
      queryClient.invalidateQueries({ queryKey: licenseKeys.detail(id) })
    },
  })
}

export function useResumeLicense(licenseService: LicenseService) {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (id: string) => licenseService.resume(id),
    onSuccess: (_, id) => {
      queryClient.invalidateQueries({ queryKey: licenseKeys.lists() })
      queryClient.invalidateQueries({ queryKey: licenseKeys.detail(id) })
    },
  })
}