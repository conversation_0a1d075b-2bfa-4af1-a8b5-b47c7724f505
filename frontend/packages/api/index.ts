export { ApiClient, createApiClient } from './lib/client'
export { LicenseService } from './services/license'
export { MachineService } from './services/machine'
export { UserService } from './services/user'
export type * from './types'

// Factory function to create service instances
export function createServices(client: ApiClient) {
  return {
    licenses: new LicenseService(client),
    machines: new MachineService(client),
    users: new UserService(client),
  }
}