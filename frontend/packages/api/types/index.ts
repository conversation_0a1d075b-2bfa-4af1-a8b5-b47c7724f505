// API Response Types
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  message?: string
  error?: string
  errors?: Record<string, string[]>
}

export interface PaginatedResponse<T> {
  data: T[]
  pagination: {
    page: number
    per_page: number
    total: number
    total_pages: number
  }
}

// Entity Types (matching Go backend)
export interface User {
  id: string
  account_id: string
  email: string
  first_name: string
  last_name: string
  role: 'admin' | 'super_admin' | 'user' | 'developer'
  status: 'active' | 'inactive' | 'banned'
  created_at: string
  updated_at: string
}

export interface License {
  id: string
  account_id: string
  policy_id: string
  key: string
  name?: string
  status: 'active' | 'inactive' | 'expired' | 'suspended'
  uses: number
  max_uses?: number
  expiry?: string
  created_at: string
  updated_at: string
  policy?: Policy
  machines?: Machine[]
}

export interface Policy {
  id: string
  account_id: string
  product_id: string
  name: string
  duration?: number
  require_heartbeat: boolean
  heartbeat_duration?: number
  max_machines?: number
  max_processes?: number
  max_cores?: number
  max_uses?: number
  concurrent_uses?: number
  created_at: string
  updated_at: string
  product?: Product
}

export interface Product {
  id: string
  account_id: string
  name: string
  url?: string
  distribution_strategy: 'licensed' | 'open' | 'closed'
  platforms: string[]
  created_at: string
  updated_at: string
}

export interface Machine {
  id: string
  license_id: string
  fingerprint: string
  name?: string
  hostname?: string
  platform?: string
  ip?: string
  cores?: number
  last_heartbeat?: string
  created_at: string
  updated_at: string
  components?: MachineComponent[]
  processes?: MachineProcess[]
}

export interface MachineComponent {
  id: string
  machine_id: string
  name: string
  value: string
  created_at: string
  updated_at: string
}

export interface MachineProcess {
  id: string
  machine_id: string
  pid: number
  created_at: string
  updated_at: string
}

export interface Account {
  id: string
  name: string
  slug: string
  plan_id?: string
  created_at: string
  updated_at: string
  users?: User[]
  licenses?: License[]
}

// Request Types
export interface CreateLicenseRequest {
  policy_id: string
  name?: string
  max_uses?: number
  expiry?: string
}

export interface UpdateLicenseRequest {
  name?: string
  status?: License['status']
  max_uses?: number
  expiry?: string
}

export interface CreatePolicyRequest {
  product_id: string
  name: string
  duration?: number
  require_heartbeat?: boolean
  heartbeat_duration?: number
  max_machines?: number
  max_processes?: number
  max_cores?: number
  max_uses?: number
  concurrent_uses?: number
}

export interface CreateProductRequest {
  name: string
  url?: string
  distribution_strategy?: Product['distribution_strategy']
  platforms?: string[]
}

// Filter Types
export interface LicenseFilters {
  status?: License['status']
  policy_id?: string
  search?: string
  page?: number
  per_page?: number
}

export interface MachineFilters {
  license_id?: string
  search?: string
  page?: number
  per_page?: number
}

export interface UserFilters {
  role?: User['role']
  status?: User['status']
  search?: string
  page?: number
  per_page?: number
}