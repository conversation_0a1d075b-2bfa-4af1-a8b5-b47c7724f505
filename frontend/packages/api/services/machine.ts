import type { ApiClient } from '../lib/client'
import type {
  ApiResponse,
  PaginatedResponse,
  Machine,
  MachineFilters,
} from '../types'

export class MachineService {
  constructor(private client: ApiClient) {}

  async getAll(filters?: MachineFilters): Promise<PaginatedResponse<Machine>> {
    const params = new URLSearchParams()
    
    if (filters?.license_id) params.append('license_id', filters.license_id)
    if (filters?.search) params.append('search', filters.search)
    if (filters?.page) params.append('page', filters.page.toString())
    if (filters?.per_page) params.append('per_page', filters.per_page.toString())

    const query = params.toString()
    const url = `/api/v1/machines${query ? `?${query}` : ''}`
    
    return this.client.get<PaginatedResponse<Machine>>(url)
  }

  async getById(id: string): Promise<Machine> {
    const response = await this.client.get<ApiResponse<Machine>>(`/api/v1/machines/${id}`)
    if (!response.success || !response.data) {
      throw new Error(response.error || 'Failed to fetch machine')
    }
    return response.data
  }

  async delete(id: string): Promise<void> {
    const response = await this.client.delete<ApiResponse>(`/api/v1/machines/${id}`)
    if (!response.success) {
      throw new Error(response.error || 'Failed to delete machine')
    }
  }

  async getByLicense(licenseId: string): Promise<Machine[]> {
    const response = await this.getAll({ license_id: licenseId })
    return response.data
  }

  async heartbeat(id: string): Promise<{ success: boolean; message?: string }> {
    const response = await this.client.post<ApiResponse<{ success: boolean; message?: string }>>(`/api/v1/machines/${id}/heartbeat`)
    if (!response.success || !response.data) {
      throw new Error(response.error || 'Failed to update machine heartbeat')
    }
    return response.data
  }
}