import type { ApiClient } from '../lib/client'
import type {
  ApiResponse,
  PaginatedResponse,
  User,
  UserFilters,
} from '../types'

export interface CreateUserRequest {
  email: string
  first_name: string
  last_name: string
  role: User['role']
  password: string
}

export interface UpdateUserRequest {
  first_name?: string
  last_name?: string
  role?: User['role']
  status?: User['status']
}

export class UserService {
  constructor(private client: ApiClient) {}

  async getAll(filters?: UserFilters): Promise<PaginatedResponse<User>> {
    const params = new URLSearchParams()
    
    if (filters?.role) params.append('role', filters.role)
    if (filters?.status) params.append('status', filters.status)
    if (filters?.search) params.append('search', filters.search)
    if (filters?.page) params.append('page', filters.page.toString())
    if (filters?.per_page) params.append('per_page', filters.per_page.toString())

    const query = params.toString()
    const url = `/api/v1/users${query ? `?${query}` : ''}`
    
    return this.client.get<PaginatedResponse<User>>(url)
  }

  async getById(id: string): Promise<User> {
    const response = await this.client.get<ApiResponse<User>>(`/api/v1/users/${id}`)
    if (!response.success || !response.data) {
      throw new Error(response.error || 'Failed to fetch user')
    }
    return response.data
  }

  async create(data: CreateUserRequest): Promise<User> {
    const response = await this.client.post<ApiResponse<User>>('/api/v1/users', data)
    if (!response.success || !response.data) {
      throw new Error(response.error || 'Failed to create user')
    }
    return response.data
  }

  async update(id: string, data: UpdateUserRequest): Promise<User> {
    const response = await this.client.patch<ApiResponse<User>>(`/api/v1/users/${id}`, data)
    if (!response.success || !response.data) {
      throw new Error(response.error || 'Failed to update user')
    }
    return response.data
  }

  async delete(id: string): Promise<void> {
    const response = await this.client.delete<ApiResponse>(`/api/v1/users/${id}`)
    if (!response.success) {
      throw new Error(response.error || 'Failed to delete user')
    }
  }

  async ban(id: string): Promise<User> {
    return this.update(id, { status: 'banned' })
  }

  async unban(id: string): Promise<User> {
    return this.update(id, { status: 'active' })
  }

  async getCurrentUser(): Promise<User> {
    const response = await this.client.get<ApiResponse<User>>('/api/v1/users/me')
    if (!response.success || !response.data) {
      throw new Error(response.error || 'Failed to fetch current user')
    }
    return response.data
  }
}