import type { ApiClient } from '../lib/client'
import type {
  ApiResponse,
  PaginatedResponse,
  License,
  LicenseFilters,
  CreateLicenseRequest,
  UpdateLicenseRequest,
} from '../types'

export class LicenseService {
  constructor(private client: ApiClient) {}

  async getAll(filters?: LicenseFilters): Promise<PaginatedResponse<License>> {
    const params = new URLSearchParams()
    
    if (filters?.status) params.append('status', filters.status)
    if (filters?.policy_id) params.append('policy_id', filters.policy_id)
    if (filters?.search) params.append('search', filters.search)
    if (filters?.page) params.append('page', filters.page.toString())
    if (filters?.per_page) params.append('per_page', filters.per_page.toString())

    const query = params.toString()
    const url = `/api/v1/licenses${query ? `?${query}` : ''}`
    
    return this.client.get<PaginatedResponse<License>>(url)
  }

  async getById(id: string): Promise<License> {
    const response = await this.client.get<ApiResponse<License>>(`/api/v1/licenses/${id}`)
    if (!response.success || !response.data) {
      throw new Error(response.error || 'Failed to fetch license')
    }
    return response.data
  }

  async create(data: CreateLicenseRequest): Promise<License> {
    const response = await this.client.post<ApiResponse<License>>('/api/v1/licenses', data)
    if (!response.success || !response.data) {
      throw new Error(response.error || 'Failed to create license')
    }
    return response.data
  }

  async update(id: string, data: UpdateLicenseRequest): Promise<License> {
    const response = await this.client.patch<ApiResponse<License>>(`/api/v1/licenses/${id}`, data)
    if (!response.success || !response.data) {
      throw new Error(response.error || 'Failed to update license')
    }
    return response.data
  }

  async delete(id: string): Promise<void> {
    const response = await this.client.delete<ApiResponse>(`/api/v1/licenses/${id}`)
    if (!response.success) {
      throw new Error(response.error || 'Failed to delete license')
    }
  }

  async validate(key: string): Promise<{ valid: boolean; license?: License; message?: string }> {
    const response = await this.client.post<ApiResponse<{ valid: boolean; license?: License; message?: string }>>('/api/v1/licenses/validate', { key })
    if (!response.success || !response.data) {
      throw new Error(response.error || 'Failed to validate license')
    }
    return response.data
  }

  async suspend(id: string): Promise<License> {
    return this.update(id, { status: 'suspended' })
  }

  async resume(id: string): Promise<License> {
    return this.update(id, { status: 'active' })
  }
}