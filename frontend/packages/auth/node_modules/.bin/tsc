#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/workspace/gokeys/frontend/node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/bin/node_modules:/Users/<USER>/workspace/gokeys/frontend/node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/node_modules:/Users/<USER>/workspace/gokeys/frontend/node_modules/.pnpm/typescript@5.8.3/node_modules:/Users/<USER>/workspace/gokeys/frontend/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/workspace/gokeys/frontend/node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/bin/node_modules:/Users/<USER>/workspace/gokeys/frontend/node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/node_modules:/Users/<USER>/workspace/gokeys/frontend/node_modules/.pnpm/typescript@5.8.3/node_modules:/Users/<USER>/workspace/gokeys/frontend/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../typescript/bin/tsc" "$@"
else
  exec node  "$basedir/../typescript/bin/tsc" "$@"
fi
