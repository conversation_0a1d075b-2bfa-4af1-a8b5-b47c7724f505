"use client"

import { useSession } from "next-auth/react"

export function useAuth() {
  const { data: session, status } = useSession()

  return {
    user: session?.user,
    // accessToken: session?.accessToken, // Remove until we properly extend the Session type
    isLoading: status === "loading",
    isAuthenticated: !!session?.user,
    // role: session?.user?.role, // Remove until we properly extend the User type
    // accountId: session?.user?.accountId, // Remove until we properly extend the User type
  }
}