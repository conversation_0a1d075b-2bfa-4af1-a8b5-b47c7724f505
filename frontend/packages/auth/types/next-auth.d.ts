import { DefaultSession, DefaultUser } from "next-auth"
import { JWT } from "next-auth/jwt"

declare module "next-auth" {
  interface Session {
    user: {
      id: string
      role: string
      accountId: string
    } & DefaultSession["user"]
    accessToken: string
  }

  interface User extends DefaultUser {
    role: string
    accountId: string
    accessToken: string
  }
}

declare module "next-auth/jwt" {
  interface JWT {
    role: string
    accountId: string
    accessToken: string
  }
}