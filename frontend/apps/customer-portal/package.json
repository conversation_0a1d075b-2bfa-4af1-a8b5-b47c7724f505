{"name": "@gokeys/customer-portal", "version": "0.1.0", "type": "module", "private": true, "scripts": {"dev": "next dev --turbopack --port 3001", "build": "next build", "start": "next start", "lint": "next lint --max-warnings 0", "type-check": "tsc --noEmit"}, "dependencies": {"@repo/auth": "workspace:*", "@repo/api": "workspace:*", "@repo/ui": "workspace:*", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@stripe/stripe-js": "^7.5.0", "@tanstack/react-query": "^5.83.0", "axios": "^1.10.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.525.0", "next": "^15.3.0", "next-auth": "^4.24.11", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.60.0", "tailwind-merge": "^3.3.1", "zustand": "^5.0.6"}, "devDependencies": {"@hookform/resolvers": "^3.10.0", "@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@tailwindcss/postcss": "^4.1.11", "@types/node": "^22.15.3", "@types/react": "19.1.0", "@types/react-dom": "19.1.1", "autoprefixer": "^10.4.21", "eslint": "^9.31.0", "postcss": "^8.5.6", "typescript": "5.8.2", "zod": "^4.0.5"}}