import type { Metada<PERSON> } from 'next'
import { Inter } from 'next/font/google'
import { AuthProvider } from '@repo/auth'
import './globals.css'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'GoKeys Customer Portal',
  description: 'Manage your software licenses and downloads',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <body className={inter.className}>
        <AuthProvider>
          {children}
        </AuthProvider>
      </body>
    </html>
  )
}