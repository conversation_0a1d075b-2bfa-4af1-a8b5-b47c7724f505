import { SignupForm } from "@repo/auth"
import { Shield, ArrowLeft, Star } from "lucide-react"
import Link from "next/link"
import { Button } from "@repo/ui"

interface SignUpPageProps {
  searchParams: Promise<{
    callbackUrl?: string
  }>
}

export default async function SignUpPage({ searchParams }: SignUpPageProps) {
  const params = await searchParams
  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 via-emerald-50 to-teal-50 flex items-center justify-center p-4">
      <div className="w-full max-w-md space-y-6">
        {/* Back Button */}
        <div className="flex justify-start">
          <Button variant="outline" size="sm" asChild className="border-emerald-200 text-emerald-700 hover:bg-emerald-50">
            <Link href="/">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Home
            </Link>
          </Button>
        </div>

        {/* Header */}
        <div className="text-center">
          <div className="flex justify-center mb-4">
            <div className="flex items-center space-x-3">
              <div className="relative">
                <Shield className="h-10 w-10 text-emerald-600" />
                <div className="absolute -top-1 -right-1 w-3 h-3 bg-emerald-500 rounded-full animate-pulse"></div>
              </div>
              <div className="text-left">
                <h1 className="text-2xl font-bold text-gray-900">GoKeys</h1>
                <p className="text-xs text-emerald-600 font-medium">Customer Portal</p>
              </div>
            </div>
          </div>
          
          <div className="inline-flex items-center px-3 py-1 bg-emerald-100 text-emerald-800 rounded-full text-xs font-medium mb-4">
            <Star className="w-3 h-3 mr-1" />
            Join 10,000+ developers
          </div>
          
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Get Started Today</h2>
          <p className="text-gray-600">
            Create your account to manage licenses and access downloads
          </p>
        </div>

        {/* Signup Form */}
        <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-xl border border-emerald-100">
          <SignupForm callbackUrl={params.callbackUrl} />
        </div>

        {/* Sign In Link */}
        <div className="text-center">
          <p className="text-sm text-gray-600">
            Already have an account?{" "}
            <Link href="/auth/signin" className="text-emerald-600 hover:text-emerald-700 font-medium hover:underline">
              Sign in here
            </Link>
          </p>
        </div>

        {/* Features */}
        <div className="bg-white/60 backdrop-blur-sm rounded-xl p-6 border border-emerald-100">
          <h3 className="font-semibold text-gray-900 mb-3 text-center">What you'll get:</h3>
          <ul className="space-y-2 text-sm text-gray-600">
            <li className="flex items-center">
              <div className="w-2 h-2 bg-emerald-500 rounded-full mr-3"></div>
              Instant access to your license dashboard
            </li>
            <li className="flex items-center">
              <div className="w-2 h-2 bg-emerald-500 rounded-full mr-3"></div>
              Secure software downloads
            </li>
            <li className="flex items-center">
              <div className="w-2 h-2 bg-emerald-500 rounded-full mr-3"></div>
              Real-time usage tracking
            </li>
            <li className="flex items-center">
              <div className="w-2 h-2 bg-emerald-500 rounded-full mr-3"></div>
              24/7 customer support
            </li>
          </ul>
        </div>

        {/* Footer */}
        <div className="text-center text-xs text-gray-500">
          <p>
            Need help? <a href="#" className="text-emerald-600 hover:underline">Contact Support</a>
          </p>
        </div>
      </div>
    </div>
  )
}