"use client"

import { useState, useEffect } from "react"
import { useSession } from "next-auth/react"
import { MachineTable, MachineDetailsDialog } from "@repo/ui"

interface Machine {
  id: string
  name: string
  fingerprint: string
  platform: string
  hostname: string
  license_key: string
  last_heartbeat: string
  status: 'active' | 'inactive' | 'banned'
  ip: string
  cores: number
  created_at: string
  components?: any[]
  processes?: any[]
  metadata?: Record<string, any>
}

export function MachinesPageClient() {
  const { data: session } = useSession()
  const [machines, setMachines] = useState<Machine[]>([])
  const [selectedMachine, setSelectedMachine] = useState<Machine | null>(null)
  const [isDetailsOpen, setIsDetailsOpen] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Mock data for development
  const mockMachines: Machine[] = [
    {
      id: "1",
      name: "MacBook Pro",
      fingerprint: "a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6",
      platform: "macOS 14.0",
      hostname: "johns-macbook-pro.local",
      license_key: "gkeys_1234567890abcdef",
      last_heartbeat: new Date(Date.now() - 2 * 60 * 1000).toISOString(), // 2 minutes ago
      status: "active",
      ip: "*************",
      cores: 8,
      created_at: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days ago
      components: [
        { id: "1", name: "GoKeys SDK", version: "1.2.3", type: "library" },
        { id: "2", name: "License Validator", version: "2.1.0", type: "service" }
      ],
      processes: [
        { id: "1", name: "my-app", pid: 12345, cpu_usage: 2.5, memory_usage: 128 * 1024 * 1024 },
        { id: "2", name: "license-service", pid: 67890, cpu_usage: 0.1, memory_usage: 64 * 1024 * 1024 }
      ],
      metadata: {
        "OS Version": "macOS 14.0",
        "Architecture": "arm64",
        "RAM": "16GB"
      }
    },
    {
      id: "2", 
      name: "Ubuntu Server",
      fingerprint: "z9y8x7w6v5u4t3s2r1q0p9o8n7m6l5k4",
      platform: "Ubuntu 22.04",
      hostname: "prod-server-01",
      license_key: "gkeys_fedcba0987654321", 
      last_heartbeat: new Date(Date.now() - 45 * 60 * 1000).toISOString(), // 45 minutes ago
      status: "inactive",
      ip: "*********",
      cores: 4,
      created_at: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 days ago
      components: [
        { id: "3", name: "GoKeys CLI", version: "1.1.5", type: "tool" }
      ],
      processes: [],
      metadata: {
        "OS Version": "Ubuntu 22.04 LTS",
        "Architecture": "x86_64",
        "RAM": "8GB"
      }
    },
    {
      id: "3",
      name: "Development Workstation", 
      fingerprint: "m5n6o7p8q9r0s1t2u3v4w5x6y7z8a9b0",
      platform: "Windows 11",
      hostname: "DEV-WORKSTATION",
      license_key: "gkeys_1122334455667788",
      last_heartbeat: new Date(Date.now() - 3 * 60 * 60 * 1000).toISOString(), // 3 hours ago
      status: "active",
      ip: "*************", 
      cores: 12,
      created_at: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000).toISOString(), // 14 days ago
      components: [
        { id: "4", name: "GoKeys SDK", version: "1.2.3", type: "library" },
        { id: "5", name: "Visual Studio Extension", version: "3.0.1", type: "plugin" }
      ],
      processes: [
        { id: "3", name: "devenv.exe", pid: 54321, cpu_usage: 5.2, memory_usage: 512 * 1024 * 1024 }
      ],
      metadata: {
        "OS Version": "Windows 11 Pro",
        "Architecture": "x86_64", 
        "RAM": "32GB"
      }
    }
  ]

  useEffect(() => {
    // Simulate API call
    const fetchMachines = async () => {
      setIsLoading(true)
      try {
        // In production, this would be:
        // const response = await fetch('/api/gokeys/v1/machines', {
        //   headers: {
        //     'Authorization': `Bearer ${session?.accessToken}`
        //   }
        // })
        // const data = await response.json()
        
        // For now, use mock data
        await new Promise(resolve => setTimeout(resolve, 1000)) // Simulate loading
        setMachines(mockMachines)
        setError(null)
      } catch (err) {
        setError('Failed to fetch machines')
        console.error('Error fetching machines:', err)
      } finally {
        setIsLoading(false)
      }
    }

    if (session) {
      fetchMachines()
    }
  }, [session])

  const handleViewDetails = (machine: Machine) => {
    setSelectedMachine(machine)
    setIsDetailsOpen(true)
  }

  const handleDeactivate = async (machineId: string) => {
    try {
      // In production, this would be:
      // const response = await fetch(`/api/gokeys/v1/machines/${machineId}/deactivate`, {
      //   method: 'POST',
      //   headers: {
      //     'Authorization': `Bearer ${session?.accessToken}`
      //   }
      // })
      
      // For now, just update local state
      setMachines(prev => 
        prev.map(machine => 
          machine.id === machineId 
            ? { ...machine, status: 'inactive' as const }
            : machine
        )
      )
    } catch (err) {
      console.error('Error deactivating machine:', err)
    }
  }

  if (!session) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">Please sign in to view your machines.</p>
      </div>
    )
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-emerald-500"></div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="text-center py-8">
        <p className="text-red-500">{error}</p>
      </div>
    )
  }

  return (
    <>
      <MachineTable
        machines={machines}
        onViewDetails={handleViewDetails}
        onDeactivate={handleDeactivate}
      />
      
      <MachineDetailsDialog
        machine={selectedMachine}
        open={isDetailsOpen}
        onClose={() => setIsDetailsOpen(false)}
        onDeactivate={handleDeactivate}
      />
    </>
  )
}