"use client"

import { useState, useEffect } from "react"
import { useSession } from "next-auth/react"
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@repo/ui"
import { Badge } from "@repo/ui"
import { Key, Monitor, AlertCircle, CheckCircle, Clock, TrendingUp } from "lucide-react"

interface DashboardStats {
  totalLicenses: number
  activeLicenses: number
  totalMachines: number
  activeMachines: number
  expiringLicenses: number
  recentActivity: Array<{
    id: string
    type: 'license_activated' | 'machine_registered' | 'license_expired'
    message: string
    timestamp: string
  }>
}

export function DashboardOverview() {
  const { data: session } = useSession()
  const [stats, setStats] = useState<DashboardStats | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  // Mock data for development
  const mockStats: DashboardStats = {
    totalLicenses: 5,
    activeLicenses: 4,
    totalMachines: 8,
    activeMachines: 6,
    expiringLicenses: 1,
    recentActivity: [
      {
        id: "1",
        type: "machine_registered",
        message: "New machine 'MacBook Pro' registered",
        timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString()
      },
      {
        id: "2", 
        type: "license_activated",
        message: "License activated for 'Development Team'",
        timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString()
      },
      {
        id: "3",
        type: "license_expired",
        message: "Trial license expired",
        timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()
      }
    ]
  }

  useEffect(() => {
    const fetchStats = async () => {
      setIsLoading(true)
      try {
        // In production, this would be:
        // const response = await fetch('/api/gokeys/v1/dashboard/stats', {
        //   headers: {
        //     'Authorization': `Bearer ${session?.accessToken}`
        //   }
        // })
        // const data = await response.json()
        
        // For now, use mock data
        await new Promise(resolve => setTimeout(resolve, 800))
        setStats(mockStats)
      } catch (err) {
        console.error('Error fetching dashboard stats:', err)
      } finally {
        setIsLoading(false)
      }
    }

    if (session) {
      fetchStats()
    }
  }, [session])

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'license_activated': return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'machine_registered': return <Monitor className="h-4 w-4 text-blue-500" />
      case 'license_expired': return <AlertCircle className="h-4 w-4 text-red-500" />
      default: return <Clock className="h-4 w-4 text-gray-500" />
    }
  }

  const formatTimeAgo = (timestamp: string) => {
    const now = new Date()
    const time = new Date(timestamp)
    const diffInHours = (now.getTime() - time.getTime()) / (1000 * 60 * 60)
    
    if (diffInHours < 1) return 'Just now'
    if (diffInHours < 24) return `${Math.floor(diffInHours)}h ago`
    return `${Math.floor(diffInHours / 24)}d ago`
  }

  if (!session) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">Please sign in to view your dashboard.</p>
      </div>
    )
  }

  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {[...Array(4)].map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardContent className="p-6">
              <div className="h-4 bg-gray-200 rounded w-1/2 mb-2"></div>
              <div className="h-8 bg-gray-200 rounded w-1/3"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Licenses</p>
                <p className="text-3xl font-bold text-gray-900">{stats?.totalLicenses}</p>
              </div>
              <Key className="h-8 w-8 text-emerald-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Active Licenses</p>
                <p className="text-3xl font-bold text-gray-900">{stats?.activeLicenses}</p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Machines</p>
                <p className="text-3xl font-bold text-gray-900">{stats?.totalMachines}</p>
              </div>
              <Monitor className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Active Machines</p>
                <p className="text-3xl font-bold text-gray-900">{stats?.activeMachines}</p>
              </div>
              <TrendingUp className="h-8 w-8 text-purple-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions & Recent Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
            <CardDescription>Common tasks and shortcuts</CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="grid grid-cols-1 gap-3">
              <button className="flex items-center p-3 text-left border rounded-lg hover:bg-gray-50 transition-colors">
                <Key className="h-5 w-5 text-emerald-500 mr-3" />
                <div>
                  <div className="font-medium">Request New License</div>
                  <div className="text-sm text-gray-500">Get additional licenses for your team</div>
                </div>
              </button>
              
              <button className="flex items-center p-3 text-left border rounded-lg hover:bg-gray-50 transition-colors">
                <Monitor className="h-5 w-5 text-blue-500 mr-3" />
                <div>
                  <div className="font-medium">Register Machine</div>
                  <div className="text-sm text-gray-500">Add a new device to your account</div>
                </div>
              </button>
              
              <button className="flex items-center p-3 text-left border rounded-lg hover:bg-gray-50 transition-colors">
                <AlertCircle className="h-5 w-5 text-amber-500 mr-3" />
                <div>
                  <div className="font-medium">View Expiring Licenses</div>
                  <div className="text-sm text-gray-500">{stats?.expiringLicenses} license(s) expiring soon</div>
                </div>
              </button>
            </div>
          </CardContent>
        </Card>

        {/* Recent Activity */}
        <Card>
          <CardHeader>
            <CardTitle>Recent Activity</CardTitle>
            <CardDescription>Latest updates from your account</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {stats?.recentActivity.map((activity) => (
                <div key={activity.id} className="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg">
                  {getActivityIcon(activity.type)}
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900">{activity.message}</p>
                    <p className="text-xs text-gray-500">{formatTimeAgo(activity.timestamp)}</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Alerts */}
      {stats?.expiringLicenses && stats.expiringLicenses > 0 && (
        <Card className="border-amber-200 bg-amber-50">
          <CardContent className="p-6">
            <div className="flex items-center space-x-3">
              <AlertCircle className="h-6 w-6 text-amber-500" />
              <div>
                <p className="font-medium text-amber-800">
                  {stats.expiringLicenses} license(s) expiring soon
                </p>
                <p className="text-sm text-amber-700">
                  Review and renew your licenses to avoid service interruption.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}