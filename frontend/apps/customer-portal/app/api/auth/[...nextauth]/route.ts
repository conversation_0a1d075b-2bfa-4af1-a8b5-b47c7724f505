import NextAuth from "next-auth"
import Credentials from "next-auth/providers/credentials"

const handler = NextAuth({
  providers: [
    Credentials({
      name: "credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" }
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          return null
        }

        try {
          // Call GoKeys backend API for authentication directly
          const response = await fetch(`http://localhost:8080/api/v1/auth/login`, {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              email: credentials.email,
              password: credentials.password,
            }),
          })

          if (!response.ok) {
            console.error("Login failed:", response.status, response.statusText)
            return null
          }

          const data = await response.json()
          
          if (data.success && data.user) {
            return {
              id: data.user.id,
              email: data.user.email,
              name: `${data.user.first_name || ''} ${data.user.last_name || ''}`.trim(),
              role: data.user.role || 'user',
              accountId: data.user.organization_id,
              accessToken: data.token,
            }
          }

          return null
        } catch (error) {
          console.error("Authentication error:", error)
          return null
        }
      }
    })
  ],
  pages: {
    signIn: "/auth/signin",
    error: "/auth/error",
  },
  session: {
    strategy: "jwt",
  },
})

export { handler as GET, handler as POST }