import { <PERSON><PERSON>, <PERSON>, Card<PERSON>ontent, CardDescription, CardHeader, Card<PERSON><PERSON>le, <PERSON>ge } from "@repo/ui"
import { Download, Key, CreditCard, Shield, Zap, Star, ArrowRight, CheckCircle, Clock, TrendingUp } from "lucide-react"
import { Head<PERSON> } from "@/components/header"
import Link from "next/link"

export default function CustomerPortal() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 via-emerald-50 to-teal-50">
      <Header />

      {/* Hero Section */}
      <section className="relative overflow-hidden py-20">
        <div className="absolute inset-0 bg-gradient-to-r from-emerald-600/20 via-green-500/20 to-teal-600/20"></div>
        <div className="container mx-auto px-6 text-center relative">
          <div className="max-w-4xl mx-auto">
            <div className="inline-flex items-center px-4 py-2 bg-emerald-100 text-emerald-800 rounded-full text-sm font-medium mb-6">
              <Star className="w-4 h-4 mr-2" />
              Trusted by 10,000+ developers worldwide
            </div>
            <h1 className="text-5xl md:text-6xl font-bold text-gray-900 mb-6 leading-tight">
              Your Software
              <span className="text-emerald-600 block">License Hub</span>
            </h1>
            <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto leading-relaxed">
              Manage licenses, download software, and track subscriptions with ease. 
              Everything you need in one beautiful, intuitive dashboard.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" className="bg-emerald-600 hover:bg-emerald-700 text-white px-8 py-3" asChild>
                <Link href="/auth/signup">
                  <Zap className="w-5 h-5 mr-2" />
                  Get Started Free
                </Link>
              </Button>
              <Button size="lg" variant="outline" className="border-emerald-200 text-emerald-700 hover:bg-emerald-50 px-8 py-3" asChild>
                <Link href="#products">
                  Browse Products
                  <ArrowRight className="w-5 h-5 ml-2" />
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Quick Stats */}
      <section className="py-12 -mt-10 relative z-10">
        <div className="container mx-auto px-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto">
            <Card className="border-0 shadow-lg bg-white/80 backdrop-blur-sm hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
                <CardTitle className="text-sm font-semibold text-gray-700">Active Licenses</CardTitle>
                <div className="p-2 bg-emerald-100 rounded-lg">
                  <Key className="h-5 w-5 text-emerald-600" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold text-emerald-600 mb-1">12</div>
                <div className="flex items-center text-sm">
                  <TrendingUp className="w-4 h-4 text-green-500 mr-1" />
                  <span className="text-green-600 font-medium">+2 this month</span>
                </div>
              </CardContent>
            </Card>
            
            <Card className="border-0 shadow-lg bg-white/80 backdrop-blur-sm hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
                <CardTitle className="text-sm font-semibold text-gray-700">Downloads</CardTitle>
                <div className="p-2 bg-blue-100 rounded-lg">
                  <Download className="h-5 w-5 text-blue-600" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold text-blue-600 mb-1">847</div>
                <div className="flex items-center text-sm">
                  <TrendingUp className="w-4 h-4 text-green-500 mr-1" />
                  <span className="text-green-600 font-medium">+180 this month</span>
                </div>
              </CardContent>
            </Card>
            
            <Card className="border-0 shadow-lg bg-white/80 backdrop-blur-sm hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
                <CardTitle className="text-sm font-semibold text-gray-700">Subscription</CardTitle>
                <div className="p-2 bg-purple-100 rounded-lg">
                  <CreditCard className="h-5 w-5 text-purple-600" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold text-purple-600 mb-1">Pro</div>
                <div className="flex items-center text-sm">
                  <Clock className="w-4 h-4 text-gray-500 mr-1" />
                  <span className="text-gray-600">Renews Dec 15, 2024</span>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Main Content */}
      <section id="products" className="py-16">
        <div className="container mx-auto px-6">
          <div className="grid grid-cols-1 xl:grid-cols-2 gap-12 max-w-7xl mx-auto">
            
            {/* My Licenses */}
            <Card className="border-0 shadow-xl bg-white/90 backdrop-blur-sm">
              <CardHeader className="pb-6">
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="text-2xl font-bold text-gray-900">My Licenses</CardTitle>
                    <CardDescription className="text-gray-600 mt-1">
                      Your active software licenses and keys
                    </CardDescription>
                  </div>
                  <Badge className="bg-emerald-100 text-emerald-800 hover:bg-emerald-200">
                    12 Active
                  </Badge>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="p-6 border border-emerald-100 rounded-xl bg-gradient-to-r from-emerald-50 to-green-50 hover:shadow-md transition-all duration-200">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-2">
                        <h4 className="font-bold text-gray-900">GoKeys Pro</h4>
                        <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                          <CheckCircle className="w-3 h-3 mr-1" />
                          Active
                        </Badge>
                      </div>
                      <p className="text-sm text-gray-600 mb-3">License Key: GK-PRO-****-****-ABCD</p>
                      <div className="flex items-center space-x-4 text-xs text-gray-500">
                        <span>Expires: Dec 15, 2024</span>
                        <span>•</span>
                        <span>Usage: 847/∞</span>
                      </div>
                    </div>
                    <Button size="sm" variant="outline" className="shrink-0 border-emerald-200 text-emerald-700 hover:bg-emerald-50">
                      View Details
                    </Button>
                  </div>
                </div>
                
                <div className="p-6 border border-blue-100 rounded-xl bg-gradient-to-r from-blue-50 to-indigo-50 hover:shadow-md transition-all duration-200">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-2">
                        <h4 className="font-bold text-gray-900">Development Tools</h4>
                        <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                          <CheckCircle className="w-3 h-3 mr-1" />
                          Active
                        </Badge>
                      </div>
                      <p className="text-sm text-gray-600 mb-3">License Key: DT-DEV-****-****-EFGH</p>
                      <div className="flex items-center space-x-4 text-xs text-gray-500">
                        <span>Expires: Mar 20, 2025</span>
                        <span>•</span>
                        <span>Usage: 234/500</span>
                      </div>
                    </div>
                    <Button size="sm" variant="outline" className="shrink-0 border-blue-200 text-blue-700 hover:bg-blue-50">
                      View Details
                    </Button>
                  </div>
                </div>
                
                <Button className="w-full h-12 bg-emerald-600 hover:bg-emerald-700 text-white font-medium">
                  <Key className="w-4 h-4 mr-2" />
                  View All Licenses
                </Button>
              </CardContent>
            </Card>

            {/* Available Products */}
            <Card className="border-0 shadow-xl bg-white/90 backdrop-blur-sm">
              <CardHeader className="pb-6">
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="text-2xl font-bold text-gray-900">Available Products</CardTitle>
                    <CardDescription className="text-gray-600 mt-1">
                      Discover and purchase new software licenses
                    </CardDescription>
                  </div>
                  <Badge className="bg-blue-100 text-blue-800 hover:bg-blue-200">
                    New Arrivals
                  </Badge>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="p-6 border border-purple-100 rounded-xl bg-gradient-to-r from-purple-50 to-pink-50 hover:shadow-md transition-all duration-200">
                  <div className="flex justify-between items-start mb-3">
                    <h4 className="font-bold text-gray-900">GoKeys Enterprise</h4>
                    <Badge className="bg-gradient-to-r from-purple-500 to-pink-500 text-white">
                      New
                    </Badge>
                  </div>
                  <p className="text-sm text-gray-600 mb-4 leading-relaxed">
                    Advanced license management with team collaboration, analytics, and enterprise-grade security.
                  </p>
                  <div className="flex justify-between items-center">
                    <div>
                      <span className="text-2xl font-bold text-gray-900">$299</span>
                      <span className="text-gray-500">/year</span>
                    </div>
                    <Button size="sm" className="bg-purple-600 hover:bg-purple-700 text-white">
                      Purchase Now
                    </Button>
                  </div>
                </div>
                
                <div className="p-6 border border-orange-100 rounded-xl bg-gradient-to-r from-orange-50 to-yellow-50 hover:shadow-md transition-all duration-200">
                  <div className="flex justify-between items-start mb-3">
                    <h4 className="font-bold text-gray-900">Analytics Suite</h4>
                    <Badge variant="outline" className="border-orange-200 text-orange-700">
                      Popular
                    </Badge>
                  </div>
                  <p className="text-sm text-gray-600 mb-4 leading-relaxed">
                    Comprehensive analytics and reporting tools for license usage insights.
                  </p>
                  <div className="flex justify-between items-center">
                    <div>
                      <span className="text-2xl font-bold text-gray-900">$199</span>
                      <span className="text-gray-500">/year</span>
                    </div>
                    <Button size="sm" className="bg-orange-600 hover:bg-orange-700 text-white">
                      Purchase Now
                    </Button>
                  </div>
                </div>
                
                <Button variant="outline" className="w-full h-12 border-gray-200 text-gray-700 hover:bg-gray-50 font-medium">
                  Browse All Products
                  <ArrowRight className="w-4 h-4 ml-2" />
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Quick Actions */}
      <section className="py-16 bg-white/50">
        <div className="container mx-auto px-6">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Quick Actions</h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              Everything you need to manage your software licenses efficiently
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 max-w-5xl mx-auto">
            <Button className="h-24 flex-col space-y-3 bg-white hover:bg-emerald-50 text-gray-700 border border-emerald-100 hover:border-emerald-200 shadow-lg hover:shadow-xl transition-all duration-300">
              <Download className="h-8 w-8 text-emerald-600" />
              <span className="font-medium">Download Software</span>
            </Button>
            <Button className="h-24 flex-col space-y-3 bg-white hover:bg-blue-50 text-gray-700 border border-blue-100 hover:border-blue-200 shadow-lg hover:shadow-xl transition-all duration-300">
              <Key className="h-8 w-8 text-blue-600" />
              <span className="font-medium">Manage Keys</span>
            </Button>
            <Button className="h-24 flex-col space-y-3 bg-white hover:bg-purple-50 text-gray-700 border border-purple-100 hover:border-purple-200 shadow-lg hover:shadow-xl transition-all duration-300">
              <CreditCard className="h-8 w-8 text-purple-600" />
              <span className="font-medium">Billing & Payment</span>
            </Button>
            <Button className="h-24 flex-col space-y-3 bg-white hover:bg-gray-50 text-gray-700 border border-gray-100 hover:border-gray-200 shadow-lg hover:shadow-xl transition-all duration-300">
              <Shield className="h-8 w-8 text-gray-600" />
              <span className="font-medium">Account Settings</span>
            </Button>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-16">
        <div className="container mx-auto px-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8 mb-12">
            <div>
              <div className="flex items-center space-x-2 mb-4">
                <Shield className="h-6 w-6 text-emerald-400" />
                <span className="text-xl font-bold">GoKeys</span>
              </div>
              <p className="text-gray-400 text-sm leading-relaxed">
                Professional software license management for modern teams and enterprises.
              </p>
            </div>
            <div>
              <h3 className="font-semibold mb-4 text-white">Support</h3>
              <ul className="space-y-3 text-sm text-gray-400">
                <li><a href="#" className="hover:text-emerald-400 transition-colors">Help Center</a></li>
                <li><a href="#" className="hover:text-emerald-400 transition-colors">Contact Support</a></li>
                <li><a href="#" className="hover:text-emerald-400 transition-colors">Documentation</a></li>
                <li><a href="#" className="hover:text-emerald-400 transition-colors">API Reference</a></li>
              </ul>
            </div>
            <div>
              <h3 className="font-semibold mb-4 text-white">Products</h3>
              <ul className="space-y-3 text-sm text-gray-400">
                <li><a href="#" className="hover:text-emerald-400 transition-colors">GoKeys Pro</a></li>
                <li><a href="#" className="hover:text-emerald-400 transition-colors">Enterprise</a></li>
                <li><a href="#" className="hover:text-emerald-400 transition-colors">Developer Tools</a></li>
                <li><a href="#" className="hover:text-emerald-400 transition-colors">Analytics Suite</a></li>
              </ul>
            </div>
            <div>
              <h3 className="font-semibold mb-4 text-white">Company</h3>
              <ul className="space-y-3 text-sm text-gray-400">
                <li><a href="#" className="hover:text-emerald-400 transition-colors">About Us</a></li>
                <li><a href="#" className="hover:text-emerald-400 transition-colors">Privacy Policy</a></li>
                <li><a href="#" className="hover:text-emerald-400 transition-colors">Terms of Service</a></li>
                <li><a href="#" className="hover:text-emerald-400 transition-colors">Contact</a></li>
              </ul>
            </div>
          </div>
          <div className="border-t border-gray-800 pt-8 text-center">
            <p className="text-gray-400 text-sm">
              © 2024 GoKeys. All rights reserved. Built with ❤️ for developers.
            </p>
          </div>
        </div>
      </footer>
    </div>
  )
}