"use client"

import { useAuth } from "@repo/auth"
import { signOut } from "next-auth/react"
import { Button } from "@repo/ui"
import { Shield, LogOut, User, Settings } from "lucide-react"
import Link from "next/link"

export function Header() {
  const { user, isAuthenticated } = useAuth()

  const handleSignOut = () => {
    signOut({ callbackUrl: "/" })
  }

  return (
    <header className="bg-white/80 backdrop-blur-sm border-b shadow-sm sticky top-0 z-50">
      <div className="container mx-auto px-6 py-4">
        <div className="flex justify-between items-center">
          <Link href="/" className="flex items-center space-x-3">
            <div className="relative">
              <Shield className="h-8 w-8 text-emerald-600" />
              <div className="absolute -top-1 -right-1 w-3 h-3 bg-emerald-500 rounded-full animate-pulse"></div>
            </div>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">GoKeys</h1>
              <p className="text-xs text-emerald-600 font-medium">Customer Portal</p>
            </div>
          </Link>
          
          {isAuthenticated ? (
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2 text-sm text-gray-600">
                <User className="h-4 w-4" />
                <span>Hi, {user?.name?.split(' ')[0] || user?.email}</span>
              </div>
              
              <Button variant="outline" size="sm" className="border-emerald-200 text-emerald-700 hover:bg-emerald-50" asChild>
                <Link href="/dashboard">
                  <Shield className="h-4 w-4 mr-2" />
                  Dashboard
                </Link>
              </Button>
              
              <Button variant="outline" size="sm" className="border-emerald-200 text-emerald-700 hover:bg-emerald-50">
                <Settings className="h-4 w-4 mr-2" />
                Account
              </Button>
              
              <Button 
                variant="outline" 
                size="sm"
                onClick={handleSignOut}
                className="text-red-600 hover:text-red-700 hover:bg-red-50 border-red-200"
              >
                <LogOut className="h-4 w-4 mr-2" />
                Sign Out
              </Button>
            </div>
          ) : (
            <div className="flex items-center space-x-3">
              <Button variant="outline" size="sm" className="border-emerald-200 text-emerald-700 hover:bg-emerald-50">
                Support
              </Button>
              <Button variant="outline" size="sm" asChild>
                <Link href="/auth/signin">
                  Sign In
                </Link>
              </Button>
              <Button size="sm" className="bg-emerald-600 hover:bg-emerald-700" asChild>
                <Link href="/auth/signup">
                  Get Started
                </Link>
              </Button>
            </div>
          )}
        </div>
      </div>
    </header>
  )
}