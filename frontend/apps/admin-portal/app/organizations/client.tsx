"use client"

import { useState, useEffect } from "react"
import { useSession } from "next-auth/react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table"
import { Plus, Search, Edit, Trash2, Users, Key, Monitor, Building } from "lucide-react"

interface Organization {
  id: string
  name: string
  email: string
  slug: string
  plan_type: 'free' | 'pro' | 'enterprise'
  users_count: number
  licenses_count: number
  machines_count: number
  billing_status: 'active' | 'past_due' | 'cancelled'
  created_at: string
  updated_at: string
}

export function OrganizationsPageClient() {
  const { data: session } = useSession()
  const [organizations, setOrganizations] = useState<Organization[]>([])
  const [searchTerm, setSearchTerm] = useState("")
  const [planFilter, setPlanFilter] = useState<string>("all")
  const [isLoading, setIsLoading] = useState(true)

  // Mock data for development
  const mockOrganizations: Organization[] = [
    {
      id: "org-1",
      name: "GoKeys Inc",
      email: "<EMAIL>",
      slug: "gokeys-inc",
      plan_type: "enterprise",
      users_count: 15,
      licenses_count: 500,
      machines_count: 1250,
      billing_status: "active",
      created_at: new Date(Date.now() - 365 * 24 * 60 * 60 * 1000).toISOString(),
      updated_at: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString()
    },
    {
      id: "org-2",
      name: "TechCorp Solutions",
      email: "<EMAIL>", 
      slug: "techcorp",
      plan_type: "pro",
      users_count: 8,
      licenses_count: 150,
      machines_count: 340,
      billing_status: "active",
      created_at: new Date(Date.now() - 180 * 24 * 60 * 60 * 1000).toISOString(),
      updated_at: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString()
    },
    {
      id: "org-3",
      name: "StartupXYZ",
      email: "<EMAIL>",
      slug: "startupxyz", 
      plan_type: "free",
      users_count: 3,
      licenses_count: 5,
      machines_count: 12,
      billing_status: "active",
      created_at: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000).toISOString(),
      updated_at: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString()
    },
    {
      id: "org-4",
      name: "Enterprise Corp",
      email: "<EMAIL>",
      slug: "enterprise-corp",
      plan_type: "enterprise",
      users_count: 45,
      licenses_count: 2000,
      machines_count: 5500,
      billing_status: "past_due",
      created_at: new Date(Date.now() - 720 * 24 * 60 * 60 * 1000).toISOString(),
      updated_at: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString()
    },
    {
      id: "org-5",
      name: "DevShop",
      email: "<EMAIL>",
      slug: "devshop",
      plan_type: "pro",
      users_count: 12,
      licenses_count: 75,
      machines_count: 180,
      billing_status: "cancelled",
      created_at: new Date(Date.now() - 300 * 24 * 60 * 60 * 1000).toISOString(),
      updated_at: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString()
    }
  ]

  useEffect(() => {
    const fetchOrganizations = async () => {
      setIsLoading(true)
      try {
        // In production, this would be:
        // const response = await fetch('/api/gokeys/v1/admin/organizations', {
        //   headers: {
        //     'Authorization': `Bearer ${session?.accessToken}`
        //   }
        // })
        // const data = await response.json()
        
        // For now, use mock data
        await new Promise(resolve => setTimeout(resolve, 800))
        setOrganizations(mockOrganizations)
      } catch (err) {
        console.error('Error fetching organizations:', err)
      } finally {
        setIsLoading(false)
      }
    }

    if (session) {
      fetchOrganizations()
    }
  }, [session])

  const filteredOrganizations = organizations.filter(org => {
    const matchesSearch = org.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         org.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         org.slug.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesPlan = planFilter === "all" || org.plan_type === planFilter
    
    return matchesSearch && matchesPlan
  })

  const getPlanBadge = (planType: string) => {
    switch (planType) {
      case 'enterprise': return <Badge className="bg-purple-100 text-purple-800">Enterprise</Badge>
      case 'pro': return <Badge className="bg-blue-100 text-blue-800">Pro</Badge>
      case 'free': return <Badge className="bg-gray-100 text-gray-800">Free</Badge>
      default: return <Badge>{planType}</Badge>
    }
  }

  const getBillingStatusBadge = (status: string) => {
    switch (status) {
      case 'active': return <Badge className="bg-green-100 text-green-800">Active</Badge>
      case 'past_due': return <Badge className="bg-yellow-100 text-yellow-800">Past Due</Badge>
      case 'cancelled': return <Badge className="bg-red-100 text-red-800">Cancelled</Badge>
      default: return <Badge>{status}</Badge>
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  if (!session) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">Please sign in to access organization management.</p>
      </div>
    )
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-indigo-500"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Organizations</p>
                <p className="text-3xl font-bold text-gray-900">{organizations.length}</p>
              </div>
              <Building className="h-8 w-8 text-indigo-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Enterprise Plans</p>
                <p className="text-3xl font-bold text-gray-900">
                  {organizations.filter(org => org.plan_type === 'enterprise').length}
                </p>
              </div>
              <Badge className="h-8 w-8 bg-purple-100 text-purple-600 flex items-center justify-center">E</Badge>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Active Billing</p>
                <p className="text-3xl font-bold text-gray-900">
                  {organizations.filter(org => org.billing_status === 'active').length}
                </p>
              </div>
              <div className="h-8 w-8 bg-green-100 rounded-full flex items-center justify-center">
                <div className="h-3 w-3 bg-green-500 rounded-full"></div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Users</p>
                <p className="text-3xl font-bold text-gray-900">
                  {organizations.reduce((sum, org) => sum + org.users_count, 0)}
                </p>
              </div>
              <Users className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Organizations Table */}
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <div>
              <CardTitle>Organizations</CardTitle>
              <CardDescription>
                Manage customer organizations and their subscriptions
              </CardDescription>
            </div>
            <Button className="bg-indigo-600 hover:bg-indigo-700">
              <Plus className="w-4 h-4 mr-2" />
              Add Organization
            </Button>
          </div>
          
          <div className="flex gap-4 pt-4">
            <div className="relative flex-1 max-w-sm">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                placeholder="Search organizations..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            
            <select
              value={planFilter}
              onChange={(e) => setPlanFilter(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
            >
              <option value="all">All Plans</option>
              <option value="enterprise">Enterprise</option>
              <option value="pro">Pro</option>
              <option value="free">Free</option>
            </select>
          </div>
        </CardHeader>
        
        <CardContent>
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Organization</TableHead>
                  <TableHead>Plan</TableHead>
                  <TableHead>Users</TableHead>
                  <TableHead>Licenses</TableHead>
                  <TableHead>Machines</TableHead>
                  <TableHead>Billing</TableHead>
                  <TableHead>Created</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredOrganizations.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={8} className="text-center py-8 text-gray-500">
                      No organizations found
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredOrganizations.map((org) => (
                    <TableRow key={org.id}>
                      <TableCell>
                        <div>
                          <div className="font-medium">{org.name}</div>
                          <div className="text-sm text-gray-500">{org.email}</div>
                          <div className="text-xs text-gray-400">/{org.slug}</div>
                        </div>
                      </TableCell>
                      
                      <TableCell>
                        {getPlanBadge(org.plan_type)}
                      </TableCell>
                      
                      <TableCell>
                        <div className="flex items-center space-x-1">
                          <Users className="w-4 h-4 text-gray-400" />
                          <span>{org.users_count}</span>
                        </div>
                      </TableCell>
                      
                      <TableCell>
                        <div className="flex items-center space-x-1">
                          <Key className="w-4 h-4 text-gray-400" />
                          <span>{org.licenses_count}</span>
                        </div>
                      </TableCell>
                      
                      <TableCell>
                        <div className="flex items-center space-x-1">
                          <Monitor className="w-4 h-4 text-gray-400" />
                          <span>{org.machines_count}</span>
                        </div>
                      </TableCell>
                      
                      <TableCell>
                        {getBillingStatusBadge(org.billing_status)}
                      </TableCell>
                      
                      <TableCell>
                        <div className="text-sm">{formatDate(org.created_at)}</div>
                      </TableCell>
                      
                      <TableCell>
                        <div className="flex gap-2">
                          <Button variant="outline" size="sm">
                            <Edit className="w-3 h-3 mr-1" />
                            Edit
                          </Button>
                          <Button variant="outline" size="sm" className="text-red-600 hover:text-red-700">
                            <Trash2 className="w-3 h-3 mr-1" />
                            Delete
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
          
          {filteredOrganizations.length > 0 && (
            <div className="mt-4 text-sm text-gray-500">
              Showing {filteredOrganizations.length} of {organizations.length} organizations
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}