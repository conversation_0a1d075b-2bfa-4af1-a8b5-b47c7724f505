import { Suspense } from "react"
import { OrganizationsPageClient } from "./client"

export default function OrganizationsPage() {
  return (
    <div className="p-8">
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Organizations</h1>
          <p className="text-gray-600 mt-2">
            Manage customer organizations and their settings
          </p>
        </div>
      </div>
      
      <Suspense fallback={
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-indigo-500"></div>
        </div>
      }>
        <OrganizationsPageClient />
      </Suspense>
    </div>
  )
}