"use client"

import { useState, useEffect } from "react"
import { useSession } from "next-auth/react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table"
import { Plus, Search, MoreHorizontal, Edit, Trash2, Ban, CheckCircle } from "lucide-react"

interface User {
  id: string
  email: string
  first_name: string
  last_name: string
  role: 'user' | 'admin' | 'super_admin'
  organization_id: string
  organization_name: string
  email_verified: boolean
  banned: boolean
  last_login_at: string | null
  created_at: string
}

export function UsersPageClient() {
  const { data: session } = useSession()
  const [users, setUsers] = useState<User[]>([])
  const [searchTerm, setSearchTerm] = useState("")
  const [roleFilter, setRoleFilter] = useState<string>("all")
  const [isLoading, setIsLoading] = useState(true)

  // Mock data for development
  const mockUsers: User[] = [
    {
      id: "1",
      email: "<EMAIL>",
      first_name: "Admin",
      last_name: "User",
      role: "super_admin",
      organization_id: "org-1",
      organization_name: "GoKeys Inc",
      email_verified: true,
      banned: false,
      last_login_at: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
      created_at: new Date(Date.now() - 365 * 24 * 60 * 60 * 1000).toISOString()
    },
    {
      id: "2", 
      email: "<EMAIL>",
      first_name: "John",
      last_name: "Smith",
      role: "admin",
      organization_id: "org-2",
      organization_name: "TechCorp",
      email_verified: true,
      banned: false,
      last_login_at: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
      created_at: new Date(Date.now() - 180 * 24 * 60 * 60 * 1000).toISOString()
    },
    {
      id: "3",
      email: "<EMAIL>", 
      first_name: "Alice",
      last_name: "Johnson",
      role: "user",
      organization_id: "org-3",
      organization_name: "StartupXYZ",
      email_verified: true,
      banned: false,
      last_login_at: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
      created_at: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000).toISOString()
    },
    {
      id: "4",
      email: "<EMAIL>",
      first_name: "Bob",
      last_name: "Wilson",
      role: "user", 
      organization_id: "org-4",
      organization_name: "Enterprise Corp",
      email_verified: false,
      banned: false,
      last_login_at: null,
      created_at: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString()
    },
    {
      id: "5",
      email: "<EMAIL>",
      first_name: "Spam",
      last_name: "Bot",
      role: "user",
      organization_id: "org-5", 
      organization_name: "Bad Actors Inc",
      email_verified: false,
      banned: true,
      last_login_at: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
      created_at: new Date(Date.now() - 45 * 24 * 60 * 60 * 1000).toISOString()
    }
  ]

  useEffect(() => {
    const fetchUsers = async () => {
      setIsLoading(true)
      try {
        // In production, this would be:
        // const response = await fetch('/api/gokeys/v1/admin/users', {
        //   headers: {
        //     'Authorization': `Bearer ${session?.accessToken}`
        //   }
        // })
        // const data = await response.json()
        
        // For now, use mock data
        await new Promise(resolve => setTimeout(resolve, 800))
        setUsers(mockUsers)
      } catch (err) {
        console.error('Error fetching users:', err)
      } finally {
        setIsLoading(false)
      }
    }

    if (session) {
      fetchUsers()
    }
  }, [session])

  const filteredUsers = users.filter(user => {
    const matchesSearch = user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         user.first_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         user.last_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         user.organization_name.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesRole = roleFilter === "all" || user.role === roleFilter
    
    return matchesSearch && matchesRole
  })

  const getRoleBadge = (role: string) => {
    switch (role) {
      case 'super_admin': return <Badge className="bg-red-100 text-red-800">Super Admin</Badge>
      case 'admin': return <Badge className="bg-blue-100 text-blue-800">Admin</Badge>
      case 'user': return <Badge className="bg-gray-100 text-gray-800">User</Badge>
      default: return <Badge>{role}</Badge>
    }
  }

  const getStatusBadge = (user: User) => {
    if (user.banned) {
      return <Badge className="bg-red-100 text-red-800">Banned</Badge>
    }
    if (!user.email_verified) {
      return <Badge className="bg-yellow-100 text-yellow-800">Unverified</Badge>
    }
    return <Badge className="bg-green-100 text-green-800">Active</Badge>
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  const formatLastLogin = (lastLogin: string | null) => {
    if (!lastLogin) return 'Never'
    
    const now = new Date()
    const loginDate = new Date(lastLogin)
    const diffInHours = (now.getTime() - loginDate.getTime()) / (1000 * 60 * 60)
    
    if (diffInHours < 1) return 'Just now'
    if (diffInHours < 24) return `${Math.floor(diffInHours)}h ago`
    if (diffInHours < 168) return `${Math.floor(diffInHours / 24)}d ago`
    return formatDate(lastLogin)
  }

  if (!session) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">Please sign in to access user management.</p>
      </div>
    )
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-indigo-500"></div>
      </div>
    )
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex justify-between items-center">
          <div>
            <CardTitle>User Management</CardTitle>
            <CardDescription>
              Manage user accounts, roles, and permissions
            </CardDescription>
          </div>
          <Button className="bg-indigo-600 hover:bg-indigo-700">
            <Plus className="w-4 h-4 mr-2" />
            Add User
          </Button>
        </div>
        
        <div className="flex gap-4 pt-4">
          <div className="relative flex-1 max-w-sm">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              placeholder="Search users..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          
          <select
            value={roleFilter}
            onChange={(e) => setRoleFilter(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
          >
            <option value="all">All Roles</option>
            <option value="super_admin">Super Admin</option>
            <option value="admin">Admin</option>
            <option value="user">User</option>
          </select>
        </div>
      </CardHeader>
      
      <CardContent>
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>User</TableHead>
                <TableHead>Organization</TableHead>
                <TableHead>Role</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Last Login</TableHead>
                <TableHead>Created</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredUsers.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={7} className="text-center py-8 text-gray-500">
                    No users found
                  </TableCell>
                </TableRow>
              ) : (
                filteredUsers.map((user) => (
                  <TableRow key={user.id}>
                    <TableCell>
                      <div>
                        <div className="font-medium">{user.first_name} {user.last_name}</div>
                        <div className="text-sm text-gray-500">{user.email}</div>
                      </div>
                    </TableCell>
                    
                    <TableCell>
                      <div className="text-sm">{user.organization_name}</div>
                    </TableCell>
                    
                    <TableCell>
                      {getRoleBadge(user.role)}
                    </TableCell>
                    
                    <TableCell>
                      {getStatusBadge(user)}
                    </TableCell>
                    
                    <TableCell>
                      <div className="text-sm">{formatLastLogin(user.last_login_at)}</div>
                    </TableCell>
                    
                    <TableCell>
                      <div className="text-sm">{formatDate(user.created_at)}</div>
                    </TableCell>
                    
                    <TableCell>
                      <div className="flex gap-2">
                        <Button variant="outline" size="sm">
                          <Edit className="w-3 h-3 mr-1" />
                          Edit
                        </Button>
                        {!user.banned ? (
                          <Button variant="outline" size="sm" className="text-red-600 hover:text-red-700">
                            <Ban className="w-3 h-3 mr-1" />
                            Ban
                          </Button>
                        ) : (
                          <Button variant="outline" size="sm" className="text-green-600 hover:text-green-700">
                            <CheckCircle className="w-3 h-3 mr-1" />
                            Unban
                          </Button>
                        )}
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>
        
        {filteredUsers.length > 0 && (
          <div className="mt-4 text-sm text-gray-500">
            Showing {filteredUsers.length} of {users.length} users
          </div>
        )}
      </CardContent>
    </Card>
  )
}