import { LoginForm } from "@repo/auth"
import { Shield } from "lucide-react"

interface SignInPageProps {
  searchParams: Promise<{
    callbackUrl?: string
  }>
}

export default async function SignInPage({ searchParams }: SignInPageProps) {
  const params = await searchParams
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 flex items-center justify-center p-4">
      <div className="w-full max-w-md space-y-6">
        {/* Header */}
        <div className="text-center">
          <div className="flex justify-center mb-4">
            <div className="flex items-center space-x-2">
              <Shield className="h-8 w-8 text-blue-600" />
              <h1 className="text-2xl font-bold text-gray-900">GoKeys</h1>
            </div>
          </div>
          <h2 className="text-xl text-gray-600">Admin Portal</h2>
          <p className="text-sm text-gray-500 mt-2">
            Sign in to manage your license infrastructure
          </p>
        </div>

        {/* Login Form */}
        <LoginForm callbackUrl={params.callbackUrl} />

        {/* Footer */}
        <div className="text-center text-xs text-gray-500">
          <p>
            Need help? <a href="#" className="text-blue-600 hover:underline">Contact Support</a>
          </p>
        </div>
      </div>
    </div>
  )
}