import { <PERSON><PERSON><PERSON>ir<PERSON>, ArrowLeft } from "lucide-react"
import { <PERSON><PERSON>, <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@repo/ui"
import Link from "next/link"

interface AuthErrorPageProps {
  searchParams: Promise<{
    error?: string
  }>
}

const errorMessages = {
  configuration: "There is a problem with the server configuration.",
  accessdenied: "You do not have permission to sign in.",
  verification: "The verification token has expired or has already been used.",
  default: "An error occurred during authentication.",
}

export default async function AuthErrorPage({ searchParams }: AuthErrorPageProps) {
  const params = await searchParams
  const error = params.error as keyof typeof errorMessages
  const message = errorMessages[error] || errorMessages.default

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 flex items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-red-100">
            <AlertCircle className="h-6 w-6 text-red-600" />
          </div>
          <CardTitle className="text-xl font-semibold">Authentication Error</CardTitle>
          <CardDescription>{message}</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <Button asChild className="w-full">
            <Link href="/auth/signin">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Try Again
            </Link>
          </Button>
          
          <div className="text-center">
            <Link 
              href="/" 
              className="text-sm text-muted-foreground hover:text-primary"
            >
              Return to Home
            </Link>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}