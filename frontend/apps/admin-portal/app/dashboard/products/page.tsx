"use client"

import { useState, useEffect } from "react"
import { Plus, Search, Download, Filter, MoreHorizontal, Package, Copy, Edit } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Badge } from "@/components/ui/badge"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { useToast } from "@/hooks/use-toast"
import { api } from "@/lib/api"
import { formatDate } from "@/lib/utils"

interface Product {
  id: string
  organization_id: string
  name: string
  url?: string
  distribution_strategy?: string
  platforms?: string[]
  metadata?: Record<string, any>
  created_at: string
  updated_at: string
}

export default function ProductsPage() {
  const [products, setProducts] = useState<Product[]>([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState("")
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null)
  const { toast } = useToast()

  // Form state for creating/editing product
  const [formData, setFormData] = useState({
    name: "",
    url: "",
    distribution_strategy: "licensed",
    platforms: [] as string[],
    metadata: {},
  })

  useEffect(() => {
    fetchProducts()
  }, [])

  const fetchProducts = async () => {
    try {
      setLoading(true)
      const response = await api.get("/products")
      if (response.data && Array.isArray(response.data)) {
        setProducts(response.data)
      }
    } catch (error) {
      console.error("Failed to fetch products:", error)
      toast({
        title: "Error",
        description: "Failed to fetch products",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const handleCreateProduct = async () => {
    try {
      const response = await api.post("/products", formData)
      if (response.data) {
        setProducts([response.data, ...products])
        setIsCreateDialogOpen(false)
        resetForm()
        toast({
          title: "Success",
          description: "Product created successfully",
        })
      }
    } catch (error) {
      console.error("Failed to create product:", error)
      toast({
        title: "Error",
        description: "Failed to create product",
        variant: "destructive",
      })
    }
  }

  const handleUpdateProduct = async () => {
    if (!selectedProduct) return

    try {
      const response = await api.put(`/products/${selectedProduct.id}`, formData)
      if (response.data) {
        setProducts(products.map(p => p.id === selectedProduct.id ? response.data : p))
        setSelectedProduct(null)
        resetForm()
        toast({
          title: "Success",
          description: "Product updated successfully",
        })
      }
    } catch (error) {
      console.error("Failed to update product:", error)
      toast({
        title: "Error",
        description: "Failed to update product",
        variant: "destructive",
      })
    }
  }

  const handleDeleteProduct = async (id: string) => {
    if (!confirm("Are you sure you want to delete this product?")) return

    try {
      await api.delete(`/products/${id}`)
      setProducts(products.filter(p => p.id !== id))
      toast({
        title: "Success",
        description: "Product deleted successfully",
      })
    } catch (error) {
      console.error("Failed to delete product:", error)
      toast({
        title: "Error",
        description: "Failed to delete product",
        variant: "destructive",
      })
    }
  }

  const resetForm = () => {
    setFormData({
      name: "",
      url: "",
      distribution_strategy: "licensed",
      platforms: [],
      metadata: {},
    })
  }

  const openEditDialog = (product: Product) => {
    setSelectedProduct(product)
    setFormData({
      name: product.name,
      url: product.url || "",
      distribution_strategy: product.distribution_strategy || "licensed",
      platforms: product.platforms || [],
      metadata: product.metadata || {},
    })
    setIsCreateDialogOpen(true)
  }

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
    toast({
      description: "Copied to clipboard",
    })
  }

  const getDistributionBadge = (strategy?: string) => {
    const strategyColors = {
      licensed: "bg-blue-500",
      open: "bg-green-500",
      closed: "bg-gray-500",
      hybrid: "bg-purple-500",
    }

    return (
      <Badge 
        variant="secondary" 
        className={`${strategyColors[strategy as keyof typeof strategyColors] || "bg-gray-500"} text-white`}
      >
        {strategy || "licensed"}
      </Badge>
    )
  }

  const filteredProducts = products.filter(product =>
    product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    (product.url && product.url.toLowerCase().includes(searchQuery.toLowerCase()))
  )

  if (loading) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="text-lg">Loading products...</div>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">Products</h1>
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button onClick={() => { setSelectedProduct(null); resetForm(); }}>
              <Plus className="mr-2 h-4 w-4" />
              Create Product
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[500px]">
            <DialogHeader>
              <DialogTitle>
                {selectedProduct ? "Edit Product" : "Create New Product"}
              </DialogTitle>
              <DialogDescription>
                {selectedProduct 
                  ? "Update the product information below."
                  : "Create a new product to organize your licenses and policies."
                }
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="name" className="text-right">
                  Name
                </Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  className="col-span-3"
                  placeholder="Product name"
                  required
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="url" className="text-right">
                  URL
                </Label>
                <Input
                  id="url"
                  value={formData.url}
                  onChange={(e) => setFormData({ ...formData, url: e.target.value })}
                  className="col-span-3"
                  placeholder="https://example.com"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="distribution_strategy" className="text-right">
                  Distribution
                </Label>
                <Select
                  value={formData.distribution_strategy}
                  onValueChange={(value) => setFormData({ ...formData, distribution_strategy: value })}
                >
                  <SelectTrigger className="col-span-3">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="licensed">Licensed</SelectItem>
                    <SelectItem value="open">Open Source</SelectItem>
                    <SelectItem value="closed">Closed Source</SelectItem>
                    <SelectItem value="hybrid">Hybrid</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="platforms" className="text-right">
                  Platforms
                </Label>
                <Input
                  id="platforms"
                  value={formData.platforms.join(", ")}
                  onChange={(e) => setFormData({ 
                    ...formData, 
                    platforms: e.target.value.split(",").map(p => p.trim()).filter(Boolean)
                  })}
                  className="col-span-3"
                  placeholder="windows, macos, linux"
                />
              </div>
              <div className="grid grid-cols-4 items-start gap-4">
                <Label htmlFor="metadata" className="text-right pt-2">
                  Metadata
                </Label>
                <Textarea
                  id="metadata"
                  value={JSON.stringify(formData.metadata, null, 2)}
                  onChange={(e) => {
                    try {
                      const metadata = JSON.parse(e.target.value)
                      setFormData({ ...formData, metadata })
                    } catch (error) {
                      // Invalid JSON, don't update
                    }
                  }}
                  className="col-span-3 font-mono"
                  rows={4}
                  placeholder="{}"
                />
              </div>
            </div>
            <DialogFooter>
              <Button 
                type="submit" 
                onClick={selectedProduct ? handleUpdateProduct : handleCreateProduct}
              >
                {selectedProduct ? "Update Product" : "Create Product"}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      <div className="flex items-center space-x-2">
        <div className="relative flex-1">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search products..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-8"
          />
        </div>
        <Button variant="outline">
          <Filter className="mr-2 h-4 w-4" />
          Filter
        </Button>
        <Button variant="outline">
          <Download className="mr-2 h-4 w-4" />
          Export
        </Button>
      </div>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Name</TableHead>
              <TableHead>URL</TableHead>
              <TableHead>Distribution</TableHead>
              <TableHead>Platforms</TableHead>
              <TableHead>Created</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredProducts.map((product) => (
              <TableRow key={product.id}>
                <TableCell>
                  <div className="flex items-center space-x-2">
                    <Package className="h-4 w-4 text-muted-foreground" />
                    <span className="font-medium">{product.name}</span>
                  </div>
                </TableCell>
                <TableCell>
                  {product.url ? (
                    <a 
                      href={product.url} 
                      target="_blank" 
                      rel="noopener noreferrer"
                      className="text-blue-600 hover:underline"
                    >
                      {product.url}
                    </a>
                  ) : (
                    "-"
                  )}
                </TableCell>
                <TableCell>
                  {getDistributionBadge(product.distribution_strategy)}
                </TableCell>
                <TableCell>
                  {product.platforms && product.platforms.length > 0 ? (
                    <div className="flex gap-1">
                      {product.platforms.slice(0, 3).map((platform) => (
                        <Badge key={platform} variant="outline" className="text-xs">
                          {platform}
                        </Badge>
                      ))}
                      {product.platforms.length > 3 && (
                        <Badge variant="outline" className="text-xs">
                          +{product.platforms.length - 3}
                        </Badge>
                      )}
                    </div>
                  ) : (
                    "-"
                  )}
                </TableCell>
                <TableCell>{formatDate(product.created_at)}</TableCell>
                <TableCell className="text-right">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" className="h-8 w-8 p-0">
                        <span className="sr-only">Open menu</span>
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuLabel>Actions</DropdownMenuLabel>
                      <DropdownMenuItem
                        onClick={() => copyToClipboard(product.id)}
                      >
                        <Copy className="mr-2 h-4 w-4" />
                        Copy ID
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onClick={() => openEditDialog(product)}
                      >
                        <Edit className="mr-2 h-4 w-4" />
                        Edit
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem
                        onClick={() => handleDeleteProduct(product.id)}
                        className="text-red-600"
                      >
                        Delete
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  )
}