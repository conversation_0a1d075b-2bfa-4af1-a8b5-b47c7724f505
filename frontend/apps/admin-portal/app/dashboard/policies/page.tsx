"use client"

import { useState, useEffect } from "react"
import { Plus, Search, Download, Filter, MoreHorizontal, Shield, Copy, Edit, Eye } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Badge } from "@/components/ui/badge"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { useToast } from "@/hooks/use-toast"
import { api } from "@/lib/api"
import { formatDate } from "@/lib/utils"

interface Policy {
  id: string
  organization_id: string
  name: string
  duration?: number
  strict?: boolean
  floating?: boolean
  concurrent?: boolean
  protected?: boolean
  require_heartbeat?: boolean
  heartbeat_duration?: number
  heartbeat_cull_strategy?: string
  heartbeat_resurrection_strategy?: string
  machine_uniqueness_strategy?: string
  machine_matching_strategy?: string
  component_uniqueness_strategy?: string
  component_matching_strategy?: string
  expiration_strategy?: string
  expiration_basis?: string
  renewal_basis?: string
  transfer_strategy?: string
  authentication_strategy?: string
  mac_algorithm?: string
  cryptographic_algorithm?: string
  cryptographic_digest?: string
  require_product_scope?: boolean
  require_policy_scope?: boolean
  require_machine_scope?: boolean
  require_fingerprint_scope?: boolean
  require_components_scope?: boolean
  require_user_scope?: boolean
  require_checksum_scope?: boolean
  require_version_scope?: boolean
  require_check_in?: boolean
  check_in_interval?: string
  check_in_interval_count?: number
  use_pool?: boolean
  max_machines?: number
  max_processes?: number
  max_users?: number
  max_cores?: number
  max_uses?: number
  encrypted?: boolean
  metadata?: Record<string, any>
  created_at: string
  updated_at: string
}

export default function PoliciesPage() {
  const [policies, setPolicies] = useState<Policy[]>([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState("")
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [selectedPolicy, setSelectedPolicy] = useState<Policy | null>(null)
  const { toast } = useToast()

  // Form state for creating/editing policy
  const [formData, setFormData] = useState({
    name: "",
    duration: 0,
    strict: false,
    floating: false,
    concurrent: false,
    protected: false,
    require_heartbeat: false,
    heartbeat_duration: 0,
    heartbeat_cull_strategy: "DEACTIVATE_DEAD",
    heartbeat_resurrection_strategy: "NO_REVIVE",
    machine_uniqueness_strategy: "UNIQUE_PER_POLICY",
    machine_matching_strategy: "MATCH_BY_FINGERPRINT",
    component_uniqueness_strategy: "UNIQUE_PER_MACHINE",
    component_matching_strategy: "MATCH_BY_FINGERPRINT",
    expiration_strategy: "RESTRICT_ACCESS",
    expiration_basis: "FROM_CREATION",
    renewal_basis: "FROM_EXPIRY",
    transfer_strategy: "RESET_EXPIRY",
    authentication_strategy: "TOKEN",
    mac_algorithm: "HMAC_SHA256",
    cryptographic_algorithm: "aes-256-gcm",
    cryptographic_digest: "sha256",
    require_product_scope: false,
    require_policy_scope: false,
    require_machine_scope: false,
    require_fingerprint_scope: false,
    require_components_scope: false,
    require_user_scope: false,
    require_checksum_scope: false,
    require_version_scope: false,
    require_check_in: false,
    check_in_interval: "daily",
    check_in_interval_count: 1,
    use_pool: false,
    max_machines: 0,
    max_processes: 0,
    max_users: 0,
    max_cores: 0,
    max_uses: 0,
    encrypted: false,
    metadata: {},
  })

  useEffect(() => {
    fetchPolicies()
  }, [])

  const fetchPolicies = async () => {
    try {
      setLoading(true)
      const response = await api.get("/policies")
      if (response.data && Array.isArray(response.data)) {
        setPolicies(response.data)
      }
    } catch (error) {
      console.error("Failed to fetch policies:", error)
      toast({
        title: "Error",
        description: "Failed to fetch policies",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const handleCreatePolicy = async () => {
    try {
      const response = await api.post("/policies", formData)
      if (response.data) {
        setPolicies([response.data, ...policies])
        setIsCreateDialogOpen(false)
        resetForm()
        toast({
          title: "Success",
          description: "Policy created successfully",
        })
      }
    } catch (error) {
      console.error("Failed to create policy:", error)
      toast({
        title: "Error",
        description: "Failed to create policy",
        variant: "destructive",
      })
    }
  }

  const handleUpdatePolicy = async () => {
    if (!selectedPolicy) return

    try {
      const response = await api.put(`/policies/${selectedPolicy.id}`, formData)
      if (response.data) {
        setPolicies(policies.map(p => p.id === selectedPolicy.id ? response.data : p))
        setSelectedPolicy(null)
        resetForm()
        toast({
          title: "Success",
          description: "Policy updated successfully",
        })
      }
    } catch (error) {
      console.error("Failed to update policy:", error)
      toast({
        title: "Error",
        description: "Failed to update policy",
        variant: "destructive",
      })
    }
  }

  const handleDeletePolicy = async (id: string) => {
    if (!confirm("Are you sure you want to delete this policy?")) return

    try {
      await api.delete(`/policies/${id}`)
      setPolicies(policies.filter(p => p.id !== id))
      toast({
        title: "Success",
        description: "Policy deleted successfully",
      })
    } catch (error) {
      console.error("Failed to delete policy:", error)
      toast({
        title: "Error",
        description: "Failed to delete policy",
        variant: "destructive",
      })
    }
  }

  const resetForm = () => {
    setFormData({
      name: "",
      duration: 0,
      strict: false,
      floating: false,
      concurrent: false,
      protected: false,
      require_heartbeat: false,
      heartbeat_duration: 0,
      heartbeat_cull_strategy: "DEACTIVATE_DEAD",
      heartbeat_resurrection_strategy: "NO_REVIVE",
      machine_uniqueness_strategy: "UNIQUE_PER_POLICY",
      machine_matching_strategy: "MATCH_BY_FINGERPRINT",
      component_uniqueness_strategy: "UNIQUE_PER_MACHINE",
      component_matching_strategy: "MATCH_BY_FINGERPRINT",
      expiration_strategy: "RESTRICT_ACCESS",
      expiration_basis: "FROM_CREATION",
      renewal_basis: "FROM_EXPIRY",
      transfer_strategy: "RESET_EXPIRY",
      authentication_strategy: "TOKEN",
      mac_algorithm: "HMAC_SHA256",
      cryptographic_algorithm: "aes-256-gcm",
      cryptographic_digest: "sha256",
      require_product_scope: false,
      require_policy_scope: false,
      require_machine_scope: false,
      require_fingerprint_scope: false,
      require_components_scope: false,
      require_user_scope: false,
      require_checksum_scope: false,
      require_version_scope: false,
      require_check_in: false,
      check_in_interval: "daily",
      check_in_interval_count: 1,
      use_pool: false,
      max_machines: 0,
      max_processes: 0,
      max_users: 0,
      max_cores: 0,
      max_uses: 0,
      encrypted: false,
      metadata: {},
    })
  }

  const openEditDialog = (policy: Policy) => {
    setSelectedPolicy(policy)
    setFormData({
      name: policy.name,
      duration: policy.duration || 0,
      strict: policy.strict || false,
      floating: policy.floating || false,
      concurrent: policy.concurrent || false,
      protected: policy.protected || false,
      require_heartbeat: policy.require_heartbeat || false,
      heartbeat_duration: policy.heartbeat_duration || 0,
      heartbeat_cull_strategy: policy.heartbeat_cull_strategy || "DEACTIVATE_DEAD",
      heartbeat_resurrection_strategy: policy.heartbeat_resurrection_strategy || "NO_REVIVE",
      machine_uniqueness_strategy: policy.machine_uniqueness_strategy || "UNIQUE_PER_POLICY",
      machine_matching_strategy: policy.machine_matching_strategy || "MATCH_BY_FINGERPRINT",
      component_uniqueness_strategy: policy.component_uniqueness_strategy || "UNIQUE_PER_MACHINE",
      component_matching_strategy: policy.component_matching_strategy || "MATCH_BY_FINGERPRINT",
      expiration_strategy: policy.expiration_strategy || "RESTRICT_ACCESS",
      expiration_basis: policy.expiration_basis || "FROM_CREATION",
      renewal_basis: policy.renewal_basis || "FROM_EXPIRY",
      transfer_strategy: policy.transfer_strategy || "RESET_EXPIRY",
      authentication_strategy: policy.authentication_strategy || "TOKEN",
      mac_algorithm: policy.mac_algorithm || "HMAC_SHA256",
      cryptographic_algorithm: policy.cryptographic_algorithm || "aes-256-gcm",
      cryptographic_digest: policy.cryptographic_digest || "sha256",
      require_product_scope: policy.require_product_scope || false,
      require_policy_scope: policy.require_policy_scope || false,
      require_machine_scope: policy.require_machine_scope || false,
      require_fingerprint_scope: policy.require_fingerprint_scope || false,
      require_components_scope: policy.require_components_scope || false,
      require_user_scope: policy.require_user_scope || false,
      require_checksum_scope: policy.require_checksum_scope || false,
      require_version_scope: policy.require_version_scope || false,
      require_check_in: policy.require_check_in || false,
      check_in_interval: policy.check_in_interval || "daily",
      check_in_interval_count: policy.check_in_interval_count || 1,
      use_pool: policy.use_pool || false,
      max_machines: policy.max_machines || 0,
      max_processes: policy.max_processes || 0,
      max_users: policy.max_users || 0,
      max_cores: policy.max_cores || 0,
      max_uses: policy.max_uses || 0,
      encrypted: policy.encrypted || false,
      metadata: policy.metadata || {},
    })
    setIsCreateDialogOpen(true)
  }

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
    toast({
      description: "Copied to clipboard",
    })
  }

  const getStrategyBadge = (strategy?: string) => {
    const strategyColors = {
      "RESTRICT_ACCESS": "bg-red-500",
      "MAINTAIN_ACCESS": "bg-green-500",
      "ALLOW_ACCESS": "bg-blue-500",
      "UNIQUE_PER_POLICY": "bg-purple-500",
      "UNIQUE_PER_MACHINE": "bg-orange-500",
      "TOKEN": "bg-blue-500",
      "LICENSE": "bg-green-500",
      "MIXED": "bg-yellow-500",
    }

    return (
      <Badge 
        variant="secondary" 
        className={`${strategyColors[strategy as keyof typeof strategyColors] || "bg-gray-500"} text-white text-xs`}
      >
        {strategy || "DEFAULT"}
      </Badge>
    )
  }

  const filteredPolicies = policies.filter(policy =>
    policy.name.toLowerCase().includes(searchQuery.toLowerCase())
  )

  if (loading) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="text-lg">Loading policies...</div>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">Policies</h1>
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button onClick={() => { setSelectedPolicy(null); resetForm(); }}>
              <Plus className="mr-2 h-4 w-4" />
              Create Policy
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[800px] max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>
                {selectedPolicy ? "Edit Policy" : "Create New Policy"}
              </DialogTitle>
              <DialogDescription>
                {selectedPolicy 
                  ? "Update the policy configuration below."
                  : "Configure a new license policy with validation rules and limits."
                }
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              {/* Basic Information */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Basic Information</h3>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="name">Name</Label>
                    <Input
                      id="name"
                      value={formData.name}
                      onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                      placeholder="Policy name"
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="duration">Duration (seconds)</Label>
                    <Input
                      id="duration"
                      type="number"
                      value={formData.duration}
                      onChange={(e) => setFormData({ ...formData, duration: parseInt(e.target.value) || 0 })}
                      placeholder="0 for unlimited"
                    />
                  </div>
                </div>
              </div>

              {/* Policy Flags */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Policy Flags</h3>
                <div className="grid grid-cols-3 gap-4">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="strict"
                      checked={formData.strict}
                      onCheckedChange={(checked) => setFormData({ ...formData, strict: checked as boolean })}
                    />
                    <Label htmlFor="strict">Strict</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="floating"
                      checked={formData.floating}
                      onCheckedChange={(checked) => setFormData({ ...formData, floating: checked as boolean })}
                    />
                    <Label htmlFor="floating">Floating</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="concurrent"
                      checked={formData.concurrent}
                      onCheckedChange={(checked) => setFormData({ ...formData, concurrent: checked as boolean })}
                    />
                    <Label htmlFor="concurrent">Concurrent</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="protected"
                      checked={formData.protected}
                      onCheckedChange={(checked) => setFormData({ ...formData, protected: checked as boolean })}
                    />
                    <Label htmlFor="protected">Protected</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="encrypted"
                      checked={formData.encrypted}
                      onCheckedChange={(checked) => setFormData({ ...formData, encrypted: checked as boolean })}
                    />
                    <Label htmlFor="encrypted">Encrypted</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="use_pool"
                      checked={formData.use_pool}
                      onCheckedChange={(checked) => setFormData({ ...formData, use_pool: checked as boolean })}
                    />
                    <Label htmlFor="use_pool">Use Pool</Label>
                  </div>
                </div>
              </div>

              {/* Strategies */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Strategies</h3>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="expiration_strategy">Expiration Strategy</Label>
                    <Select
                      value={formData.expiration_strategy}
                      onValueChange={(value) => setFormData({ ...formData, expiration_strategy: value })}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="RESTRICT_ACCESS">Restrict Access</SelectItem>
                        <SelectItem value="MAINTAIN_ACCESS">Maintain Access</SelectItem>
                        <SelectItem value="ALLOW_ACCESS">Allow Access</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="authentication_strategy">Authentication Strategy</Label>
                    <Select
                      value={formData.authentication_strategy}
                      onValueChange={(value) => setFormData({ ...formData, authentication_strategy: value })}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="TOKEN">Token</SelectItem>
                        <SelectItem value="LICENSE">License</SelectItem>
                        <SelectItem value="MIXED">Mixed</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="machine_uniqueness_strategy">Machine Uniqueness</Label>
                    <Select
                      value={formData.machine_uniqueness_strategy}
                      onValueChange={(value) => setFormData({ ...formData, machine_uniqueness_strategy: value })}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="UNIQUE_PER_POLICY">Unique Per Policy</SelectItem>
                        <SelectItem value="UNIQUE_PER_ACCOUNT">Unique Per Account</SelectItem>
                        <SelectItem value="UNIQUE_PER_PRODUCT">Unique Per Product</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="cryptographic_algorithm">Crypto Algorithm</Label>
                    <Select
                      value={formData.cryptographic_algorithm}
                      onValueChange={(value) => setFormData({ ...formData, cryptographic_algorithm: value })}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="aes-256-gcm">AES-256-GCM</SelectItem>
                        <SelectItem value="aes-256-cbc">AES-256-CBC</SelectItem>
                        <SelectItem value="chacha20-poly1305">ChaCha20-Poly1305</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>

              {/* Limits */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Limits</h3>
                <div className="grid grid-cols-3 gap-4">
                  <div>
                    <Label htmlFor="max_machines">Max Machines</Label>
                    <Input
                      id="max_machines"
                      type="number"
                      value={formData.max_machines}
                      onChange={(e) => setFormData({ ...formData, max_machines: parseInt(e.target.value) || 0 })}
                      placeholder="0 for unlimited"
                    />
                  </div>
                  <div>
                    <Label htmlFor="max_users">Max Users</Label>
                    <Input
                      id="max_users"
                      type="number"
                      value={formData.max_users}
                      onChange={(e) => setFormData({ ...formData, max_users: parseInt(e.target.value) || 0 })}
                      placeholder="0 for unlimited"
                    />
                  </div>
                  <div>
                    <Label htmlFor="max_uses">Max Uses</Label>
                    <Input
                      id="max_uses"
                      type="number"
                      value={formData.max_uses}
                      onChange={(e) => setFormData({ ...formData, max_uses: parseInt(e.target.value) || 0 })}
                      placeholder="0 for unlimited"
                    />
                  </div>
                </div>
              </div>

              {/* Metadata */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Metadata</h3>
                <Textarea
                  value={JSON.stringify(formData.metadata, null, 2)}
                  onChange={(e) => {
                    try {
                      const metadata = JSON.parse(e.target.value)
                      setFormData({ ...formData, metadata })
                    } catch (error) {
                      // Invalid JSON, don't update
                    }
                  }}
                  className="font-mono"
                  rows={4}
                  placeholder="{}"
                />
              </div>
            </div>
            <DialogFooter>
              <Button 
                type="submit" 
                onClick={selectedPolicy ? handleUpdatePolicy : handleCreatePolicy}
              >
                {selectedPolicy ? "Update Policy" : "Create Policy"}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      <div className="flex items-center space-x-2">
        <div className="relative flex-1">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search policies..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-8"
          />
        </div>
        <Button variant="outline">
          <Filter className="mr-2 h-4 w-4" />
          Filter
        </Button>
        <Button variant="outline">
          <Download className="mr-2 h-4 w-4" />
          Export
        </Button>
      </div>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Name</TableHead>
              <TableHead>Duration</TableHead>
              <TableHead>Expiration</TableHead>
              <TableHead>Authentication</TableHead>
              <TableHead>Limits</TableHead>
              <TableHead>Flags</TableHead>
              <TableHead>Created</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredPolicies.map((policy) => (
              <TableRow key={policy.id}>
                <TableCell>
                  <div className="flex items-center space-x-2">
                    <Shield className="h-4 w-4 text-muted-foreground" />
                    <span className="font-medium">{policy.name}</span>
                  </div>
                </TableCell>
                <TableCell>
                  {policy.duration ? `${policy.duration}s` : "Unlimited"}
                </TableCell>
                <TableCell>
                  {getStrategyBadge(policy.expiration_strategy)}
                </TableCell>
                <TableCell>
                  {getStrategyBadge(policy.authentication_strategy)}
                </TableCell>
                <TableCell>
                  <div className="text-sm text-muted-foreground">
                    {policy.max_machines ? `${policy.max_machines} machines` : "∞"}
                    {policy.max_users ? `, ${policy.max_users} users` : ""}
                  </div>
                </TableCell>
                <TableCell>
                  <div className="flex gap-1 flex-wrap">
                    {policy.strict && <Badge variant="outline" className="text-xs">Strict</Badge>}
                    {policy.floating && <Badge variant="outline" className="text-xs">Floating</Badge>}
                    {policy.concurrent && <Badge variant="outline" className="text-xs">Concurrent</Badge>}
                    {policy.protected && <Badge variant="outline" className="text-xs">Protected</Badge>}
                  </div>
                </TableCell>
                <TableCell>{formatDate(policy.created_at)}</TableCell>
                <TableCell className="text-right">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" className="h-8 w-8 p-0">
                        <span className="sr-only">Open menu</span>
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuLabel>Actions</DropdownMenuLabel>
                      <DropdownMenuItem
                        onClick={() => copyToClipboard(policy.id)}
                      >
                        <Copy className="mr-2 h-4 w-4" />
                        Copy ID
                      </DropdownMenuItem>
                      <DropdownMenuItem>
                        <Eye className="mr-2 h-4 w-4" />
                        View Details
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onClick={() => openEditDialog(policy)}
                      >
                        <Edit className="mr-2 h-4 w-4" />
                        Edit
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem
                        onClick={() => handleDeletePolicy(policy.id)}
                        className="text-red-600"
                      >
                        Delete
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  )
}