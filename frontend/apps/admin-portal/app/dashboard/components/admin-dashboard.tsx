"use client"

import { useState, useEffect } from "react"
import { useSession } from "next-auth/react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { 
  Users, 
  Building, 
  Key, 
  Monitor, 
  TrendingUp, 
  AlertTriangle,
  CheckCircle,
  DollarSign,
  Activity
} from "lucide-react"

interface AdminStats {
  totalUsers: number
  totalOrganizations: number
  totalLicenses: number
  activeLicenses: number
  totalMachines: number
  activeMachines: number
  monthlyRevenue: number
  alerts: Array<{
    id: string
    type: 'warning' | 'error' | 'info'
    title: string
    description: string
    timestamp: string
  }>
  recentActivity: Array<{
    id: string
    type: string
    message: string
    timestamp: string
    user?: string
  }>
}

export function AdminDashboard() {
  const { data: session } = useSession()
  const [stats, setStats] = useState<AdminStats | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  // Mock data for development
  const mockStats: AdminStats = {
    totalUsers: 1247,
    totalOrganizations: 89,
    totalLicenses: 3892,
    activeLicenses: 3156,
    totalMachines: 8943,
    activeMachines: 7234,
    monthlyRevenue: 89750,
    alerts: [
      {
        id: "1",
        type: "warning",
        title: "High license usage",
        description: "Organization 'Tech Corp' is approaching license limit",
        timestamp: new Date(Date.now() - 30 * 60 * 1000).toISOString()
      },
      {
        id: "2",
        type: "error", 
        title: "Payment failed",
        description: "Failed payment for organization 'StartupXYZ'",
        timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString()
      },
      {
        id: "3",
        type: "info",
        title: "New organization",
        description: "Organization 'Global Inc' has registered",
        timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString()
      }
    ],
    recentActivity: [
      {
        id: "1",
        type: "license_created",
        message: "New license issued to TechCorp",
        timestamp: new Date(Date.now() - 10 * 60 * 1000).toISOString(),
        user: "<EMAIL>"
      },
      {
        id: "2",
        type: "user_created", 
        message: "New user registration: <EMAIL>",
        timestamp: new Date(Date.now() - 25 * 60 * 1000).toISOString(),
        user: "system"
      },
      {
        id: "3",
        type: "machine_registered",
        message: "New machine registered for license GK-PRO-****",
        timestamp: new Date(Date.now() - 45 * 60 * 1000).toISOString(),
        user: "<EMAIL>"
      },
      {
        id: "4",
        type: "payment_received",
        message: "Payment received from Global Inc ($299)",
        timestamp: new Date(Date.now() - 90 * 60 * 1000).toISOString(),
        user: "system"
      }
    ]
  }

  useEffect(() => {
    const fetchStats = async () => {
      setIsLoading(true)
      try {
        // In production, this would be:
        // const response = await fetch('/api/gokeys/v1/admin/dashboard/stats', {
        //   headers: {
        //     'Authorization': `Bearer ${session?.accessToken}`
        //   }
        // })
        // const data = await response.json()
        
        // For now, use mock data
        await new Promise(resolve => setTimeout(resolve, 1000))
        setStats(mockStats)
      } catch (err) {
        console.error('Error fetching admin stats:', err)
      } finally {
        setIsLoading(false)
      }
    }

    if (session) {
      fetchStats()
    }
  }, [session])

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount)
  }

  const formatTimeAgo = (timestamp: string) => {
    const now = new Date()
    const time = new Date(timestamp)
    const diffInMinutes = (now.getTime() - time.getTime()) / (1000 * 60)
    
    if (diffInMinutes < 1) return 'Just now'
    if (diffInMinutes < 60) return `${Math.floor(diffInMinutes)}m ago`
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`
    return `${Math.floor(diffInMinutes / 1440)}d ago`
  }

  const getAlertIcon = (type: string) => {
    switch (type) {
      case 'error': return <AlertTriangle className="h-4 w-4 text-red-500" />
      case 'warning': return <AlertTriangle className="h-4 w-4 text-yellow-500" />
      case 'info': return <CheckCircle className="h-4 w-4 text-blue-500" />
      default: return <Activity className="h-4 w-4 text-gray-500" />
    }
  }

  if (!session) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">Please sign in to access the admin dashboard.</p>
      </div>
    )
  }

  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {[...Array(8)].map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardContent className="p-6">
              <div className="h-4 bg-gray-200 rounded w-1/2 mb-2"></div>
              <div className="h-8 bg-gray-200 rounded w-1/3"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Users</p>
                <p className="text-3xl font-bold text-gray-900">{stats?.totalUsers.toLocaleString()}</p>
              </div>
              <Users className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Organizations</p>
                <p className="text-3xl font-bold text-gray-900">{stats?.totalOrganizations}</p>
              </div>
              <Building className="h-8 w-8 text-purple-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Licenses</p>
                <p className="text-3xl font-bold text-gray-900">{stats?.totalLicenses.toLocaleString()}</p>
                <p className="text-xs text-green-600">{stats?.activeLicenses} active</p>
              </div>
              <Key className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Active Machines</p>
                <p className="text-3xl font-bold text-gray-900">{stats?.activeMachines.toLocaleString()}</p>
                <p className="text-xs text-gray-500">of {stats?.totalMachines.toLocaleString()} total</p>
              </div>
              <Monitor className="h-8 w-8 text-indigo-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Monthly Revenue</p>
                <p className="text-3xl font-bold text-gray-900">{formatCurrency(stats?.monthlyRevenue || 0)}</p>
              </div>
              <DollarSign className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">License Usage</p>
                <p className="text-3xl font-bold text-gray-900">
                  {Math.round(((stats?.activeLicenses || 0) / (stats?.totalLicenses || 1)) * 100)}%
                </p>
              </div>
              <TrendingUp className="h-8 w-8 text-orange-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Alerts & Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Alerts */}
        <Card>
          <CardHeader>
            <CardTitle>System Alerts</CardTitle>
            <CardDescription>Issues requiring attention</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {stats?.alerts.map((alert) => (
                <div key={alert.id} className="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg">
                  {getAlertIcon(alert.type)}
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900">{alert.title}</p>
                    <p className="text-xs text-gray-600">{alert.description}</p>
                    <p className="text-xs text-gray-500 mt-1">{formatTimeAgo(alert.timestamp)}</p>
                  </div>
                  <Badge variant="outline" className={
                    alert.type === 'error' ? 'border-red-200 text-red-700' :
                    alert.type === 'warning' ? 'border-yellow-200 text-yellow-700' :
                    'border-blue-200 text-blue-700'
                  }>
                    {alert.type}
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Recent Activity */}
        <Card>
          <CardHeader>
            <CardTitle>Recent Activity</CardTitle>
            <CardDescription>Latest platform events</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {stats?.recentActivity.map((activity) => (
                <div key={activity.id} className="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg">
                  <Activity className="h-4 w-4 text-gray-500 mt-0.5" />
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900">{activity.message}</p>
                    <div className="flex items-center space-x-2 mt-1">
                      <p className="text-xs text-gray-500">{formatTimeAgo(activity.timestamp)}</p>
                      {activity.user && activity.user !== 'system' && (
                        <>
                          <span className="text-xs text-gray-400">•</span>
                          <p className="text-xs text-gray-500">{activity.user}</p>
                        </>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}