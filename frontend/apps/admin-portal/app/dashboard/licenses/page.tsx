"use client"

import { useState, useEffect } from "react"
import { Plus, Search, Download, Filter, MoreHorizontal, Key, Copy, RefreshCw } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Badge } from "@/components/ui/badge"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { useToast } from "@/hooks/use-toast"
import { api } from "@/lib/api"
import { formatDate } from "@/lib/utils"

interface License {
  id: string
  key: string
  name?: string
  status: string
  product_id: string
  policy_id?: string
  user_id?: string
  uses: number
  expires_at?: string
  last_validated?: string
  validation_count: number
  created_at: string
  updated_at: string
}

export default function LicensesPage() {
  const [licenses, setLicenses] = useState<License[]>([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState("")
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [selectedLicense, setSelectedLicense] = useState<License | null>(null)
  const { toast } = useToast()

  // Form state for creating license
  const [formData, setFormData] = useState({
    name: "",
    product_id: "",
    policy_id: "",
    user_id: "",
    expires_at: "",
  })

  useEffect(() => {
    fetchLicenses()
  }, [])

  const fetchLicenses = async () => {
    try {
      setLoading(true)
      const response = await api.get("/licenses")
      if (response.data && Array.isArray(response.data)) {
        setLicenses(response.data)
      }
    } catch (error) {
      console.error("Failed to fetch licenses:", error)
      toast({
        title: "Error",
        description: "Failed to fetch licenses",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const handleCreateLicense = async () => {
    try {
      const response = await api.post("/licenses", formData)
      if (response.data) {
        setLicenses([response.data, ...licenses])
        setIsCreateDialogOpen(false)
        setFormData({
          name: "",
          product_id: "",
          policy_id: "",
          user_id: "",
          expires_at: "",
        })
        toast({
          title: "Success",
          description: "License created successfully",
        })
      }
    } catch (error) {
      console.error("Failed to create license:", error)
      toast({
        title: "Error",
        description: "Failed to create license",
        variant: "destructive",
      })
    }
  }

  const handleSuspendLicense = async (id: string) => {
    try {
      await api.put(`/licenses/${id}`, { status: "suspended" })
      await fetchLicenses()
      toast({
        title: "Success",
        description: "License suspended successfully",
      })
    } catch (error) {
      console.error("Failed to suspend license:", error)
      toast({
        title: "Error",
        description: "Failed to suspend license",
        variant: "destructive",
      })
    }
  }

  const handleRevokeLicense = async (id: string) => {
    try {
      await api.put(`/licenses/${id}`, { status: "revoked" })
      await fetchLicenses()
      toast({
        title: "Success",
        description: "License revoked successfully",
      })
    } catch (error) {
      console.error("Failed to revoke license:", error)
      toast({
        title: "Error",
        description: "Failed to revoke license",
        variant: "destructive",
      })
    }
  }

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
    toast({
      description: "Copied to clipboard",
    })
  }

  const getStatusBadge = (status: string) => {
    const statusColors = {
      active: "bg-green-500",
      suspended: "bg-yellow-500",
      revoked: "bg-red-500",
      expired: "bg-gray-500",
    }

    return (
      <Badge 
        variant="secondary" 
        className={`${statusColors[status as keyof typeof statusColors] || "bg-gray-500"} text-white`}
      >
        {status}
      </Badge>
    )
  }

  const filteredLicenses = licenses.filter(license =>
    license.key.toLowerCase().includes(searchQuery.toLowerCase()) ||
    (license.name && license.name.toLowerCase().includes(searchQuery.toLowerCase()))
  )

  if (loading) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="text-lg">Loading licenses...</div>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">Licenses</h1>
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              Create License
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle>Create New License</DialogTitle>
              <DialogDescription>
                Create a new license for a product with optional policy and user assignment.
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="name" className="text-right">
                  Name
                </Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  className="col-span-3"
                  placeholder="License name (optional)"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="product_id" className="text-right">
                  Product
                </Label>
                <Input
                  id="product_id"
                  value={formData.product_id}
                  onChange={(e) => setFormData({ ...formData, product_id: e.target.value })}
                  className="col-span-3"
                  placeholder="Product ID"
                  required
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="policy_id" className="text-right">
                  Policy
                </Label>
                <Input
                  id="policy_id"
                  value={formData.policy_id}
                  onChange={(e) => setFormData({ ...formData, policy_id: e.target.value })}
                  className="col-span-3"
                  placeholder="Policy ID (optional)"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="user_id" className="text-right">
                  User
                </Label>
                <Input
                  id="user_id"
                  value={formData.user_id}
                  onChange={(e) => setFormData({ ...formData, user_id: e.target.value })}
                  className="col-span-3"
                  placeholder="User ID (optional)"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="expires_at" className="text-right">
                  Expires
                </Label>
                <Input
                  id="expires_at"
                  type="datetime-local"
                  value={formData.expires_at}
                  onChange={(e) => setFormData({ ...formData, expires_at: e.target.value })}
                  className="col-span-3"
                />
              </div>
            </div>
            <DialogFooter>
              <Button type="submit" onClick={handleCreateLicense}>Create License</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      <div className="flex items-center space-x-2">
        <div className="relative flex-1">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search licenses..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-8"
          />
        </div>
        <Button variant="outline">
          <Filter className="mr-2 h-4 w-4" />
          Filter
        </Button>
        <Button variant="outline">
          <Download className="mr-2 h-4 w-4" />
          Export
        </Button>
      </div>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>License Key</TableHead>
              <TableHead>Name</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Uses</TableHead>
              <TableHead>Validations</TableHead>
              <TableHead>Expires</TableHead>
              <TableHead>Created</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredLicenses.map((license) => (
              <TableRow key={license.id}>
                <TableCell>
                  <div className="flex items-center space-x-2">
                    <Key className="h-4 w-4 text-muted-foreground" />
                    <code className="text-sm">{license.key.substring(0, 20)}...</code>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => copyToClipboard(license.key)}
                    >
                      <Copy className="h-3 w-3" />
                    </Button>
                  </div>
                </TableCell>
                <TableCell>{license.name || "-"}</TableCell>
                <TableCell>{getStatusBadge(license.status)}</TableCell>
                <TableCell>{license.uses}</TableCell>
                <TableCell>{license.validation_count}</TableCell>
                <TableCell>
                  {license.expires_at ? formatDate(license.expires_at) : "Never"}
                </TableCell>
                <TableCell>{formatDate(license.created_at)}</TableCell>
                <TableCell className="text-right">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" className="h-8 w-8 p-0">
                        <span className="sr-only">Open menu</span>
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuLabel>Actions</DropdownMenuLabel>
                      <DropdownMenuItem
                        onClick={() => copyToClipboard(license.key)}
                      >
                        <Copy className="mr-2 h-4 w-4" />
                        Copy Key
                      </DropdownMenuItem>
                      <DropdownMenuItem>
                        <RefreshCw className="mr-2 h-4 w-4" />
                        Refresh
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem
                        onClick={() => handleSuspendLicense(license.id)}
                        disabled={license.status !== "active"}
                      >
                        Suspend
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onClick={() => handleRevokeLicense(license.id)}
                        className="text-red-600"
                        disabled={license.status === "revoked"}
                      >
                        Revoke
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  )
}