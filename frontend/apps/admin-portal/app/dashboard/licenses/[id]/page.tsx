"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
import { ArrowLeft, Save, Trash2, Refresh<PERSON>w, Shield, Activity, Clock } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { useToast } from "@/hooks/use-toast"
import { api } from "@/lib/api"
import { formatDate, formatDateTime } from "@/lib/utils"

interface License {
  id: string
  key: string
  organization_id: string
  product_id: string
  policy_id?: string
  user_id?: string
  name?: string
  status: string
  uses: number
  protected?: boolean
  suspended?: boolean
  expires_at?: string
  last_validated?: string
  validation_count: number
  metadata?: Record<string, any>
  created_at: string
  updated_at: string
}

interface ValidationHistory {
  id: string
  license_id: string
  machine_fingerprint?: string
  result: boolean
  errors?: string[]
  created_at: string
}

export default function LicenseDetailPage() {
  const params = useParams()
  const router = useRouter()
  const { toast } = useToast()
  const [license, setLicense] = useState<License | null>(null)
  const [validationHistory, setValidationHistory] = useState<ValidationHistory[]>([])
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [formData, setFormData] = useState<Partial<License>>({})

  useEffect(() => {
    if (params.id) {
      fetchLicense()
      fetchValidationHistory()
    }
  }, [params.id])

  const fetchLicense = async () => {
    try {
      setLoading(true)
      const response = await api.get(`/licenses/${params.id}`)
      if (response.data) {
        setLicense(response.data)
        setFormData(response.data)
      }
    } catch (error) {
      console.error("Failed to fetch license:", error)
      toast({
        title: "Error",
        description: "Failed to fetch license details",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const fetchValidationHistory = async () => {
    try {
      const response = await api.get(`/licenses/${params.id}/validations`)
      if (response.data && Array.isArray(response.data)) {
        setValidationHistory(response.data)
      }
    } catch (error) {
      console.error("Failed to fetch validation history:", error)
    }
  }

  const handleSave = async () => {
    try {
      setSaving(true)
      const response = await api.put(`/licenses/${params.id}`, formData)
      if (response.data) {
        setLicense(response.data)
        toast({
          title: "Success",
          description: "License updated successfully",
        })
      }
    } catch (error) {
      console.error("Failed to update license:", error)
      toast({
        title: "Error",
        description: "Failed to update license",
        variant: "destructive",
      })
    } finally {
      setSaving(false)
    }
  }

  const handleDelete = async () => {
    if (!confirm("Are you sure you want to delete this license?")) return

    try {
      await api.delete(`/licenses/${params.id}`)
      toast({
        title: "Success",
        description: "License deleted successfully",
      })
      router.push("/licenses")
    } catch (error) {
      console.error("Failed to delete license:", error)
      toast({
        title: "Error",
        description: "Failed to delete license",
        variant: "destructive",
      })
    }
  }

  const handleRefresh = async () => {
    try {
      const response = await api.post(`/licenses/${params.id}/actions/refresh`)
      if (response.data) {
        setLicense(response.data.license)
        toast({
          title: "Success",
          description: "License key refreshed successfully",
        })
      }
    } catch (error) {
      console.error("Failed to refresh license:", error)
      toast({
        title: "Error",
        description: "Failed to refresh license key",
        variant: "destructive",
      })
    }
  }

  const getStatusBadge = (status: string) => {
    const statusColors = {
      active: "bg-green-500",
      suspended: "bg-yellow-500",
      revoked: "bg-red-500",
      expired: "bg-gray-500",
    }

    return (
      <Badge 
        variant="secondary" 
        className={`${statusColors[status as keyof typeof statusColors] || "bg-gray-500"} text-white`}
      >
        {status}
      </Badge>
    )
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="text-lg">Loading license details...</div>
      </div>
    )
  }

  if (!license) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="text-lg">License not found</div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="ghost"
            onClick={() => router.push("/licenses")}
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back
          </Button>
          <h1 className="text-3xl font-bold">License Details</h1>
          {getStatusBadge(license.status)}
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={handleRefresh}>
            <RefreshCw className="mr-2 h-4 w-4" />
            Refresh Key
          </Button>
          <Button onClick={handleSave} disabled={saving}>
            <Save className="mr-2 h-4 w-4" />
            {saving ? "Saving..." : "Save Changes"}
          </Button>
          <Button variant="destructive" onClick={handleDelete}>
            <Trash2 className="mr-2 h-4 w-4" />
            Delete
          </Button>
        </div>
      </div>

      <Tabs defaultValue="details" className="space-y-4">
        <TabsList>
          <TabsTrigger value="details">Details</TabsTrigger>
          <TabsTrigger value="validations">Validation History</TabsTrigger>
          <TabsTrigger value="machines">Machines</TabsTrigger>
          <TabsTrigger value="metadata">Metadata</TabsTrigger>
        </TabsList>

        <TabsContent value="details" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>License Information</CardTitle>
              <CardDescription>
                Basic information about the license
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>License Key</Label>
                  <Input value={license.key} disabled />
                </div>
                <div className="space-y-2">
                  <Label>Name</Label>
                  <Input
                    value={formData.name || ""}
                    onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                    placeholder="License name"
                  />
                </div>
                <div className="space-y-2">
                  <Label>Status</Label>
                  <Select
                    value={formData.status}
                    onValueChange={(value) => setFormData({ ...formData, status: value })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="active">Active</SelectItem>
                      <SelectItem value="suspended">Suspended</SelectItem>
                      <SelectItem value="revoked">Revoked</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label>Expires At</Label>
                  <Input
                    type="datetime-local"
                    value={formData.expires_at ? new Date(formData.expires_at).toISOString().slice(0, 16) : ""}
                    onChange={(e) => setFormData({ ...formData, expires_at: e.target.value })}
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          <div className="grid gap-4 md:grid-cols-3">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Total Uses
                </CardTitle>
                <Activity className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{license.uses}</div>
                <p className="text-xs text-muted-foreground">
                  Times this license has been used
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Validations
                </CardTitle>
                <Shield className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{license.validation_count}</div>
                <p className="text-xs text-muted-foreground">
                  Total validation requests
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Last Validated
                </CardTitle>
                <Clock className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {license.last_validated ? formatDate(license.last_validated) : "Never"}
                </div>
                <p className="text-xs text-muted-foreground">
                  Last validation attempt
                </p>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Associations</CardTitle>
              <CardDescription>
                Product, policy, and user associations
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label>Product ID</Label>
                  <Input
                    value={formData.product_id || ""}
                    onChange={(e) => setFormData({ ...formData, product_id: e.target.value })}
                    placeholder="Product ID"
                  />
                </div>
                <div className="space-y-2">
                  <Label>Policy ID</Label>
                  <Input
                    value={formData.policy_id || ""}
                    onChange={(e) => setFormData({ ...formData, policy_id: e.target.value })}
                    placeholder="Policy ID (optional)"
                  />
                </div>
                <div className="space-y-2">
                  <Label>User ID</Label>
                  <Input
                    value={formData.user_id || ""}
                    onChange={(e) => setFormData({ ...formData, user_id: e.target.value })}
                    placeholder="User ID (optional)"
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="validations" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Validation History</CardTitle>
              <CardDescription>
                Recent validation attempts for this license
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {validationHistory.length === 0 ? (
                  <p className="text-muted-foreground">No validation history available</p>
                ) : (
                  validationHistory.map((validation) => (
                    <div
                      key={validation.id}
                      className="flex items-center justify-between p-4 rounded-lg border"
                    >
                      <div>
                        <p className="text-sm font-medium">
                          {validation.result ? "Successful" : "Failed"} Validation
                        </p>
                        <p className="text-xs text-muted-foreground">
                          {formatDateTime(validation.created_at)}
                        </p>
                        {validation.machine_fingerprint && (
                          <p className="text-xs text-muted-foreground">
                            Machine: {validation.machine_fingerprint}
                          </p>
                        )}
                      </div>
                      <Badge variant={validation.result ? "default" : "destructive"}>
                        {validation.result ? "Valid" : "Invalid"}
                      </Badge>
                    </div>
                  ))
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="machines" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Associated Machines</CardTitle>
              <CardDescription>
                Machines that have used this license
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">Machine association tracking coming soon...</p>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="metadata" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Metadata</CardTitle>
              <CardDescription>
                Custom metadata associated with this license
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Textarea
                value={JSON.stringify(formData.metadata || {}, null, 2)}
                onChange={(e) => {
                  try {
                    const metadata = JSON.parse(e.target.value)
                    setFormData({ ...formData, metadata })
                  } catch (error) {
                    // Invalid JSON, don't update
                  }
                }}
                className="font-mono"
                rows={10}
                placeholder="{}"
              />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}