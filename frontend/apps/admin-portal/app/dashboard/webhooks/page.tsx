"use client"

import { useState, useEffect } from "react"
import { Plus, Search, Download, Filter, MoreHorizontal, Webhook, Copy, Edit, Activity, AlertCircle, CheckCircle } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Badge } from "@/components/ui/badge"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { useToast } from "@/hooks/use-toast"
import { api } from "@/lib/api"
import { formatDate } from "@/lib/utils"

interface WebhookEndpoint {
  id: string
  organization_id: string
  url: string
  signing_secret?: string
  subscriptions: string[]
  enabled: boolean
  api_version?: string
  metadata?: Record<string, any>
  created_at: string
  updated_at: string
  last_triggered_at?: string
  last_error?: string
}

const availableEvents = [
  // License events
  "license.validation.succeeded",
  "license.validation.failed",
  "license.expired",
  "license.suspended",
  "license.renewed",
  "license.created",
  "license.updated",
  "license.deleted",
  
  // Machine events
  "machine.created",
  "machine.updated",
  "machine.deleted",
  "machine.heartbeat",
  "machine.checkout",
  "machine.checkin",
  
  // User events
  "user.created",
  "user.updated",
  "user.deleted",
  "user.banned",
  "user.unbanned",
  
  // Organization events
  "organization.created",
  "organization.updated",
  "organization.deleted",
  
  // Policy events
  "policy.created",
  "policy.updated",
  "policy.deleted",
  
  // Product events
  "product.created",
  "product.updated",
  "product.deleted",
]

export default function WebhooksPage() {
  const [webhooks, setWebhooks] = useState<WebhookEndpoint[]>([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState("")
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [selectedWebhook, setSelectedWebhook] = useState<WebhookEndpoint | null>(null)
  const { toast } = useToast()

  // Form state for creating/editing webhook
  const [formData, setFormData] = useState({
    url: "",
    subscriptions: [] as string[],
    enabled: true,
    api_version: "2023-01-01",
    metadata: {},
  })

  useEffect(() => {
    fetchWebhooks()
  }, [])

  const fetchWebhooks = async () => {
    try {
      setLoading(true)
      const response = await api.get("/webhook-endpoints")
      if (response.data && Array.isArray(response.data)) {
        setWebhooks(response.data)
      }
    } catch (error) {
      console.error("Failed to fetch webhooks:", error)
      toast({
        title: "Error",
        description: "Failed to fetch webhooks",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const handleCreateWebhook = async () => {
    try {
      const response = await api.post("/webhook-endpoints", formData)
      if (response.data) {
        setWebhooks([response.data, ...webhooks])
        setIsCreateDialogOpen(false)
        resetForm()
        toast({
          title: "Success",
          description: "Webhook created successfully",
        })
      }
    } catch (error) {
      console.error("Failed to create webhook:", error)
      toast({
        title: "Error",
        description: "Failed to create webhook",
        variant: "destructive",
      })
    }
  }

  const handleUpdateWebhook = async () => {
    if (!selectedWebhook) return

    try {
      const response = await api.put(`/webhook-endpoints/${selectedWebhook.id}`, formData)
      if (response.data) {
        setWebhooks(webhooks.map(w => w.id === selectedWebhook.id ? response.data : w))
        setSelectedWebhook(null)
        resetForm()
        toast({
          title: "Success",
          description: "Webhook updated successfully",
        })
      }
    } catch (error) {
      console.error("Failed to update webhook:", error)
      toast({
        title: "Error",
        description: "Failed to update webhook",
        variant: "destructive",
      })
    }
  }

  const handleDeleteWebhook = async (id: string) => {
    if (!confirm("Are you sure you want to delete this webhook?")) return

    try {
      await api.delete(`/webhook-endpoints/${id}`)
      setWebhooks(webhooks.filter(w => w.id !== id))
      toast({
        title: "Success",
        description: "Webhook deleted successfully",
      })
    } catch (error) {
      console.error("Failed to delete webhook:", error)
      toast({
        title: "Error",
        description: "Failed to delete webhook",
        variant: "destructive",
      })
    }
  }

  const handleTestWebhook = async (id: string) => {
    toast({
      title: "Info",
      description: "Test webhook functionality coming soon",
    })
  }

  const resetForm = () => {
    setFormData({
      url: "",
      subscriptions: [],
      enabled: true,
      api_version: "2023-01-01",
      metadata: {},
    })
  }

  const openEditDialog = (webhook: WebhookEndpoint) => {
    setSelectedWebhook(webhook)
    setFormData({
      url: webhook.url,
      subscriptions: webhook.subscriptions || [],
      enabled: webhook.enabled,
      api_version: webhook.api_version || "2023-01-01",
      metadata: webhook.metadata || {},
    })
    setIsCreateDialogOpen(true)
  }

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
    toast({
      description: "Copied to clipboard",
    })
  }

  const getStatusBadge = (webhook: WebhookEndpoint) => {
    if (!webhook.enabled) {
      return <Badge variant="secondary">Disabled</Badge>
    }
    
    if (webhook.last_error) {
      return <Badge variant="destructive" className="flex items-center gap-1">
        <AlertCircle className="h-3 w-3" />
        Error
      </Badge>
    }
    
    return <Badge variant="default" className="flex items-center gap-1 bg-green-500">
      <CheckCircle className="h-3 w-3" />
      Active
    </Badge>
  }

  const handleSubscriptionChange = (event: string, checked: boolean) => {
    if (checked) {
      setFormData({
        ...formData,
        subscriptions: [...formData.subscriptions, event]
      })
    } else {
      setFormData({
        ...formData,
        subscriptions: formData.subscriptions.filter(sub => sub !== event)
      })
    }
  }

  const filteredWebhooks = webhooks.filter(webhook =>
    webhook.url.toLowerCase().includes(searchQuery.toLowerCase())
  )

  if (loading) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="text-lg">Loading webhooks...</div>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">Webhooks</h1>
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button onClick={() => { setSelectedWebhook(null); resetForm(); }}>
              <Plus className="mr-2 h-4 w-4" />
              Create Webhook
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>
                {selectedWebhook ? "Edit Webhook" : "Create New Webhook"}
              </DialogTitle>
              <DialogDescription>
                {selectedWebhook 
                  ? "Update the webhook endpoint configuration below."
                  : "Create a new webhook endpoint to receive real-time notifications."
                }
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              {/* Basic Information */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Basic Information</h3>
                <div className="grid gap-4">
                  <div>
                    <Label htmlFor="url">Endpoint URL</Label>
                    <Input
                      id="url"
                      type="url"
                      value={formData.url}
                      onChange={(e) => setFormData({ ...formData, url: e.target.value })}
                      placeholder="https://your-domain.com/webhook"
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="api_version">API Version</Label>
                    <Select
                      value={formData.api_version}
                      onValueChange={(value) => setFormData({ ...formData, api_version: value })}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="2023-01-01">2023-01-01</SelectItem>
                        <SelectItem value="2022-01-01">2022-01-01</SelectItem>
                        <SelectItem value="2021-01-01">2021-01-01</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="enabled"
                      checked={formData.enabled}
                      onCheckedChange={(checked) => setFormData({ ...formData, enabled: checked as boolean })}
                    />
                    <Label htmlFor="enabled">Enable webhook</Label>
                  </div>
                </div>
              </div>

              {/* Event Subscriptions */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Event Subscriptions</h3>
                <div className="max-h-60 overflow-y-auto border rounded-md p-4">
                  <div className="grid grid-cols-1 gap-2">
                    {availableEvents.map((event) => (
                      <div key={event} className="flex items-center space-x-2">
                        <Checkbox
                          id={event}
                          checked={formData.subscriptions.includes(event)}
                          onCheckedChange={(checked) => handleSubscriptionChange(event, checked as boolean)}
                        />
                        <Label htmlFor={event} className="text-sm font-mono">
                          {event}
                        </Label>
                      </div>
                    ))}
                  </div>
                </div>
                <div className="text-sm text-muted-foreground">
                  Selected {formData.subscriptions.length} of {availableEvents.length} events
                </div>
              </div>

              {/* Metadata */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Metadata</h3>
                <Textarea
                  value={JSON.stringify(formData.metadata, null, 2)}
                  onChange={(e) => {
                    try {
                      const metadata = JSON.parse(e.target.value)
                      setFormData({ ...formData, metadata })
                    } catch (error) {
                      // Invalid JSON, don't update
                    }
                  }}
                  className="font-mono"
                  rows={4}
                  placeholder="{}"
                />
              </div>
            </div>
            <DialogFooter>
              <Button 
                type="submit" 
                onClick={selectedWebhook ? handleUpdateWebhook : handleCreateWebhook}
              >
                {selectedWebhook ? "Update Webhook" : "Create Webhook"}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      <div className="flex items-center space-x-2">
        <div className="relative flex-1">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search webhooks..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-8"
          />
        </div>
        <Button variant="outline">
          <Filter className="mr-2 h-4 w-4" />
          Filter
        </Button>
        <Button variant="outline">
          <Download className="mr-2 h-4 w-4" />
          Export
        </Button>
      </div>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>URL</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Events</TableHead>
              <TableHead>API Version</TableHead>
              <TableHead>Last Triggered</TableHead>
              <TableHead>Created</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredWebhooks.map((webhook) => (
              <TableRow key={webhook.id}>
                <TableCell>
                  <div className="flex items-center space-x-2">
                    <Webhook className="h-4 w-4 text-muted-foreground" />
                    <span className="font-medium truncate max-w-xs">{webhook.url}</span>
                  </div>
                </TableCell>
                <TableCell>
                  {getStatusBadge(webhook)}
                </TableCell>
                <TableCell>
                  <div className="flex items-center gap-2">
                    <Badge variant="outline" className="text-xs">
                      {webhook.subscriptions?.length || 0} events
                    </Badge>
                    {webhook.subscriptions && webhook.subscriptions.length > 0 && (
                      <span className="text-xs text-muted-foreground truncate max-w-xs">
                        {webhook.subscriptions.slice(0, 2).join(", ")}
                        {webhook.subscriptions.length > 2 && `, +${webhook.subscriptions.length - 2} more`}
                      </span>
                    )}
                  </div>
                </TableCell>
                <TableCell>
                  <Badge variant="outline" className="text-xs">
                    {webhook.api_version || "latest"}
                  </Badge>
                </TableCell>
                <TableCell>
                  {webhook.last_triggered_at ? (
                    formatDate(webhook.last_triggered_at)
                  ) : (
                    <span className="text-muted-foreground">Never</span>
                  )}
                </TableCell>
                <TableCell>{formatDate(webhook.created_at)}</TableCell>
                <TableCell className="text-right">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" className="h-8 w-8 p-0">
                        <span className="sr-only">Open menu</span>
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuLabel>Actions</DropdownMenuLabel>
                      <DropdownMenuItem
                        onClick={() => copyToClipboard(webhook.id)}
                      >
                        <Copy className="mr-2 h-4 w-4" />
                        Copy ID
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onClick={() => handleTestWebhook(webhook.id)}
                      >
                        <Activity className="mr-2 h-4 w-4" />
                        Test Webhook
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onClick={() => openEditDialog(webhook)}
                      >
                        <Edit className="mr-2 h-4 w-4" />
                        Edit
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem
                        onClick={() => handleDeleteWebhook(webhook.id)}
                        className="text-red-600"
                      >
                        Delete
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  )
}