"use client"

import Link from "next/link"
import { usePathname } from "next/navigation"
import { 
  LayoutDashboard,
  Users, 
  Key, 
  Monitor, 
  Building,
  Package,
  Settings,
  Shield,
  User,
  LogOut,
  Webhook,
  BarChart3,
  CreditCard,
  Lock
} from "lucide-react"
import { useSession, signOut } from "next-auth/react"
import { Button } from "@/components/ui/button"

// Local cn utility function
function cn(...classes: (string | undefined | null | false)[]) {
  return classes.filter(Boolean).join(' ')
}

const navigation = [
  {
    name: "Dashboard",
    href: "/dashboard",
    icon: LayoutDashboard,
  },
  {
    name: "Users",
    href: "/users",
    icon: Users,
  },
  {
    name: "Organizations",
    href: "/organizations", 
    icon: Building,
  },
  {
    name: "Licenses",
    href: "/dashboard/licenses",
    icon: Key,
  },
  {
    name: "Machines",
    href: "/machines",
    icon: Monitor,
  },
  {
    name: "Products",
    href: "/dashboard/products",
    icon: Package,
  },
  {
    name: "Policies",
    href: "/dashboard/policies",
    icon: Shield,
  },
  {
    name: "Analytics",
    href: "/analytics",
    icon: BarChart3,
  },
  {
    name: "Billing",
    href: "/billing",
    icon: CreditCard,
  },
  {
    name: "Webhooks",
    href: "/dashboard/webhooks",
    icon: Webhook,
  },
  {
    name: "Settings",
    href: "/settings",
    icon: Settings,
  },
]

export function AdminNav() {
  const pathname = usePathname()
  const { data: session } = useSession()

  const handleSignOut = () => {
    signOut({ callbackUrl: "/auth/signin" })
  }

  return (
    <div className="hidden md:flex md:w-64 md:flex-col">
      <div className="flex flex-col flex-grow pt-5 bg-gray-900 overflow-y-auto border-r border-gray-800">
        {/* Logo */}
        <div className="flex items-center flex-shrink-0 px-4 mb-8">
          <Link href="/dashboard" className="flex items-center space-x-3">
            <div className="relative">
              <Lock className="h-8 w-8 text-indigo-400" />
              <div className="absolute -top-1 -right-1 w-3 h-3 bg-indigo-500 rounded-full animate-pulse"></div>
            </div>
            <div>
              <h1 className="text-xl font-bold text-white">GoKeys</h1>
              <p className="text-xs text-indigo-400 font-medium">Admin Portal</p>
            </div>
          </Link>
        </div>

        {/* Navigation */}
        <nav className="mt-5 flex-1 px-2 space-y-1">
          {navigation.map((item) => {
            const isActive = pathname === item.href || 
              (item.href !== "/dashboard" && pathname.startsWith(item.href))
            
            return (
              <Link
                key={item.name}
                href={item.href}
                className={cn(
                  "group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors",
                  isActive
                    ? "bg-indigo-800 text-white"
                    : "text-gray-300 hover:bg-gray-800 hover:text-white"
                )}
              >
                <item.icon
                  className={cn(
                    "mr-3 flex-shrink-0 h-5 w-5",
                    isActive ? "text-indigo-300" : "text-gray-400 group-hover:text-gray-300"
                  )}
                />
                {item.name}
              </Link>
            )
          })}
        </nav>

        {/* User menu */}
        <div className="flex-shrink-0 border-t border-gray-800 p-4">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="h-8 w-8 rounded-full bg-indigo-600 flex items-center justify-center">
                <User className="h-4 w-4 text-white" />
              </div>
            </div>
            <div className="ml-3 flex-1">
              <p className="text-sm font-medium text-white">
                {session?.user?.name || session?.user?.email}
              </p>
              <p className="text-xs text-gray-400 capitalize">
                {session?.user?.role || 'Admin'}
              </p>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleSignOut}
              className="text-gray-400 hover:text-red-400"
            >
              <LogOut className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}