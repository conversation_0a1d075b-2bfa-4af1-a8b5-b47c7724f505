import axios from "axios"
import { getSession } from "next-auth/react"

// Create axios instance with base configuration
const api = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_URL || "http://localhost:8080/api/v1",
  timeout: 10000,
})

// Request interceptor to add auth token
api.interceptors.request.use(async (config) => {
  const session = await getSession()
  
  if (session?.accessToken) {
    config.headers.Authorization = `Bearer ${session.accessToken}`
  }
  
  return config
})

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Handle unauthorized access
      window.location.href = "/auth/signin"
    }
    return Promise.reject(error)
  }
)

export { api }